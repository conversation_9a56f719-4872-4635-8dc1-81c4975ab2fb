/* variables */
$gt-color-main ?= #6190e8
$gt-color-sub ?= #a1a1a1
$gt-color-loader ?= #999999
$gt-color-error ?= #ff3860
$gt-color-hr ?= #E9E9E9
$gt-color-comment-txt ?= #333333
$gt-color-link-active ?= #333333
$gt-color-btn ?= #ffffff
$gt-size-base ?= 16px // default font-size
$gt-size-border-radius ?= 5px
$gt-breakpoint-mobile ?= 479px
$gt-mask-z-index ?= 9999 /* functions & mixins */

clearfix()
  &:before, &:after
    content ' '
    display table

  &:after
    clear both

em($px, $base-size = $gt-size-base)
  u = unit($px)

  if (u == 'px')
    unit(
      ($px / $base-size),
      'em'
    )
  else
    unit($px, u)

mobile()
  @media (max-width $gt-breakpoint-mobile)
    {block}

/* variables - calculated */
$gt-size-loader-dot ?= em(6px)
$gt-size-loader ?= em(28px)
$gt-size-avatar ?= em(50px)
$gt-size-avatar-mobi ?= em(32px) /* styles */

// Put everything under container to avoid style conflicts
.comments-container
  .gt-container
    box-sizing border-box

    *
      box-sizing border-box

    font-size $gt-size-base

    // common
    a
      color $gt-color-main

      &:hover
        color lighten($gt-color-main, 20%)
        border-color lighten($gt-color-main, 20%)

      &.is--active
        color $gt-color-link-active
        cursor default !important

        &:hover
          color $gt-color-link-active

    .hide
      display none !important

    // icons
    .gt-svg
      display inline-block
      width em(16px)
      height em(16px)
      vertical-align sub

      svg
        width 100%
        height 100%
        fill $gt-color-main

    .gt-ico
      display inline-block

      &-text
        margin-left em(5px)

      &-github
        width 100%
        height 100%

        .gt-svg
          width 100%
          height 100%

        svg
          fill inherit

    /* loader */
    .gt-spinner
      position relative

      &::before
        content ''
        box-sizing border-box
        position absolute
        top 3px
        width em(12px)
        height em(12px)
        margin-top em(-3px)
        margin-left em(-6px)
        border-radius $redefine-border-radius-full
        border 1px solid $gt-color-btn
        border-top-color $gt-color-main
        animation gt-kf-rotate 0.6s linear infinite

    .gt-loader
      position relative
      border 1px solid $gt-color-loader
      animation ease gt-kf-rotate 1.5s infinite
      display inline-block
      font-style normal
      width $gt-size-loader
      height $gt-size-loader
      // font-size $gt-size-loader
      line-height $gt-size-loader
      border-radius $redefine-border-radius-full

      &:before
        content ''
        position absolute
        display block
        top 0
        left 50%
        margin-top -($gt-size-loader-dot / 2)
        margin-left -($gt-size-loader-dot / 2)
        width $gt-size-loader-dot
        height $gt-size-loader-dot
        background-color $gt-color-loader
        border-radius $redefine-border-radius-full

    // avatar
    .gt-avatar
      display inline-block
      width $gt-size-avatar
      height $gt-size-avatar

      +mobile()
        width $gt-size-avatar-mobi
        height $gt-size-avatar-mobi

      img
        width 100%
        height auto
        border-radius 3px

      &-github
        width: $gt-size-avatar - em(2px)
        height: $gt-size-avatar - em(2px)
        cursor pointer

        +mobile()
          width: $gt-size-avatar-mobi - em(2px)
          height: $gt-size-avatar-mobi - em(2px)

    // button
    .gt-btn
      padding em(12px) em(20px)
      display inline-block
      line-height 1
      text-decoration none
      white-space nowrap
      cursor pointer
      border 1px solid $gt-color-main
      border-radius $gt-size-border-radius
      background-color $gt-color-main
      color $gt-color-btn
      outline none
      font-size em(12px)

      &-text
        font-weight 400

      &-loading
        position relative
        margin-left em(8px)
        display inline-block
        width em(12px)
        height em(16px)
        vertical-align top

      &.is--disable
        cursor not-allowed
        opacity 0.5

      &-login
        margin-right 0

      &-preview
        background-color var(--background-color)
        color $gt-color-main

        &:hover
          background-color var(--third-background-color)

      &-public
        &:hover
          background-color lighten($gt-color-main, 20%)
          border-color lighten($gt-color-main, 20%)

  &-loadmore, .gt-error
    text-align center
    margin em(10px)
    color $gt-color-error

  /* initing */
  .gt-initing
    padding em(20px) 0
    text-align center

    &-text
      margin em(10px) auto
      font-size 92%

  /* no int */
  .gt-no-init
    padding em(20px) 0
    text-align center

  /* link */
  .gt-link
    border-bottom 1px dotted $gt-color-main

    &-counts, &-project
      text-decoration none

  /* meta */
  .gt-meta
    margin em(20px) 0
    padding em(16px) 0
    position relative
    border-bottom 1px solid $gt-color-hr
    font-size em(16px)
    z-index 10
    clearfix()

  .gt-counts
    margin 0 em(10px) 0 0
    color var(--default-text-color)

  .gt-user
    float right
    margin 0
    font-size 92%

    &-pic
      width 16px
      height 16px
      vertical-align top
      margin-right em(8px)

    &-inner
      display inline-block
      cursor pointer

      .gt-user-name
        color var(--default-text-color)

    .gt-ico
      margin 0 0 0 em(5px)

      svg
        fill var(--default-text-color)

    .is--poping
      .gt-ico
        svg
          fill $gt-color-main

  .gt-version
    color $gt-color-sub
    margin-left em(6px)

  .gt-copyright
    margin 0 em(15px) em(8px)
    border-top 1px solid var(--border-color)
    padding-top em(8px)

  /* popup */
  .gt-popup
    position absolute
    right 0
    top em(38px)
    background var(--background-color)
    display inline-block
    border 1px solid var(--border-color)
    padding em(10px) 0
    font-size em(14px)
    letter-spacing 0.5px

    .gt-action
      cursor pointer
      display block
      margin em(8px) 0
      padding 0 em(18px)
      position relative
      text-decoration none

      &.is--active
        &:before
          content ''
          width em(4px)
          height em(4px)
          background $gt-color-main
          position absolute
          left em(8px)
          top em(7px)

  /* header */
  .gt-header
    position relative
    display flex

    &-comment
      flex 1
      margin-left em(20px)

      +mobile()
        margin-left em(14px)

    &-textarea
      padding em(12px)
      display block
      box-sizing border-box
      width 100%
      min-height em(82px)
      max-height em(240px)
      border-radius $gt-size-border-radius
      border 1px solid var(--border-color)
      font-size em(14px)
      word-wrap break-word
      resize vertical
      color var(--default-text-color)
      background-color var(--fourth-text-color)
      outline none
      transition all 0.25s ease

      &:hover
        background-color var(--background-color)

    &-preview
      padding em(12px)
      border-radius $gt-size-border-radius
      border 1px solid var(--border-color)
      background-color var(--background-color)

    &-controls
      position relative
      margin em(12px) 0 0
      clearfix()

      +mobile()
        margin 0

      &-tip
        font-size em(14px)
        color $gt-color-main
        text-decoration none
        vertical-align sub

        +mobile()
          display none

      .gt-btn
        float right
        margin-left em(20px)

        +mobile()
          float none
          width 100%
          margin em(12px) 0 0

  &:after
    content ''
    position fixed
    bottom 100%
    left 0
    right 0
    top 0
    opacity 0

  &.gt-input-focused
    position relative

    &:after
      content ''
      position fixed
      bottom 0
      left 0
      right 0
      top 0
      background #000
      opacity 0.6
      transition opacity 0.3s, bottom 0s
      z-index $gt-mask-z-index

    .gt-header-comment
      z-index: $gt-mask-z-index + 1

  /* comments */
  .gt-comments
    padding-top em(20px)

    &-null
      text-align center

    &-controls
      margin em(20px) 0
      text-align center

  /* comment */
  .gt-comment
    position relative
    padding em(10px) 0
    display flex

    &-content
      flex 1
      margin-left em(20px)
      padding em(12px) em(16px)
      background-color var(--third-background-color)
      overflow auto
      transition all ease 0.25s

      +mobile()
        margin-left em(14px)
        padding em(10px) em(12px)

    &-header
      margin-bottom em(8px)
      font-size em(14px)
      position relative

    &-block-1
      float right
      height em(22px)
      width em(32px)

    &-block-2
      float right
      height em(22px)
      width em(64px)

    &-username
      font-weight 500
      color $gt-color-main
      text-decoration none

      &:hover
        text-decoration underline

    &-text
      margin-left em(8px)
      color $gt-color-sub

    &-date
      margin-left em(8px)
      color $gt-color-sub

    &-like, &-edit, &-reply
      position absolute
      height em(22px)

      &:hover
        cursor pointer

    &-like
      top 0
      right em(32px)

    &-edit, &-reply
      top 0
      right 0

    &-body
      // color $gt-color-comment-txt !important
      color var(--second-text-color) !important

      .email-hidden-toggle a
        display inline-block
        height 12px
        padding 0 9px
        font-size 12px
        font-weight 600
        line-height 6px
        color #444d56
        text-decoration none
        vertical-align middle
        background #dfe2e5
        border-radius 1px

        &:hover
          background-color #c6cbd1

      .email-hidden-reply
        display none
        white-space pre-wrap

        .email-signature-reply
          padding 0 15px
          margin 15px 0
          color #586069
          border-left 4px solid #dfe2e5

      .email-hidden-reply.expanded
        display block

    &-admin
      .gt-comment-content
        background-color var(--fourth-text-color)

  @keyframes gt-kf-rotate
    0%
      transform rotate(0)

    100%
      transform rotate(360deg)
