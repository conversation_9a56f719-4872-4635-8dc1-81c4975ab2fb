{"version": 3, "file": "hbe.js", "names": ["main", "initTOC", "initHBE", "cryptoObj", "window", "crypto", "msCrypto", "storage", "localStorage", "storageName", "location", "pathname", "keySalt", "textToArray", "ivSalt", "mainElement", "document", "getElementById", "wrongPassMessage", "dataset", "wrongHashMessage", "dataElement", "getElementsByTagName", "encryptedData", "innerText", "HmacDigist", "hexToArray", "s", "Uint8Array", "match", "map", "h", "parseInt", "i", "length", "n", "ba", "Array", "j", "c", "codePointAt", "arrayBufferToHex", "arrayBuffer", "byteLength", "TypeError", "value", "view", "result", "toString", "async", "convertHTMLToElement", "content", "out", "createElement", "innerHTML", "querySelectorAll", "for<PERSON>ach", "elem", "replaceWith", "getExecutableScript", "oldElem", "att", "decrypt", "decrypt<PERSON>ey", "iv", "hmacKey", "typedArray", "subtle", "name", "buffer", "then", "decoded", "TextDecoder", "decode", "startsWith", "hideButton", "textContent", "type", "classList", "add", "addEventListener", "removeItem", "reload", "style", "display", "append<PERSON><PERSON><PERSON>", "getAttribute", "src", "refresh", "event", "Event", "dispatchEvent", "verify<PERSON>ontent", "key", "encoded", "TextEncoder", "encode", "signature", "verify", "hash", "console", "log", "alert", "catch", "e", "hbe<PERSON><PERSON><PERSON>", "oldStorageData", "JSON", "parse", "getItem", "sIv", "sDk", "dk", "sHmk", "hmk", "importKey", "dkCK", "hmkCK", "isComposing", "password", "keyMaterial", "getKeyMaterial", "encoder", "getHmacKey", "<PERSON><PERSON><PERSON>", "salt", "iterations", "getDecryptKey", "getIv", "deriveBits", "exportKey", "newStorageData", "setItem", "stringify"], "sources": ["0"], "mappings": "eAASA,MAAY,+BACZC,MAAe,2BAEjB,SAASC,UACd,MAAMC,EAAYC,OAAOC,QAAUD,OAAOE,SACpCC,EAAUH,OAAOI,aAEjBC,EAAc,sBAAwBL,OAAOM,SAASC,SACtDC,EAAUC,YAAY,wBACtBC,EAASD,YAAY,oBAMrBE,EAAcC,SAASC,eAAe,qBACtCC,EAAmBH,EAAYI,QAAa,IAC5CC,EAAmBL,EAAYI,QAAa,IAC5CE,EAAcN,EAAYO,qBAAqB,UAAmB,QAClEC,EAAgBF,EAAYG,UAC5BC,EAAaJ,EAAYF,QAAoB,WAEnD,SAASO,WAAWC,GAClB,OAAO,IAAIC,WACTD,EAAEE,MAAM,gBAAgBC,KAAKC,GACpBC,SAASD,EAAG,MAGzB,CAEA,SAASlB,YAAYc,GAKnB,IAJA,IAAIM,EAAIN,EAAEO,OACNC,EAAI,EACJC,EAAK,IAAIC,MAEJC,EAAI,EAAGA,EAAIL,GAAK,CACvB,IAAIM,EAAIZ,EAAEa,YAAYF,GAClBC,EAAI,KACNH,EAAGD,KAAOI,EACVD,KACSC,EAAI,KAAOA,EAAI,MACxBH,EAAGD,KAAQI,GAAK,EAAK,IACrBH,EAAGD,KAAY,GAAJI,EAAU,IACrBD,KACSC,EAAI,MAAQA,EAAI,OACzBH,EAAGD,KAAQI,GAAK,GAAM,IACtBH,EAAGD,KAASI,GAAK,EAAK,GAAM,IAC5BH,EAAGD,KAAY,GAAJI,EAAU,IACrBD,MAEAF,EAAGD,KAAQI,GAAK,GAAM,IACtBH,EAAGD,KAASI,GAAK,GAAM,GAAM,IAC7BH,EAAGD,KAASI,GAAK,EAAK,GAAM,IAC5BH,EAAGD,KAAY,GAAJI,EAAU,IACrBD,GAAK,EAET,CACA,OAAO,IAAIV,WAAWQ,EACxB,CAEA,SAASK,iBAAiBC,GACxB,GACyB,iBAAhBA,GACS,OAAhBA,GACkC,iBAA3BA,EAAYC,WAEnB,MAAM,IAAIC,UAAU,uCAOtB,IAJA,IAEIC,EAFAC,EAAO,IAAIlB,WAAWc,GACtBK,EAAS,GAGJd,EAAI,EAAGA,EAAIa,EAAKZ,OAAQD,IAE/Bc,GAA2B,KAD3BF,EAAQC,EAAKb,GAAGe,SAAS,KACTd,OAAe,IAAMW,EAAQA,EAG/C,OAAOE,CACT,CAmBAE,eAAeC,qBAAqBC,GAClC,IAAIC,EAAMpC,SAASqC,cAAc,OAMjC,OALAD,EAAIE,UAAYH,EAChBC,EAAIG,iBAAiB,UAAUC,SAAQP,MAAOQ,IAC5CA,EAAKC,kBArBTT,eAAeU,oBAAoBC,GACjC,IAAIR,EAAMpC,SAASqC,cAAc,UAajC,MAZgB,CACd,OACA,OACA,MACA,cACA,QACA,kBAEMG,SAASK,IACXD,EAAQC,KAAMT,EAAIS,GAAOD,EAAQC,GAAI,IAGpCT,CACT,CAM2BO,CAAoBF,GAAM,IAG5CL,CACT,CAwFAH,eAAea,QAAQC,EAAYC,EAAIC,GACrC,IAAIC,EAAaxC,WAAWH,GA2D5B,aAzDqBpB,EAAUgE,OAC5BL,QACC,CACEM,KAAM,UACNJ,GAAIA,GAEND,EACAG,EAAWG,QAEZC,MAAKrB,MAAOF,IACX,MACMwB,GADU,IAAIC,aACIC,OAAO1B,GAG/B,IAAKwB,EAAQG,WAtMC,6BAuMZ,KAAM,sDAGR,MAAMC,EAAa3D,SAASqC,cAAc,UAC1CsB,EAAWC,YAAc,gBACzBD,EAAWE,KAAO,SAClBF,EAAWG,UAAUC,IAAI,cACzBJ,EAAWK,iBAAiB,SAAS,KACnC5E,OAAOI,aAAayE,WAAWxE,GAC/BL,OAAOM,SAASwE,QAAQ,IAG1BlE,SAASC,eAAe,qBAAqBkE,MAAMC,QAAU,SAC7DpE,SAASC,eAAe,qBAAqBqC,UAAY,GACzDtC,SACGC,eAAe,qBACfoE,kBAAkBnC,qBAAqBqB,IAC1CvD,SAASC,eAAe,qBAAqBoE,YAAYV,GAGzD3D,SAASuC,iBAAiB,OAAOC,SAASC,IACpCA,EAAK6B,aAAa,cAAgB7B,EAAK8B,MACzC9B,EAAK8B,IAAM9B,EAAK6B,aAAa,YAC/B,IAIFtF,EAAKwF,UACLvF,IAGA,IAAIwF,EAAQ,IAAIC,MAAM,qBAGtB,OAFAtF,OAAOuF,cAAcF,SAzE3BxC,eAAe2C,cAAcC,EAAK1C,GAChC,MACM2C,GADU,IAAIC,aACIC,OAAO7C,GAE/B,IAAI8C,EAAYvE,WAAWD,GAE3B,MAAMsB,QAAe5C,EAAUgE,OAAO+B,OACpC,CACE9B,KAAM,OACN+B,KAAM,WAERN,EACAI,EACAH,GAOF,OALAM,QAAQC,IAAI,wBAAwBtD,KAC/BA,IACHuD,MAAMlF,GACNgF,QAAQC,IAAI,GAAGjF,UAA0B6E,EAAW,uBAE/ClD,CACT,CAsDmB6C,CAAc3B,EAASM,EAAQ,IAE7CgC,OAAOC,IACNF,MAAMpF,GACNkF,QAAQC,IAAIG,IACL,IAIb,EAEA,SAASC,YACP,MAAMC,EAAiBC,KAAKC,MAAMrG,EAAQsG,QAAQpG,IAElD,GAAIiG,EAAgB,CAClBN,QAAQC,IACN,kCAAkC5F,OAClCiG,GAGF,MAAMI,EAAMpF,WAAWgF,EAAe1C,IAAIK,OACpC0C,EAAML,EAAeM,GACrBC,EAAOP,EAAeQ,IAE5B/G,EAAUgE,OACPgD,UACC,MACAJ,EACA,CACE3C,KAAM,UACNlC,OAAQ,MAEV,EACA,CAAC,YAEFoC,MAAM8C,IACLjH,EAAUgE,OACPgD,UACC,MACAF,EACA,CACE7C,KAAM,OACN+B,KAAM,UACNjE,OAAQ,MAEV,EACA,CAAC,WAEFoC,MAAM+C,IACLvD,QAAQsD,EAAMN,EAAKO,GAAO/C,MAAMvB,IACzBA,GACHxC,EAAQ0E,WAAWxE,EACrB,GACA,GACF,GAEV,CAEAM,EAAYiE,iBAAiB,WAAW/B,MAAOwC,IAC7C,GAAIA,EAAM6B,aAA6B,UAAd7B,EAAMI,IAAiB,CAC9C,MAAM0B,EAAWvG,SAASC,eAAe,WAAW4B,MAC9C2E,QAvMZ,SAASC,eAAeF,GACtB,IAAIG,EAAU,IAAI3B,YAClB,OAAO5F,EAAUgE,OAAOgD,UACtB,MACAO,EAAQ1B,OAAOuB,GACf,CACEnD,KAAM,WAER,EACA,CAAC,YAAa,cAElB,CA4LgCqD,CAAeF,GACnCtD,QA3LZ,SAAS0D,WAAWH,GAClB,OAAOrH,EAAUgE,OAAOyD,UACtB,CACExD,KAAM,SACN+B,KAAM,UACN0B,KAAMjH,EAAQyD,OACdyD,WAAY,MAEdN,EACA,CACEpD,KAAM,OACN+B,KAAM,UACNjE,OAAQ,MAEV,EACA,CAAC,UAEL,CA0K4ByF,CAAWH,GAC3BzD,QAzKZ,SAASgE,cAAcP,GACrB,OAAOrH,EAAUgE,OAAOyD,UACtB,CACExD,KAAM,SACN+B,KAAM,UACN0B,KAAMjH,EAAQyD,OACdyD,WAAY,MAEdN,EACA,CACEpD,KAAM,UACNlC,OAAQ,MAEV,EACA,CAAC,WAEL,CAyJ+B6F,CAAcP,GACjCxD,QAxJZ,SAASgE,MAAMR,GACb,OAAOrH,EAAUgE,OAAO8D,WACtB,CACE7D,KAAM,SACN+B,KAAM,UACN0B,KAAM/G,EAAOuD,OACbyD,WAAY,KAEdN,EACA,IAEJ,CA6IuBQ,CAAMR,GAEvB1D,QAAQC,EAAYC,EAAIC,GAASK,MAAMvB,IACrCqD,QAAQC,IAAI,mBAAmBtD,KAC3BA,GACF5C,EAAUgE,OAAO+D,UAAU,MAAOnE,GAAYO,MAAM0C,IAClD7G,EAAUgE,OAAO+D,UAAU,MAAOjE,GAASK,MAAM4C,IAC/C,MAAMiB,EAAiB,CACrBnB,GAAIA,EACJhD,GAAIvB,iBAAiBuB,GACrBkD,IAAKA,GAEP3G,EAAQ6H,QAAQ3H,EAAakG,KAAK0B,UAAUF,GAAgB,GAC5D,GAEN,GAEJ,IAEJ,CAEA1B,EACF", "ignoreList": []}