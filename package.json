{"name": "hexo-site", "version": "0.0.0", "private": true, "scripts": {"build": "hexo generate", "clean": "hexo clean", "deploy": "hexo deploy", "server": "hexo server"}, "hexo": {"version": "7.3.0"}, "dependencies": {"hexo": "^7.3.0", "hexo-all-minifier": "^0.5.2", "hexo-asset-link": "^2.2.3", "hexo-deployer-git": "^4.0.0", "hexo-filter-mathjax": "^0.9.0", "hexo-filter-mermaid-diagrams": "^1.0.5", "hexo-generator-archive": "^2.0.0", "hexo-generator-category": "^2.0.0", "hexo-generator-feed": "^3.0.0", "hexo-generator-index": "^4.0.0", "hexo-generator-searchdb": "^1.5.0", "hexo-generator-tag": "^2.0.0", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^7.0.0", "hexo-renderer-stylus": "^3.0.1", "hexo-server": "^3.0.0", "hexo-wordcount": "^6.0.1", "image-size": "^2.0.2", "js-yaml": "^4.1.0", "node-fetch": "^3.3.2", "nodejieba": "^3.4.4"}}