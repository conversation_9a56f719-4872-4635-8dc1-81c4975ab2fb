{"version": 3, "file": "toc.js", "names": ["initTocToggle", "main", "initTOC", "utils", "navItems", "document", "querySelectorAll", "updateActiveTOCLink", "Array", "isArray", "sections", "index", "findIndex", "element", "getBoundingClientRect", "top", "length", "this", "activateTOCLink", "registerTOCScroll", "map", "getElementById", "decodeURI", "getAttribute", "replace", "target", "classList", "contains", "for<PERSON>ach", "remove", "add", "tocElement", "querySelector", "tocTop", "scrollTopOffset", "offsetHeight", "window", "innerHeight", "distanceToCenter", "Math", "max", "documentElement", "clientHeight", "scrollTop", "scrollTo", "behavior", "showTOCAside", "openHandle", "styleStatus", "getStyleStatus", "key", "hasOwnProperty", "pageAsideHandleOfTOC", "initOpenKey", "theme", "articles", "toc", "elem", "swup", "hooks", "on", "e", "addEventListener"], "sources": ["0"], "mappings": "wBAESA,MAAqB,uCACrBC,MAAY,oBACd,SAASC,UACd,MAAMC,EAAQ,CACZC,SAAUC,SAASC,iBAAiB,+BAEpC,mBAAAC,GACE,IAAKC,MAAMC,QAAQN,EAAMO,UAAW,OACpC,IAAIC,EAAQR,EAAMO,SAASE,WAAWC,GAC7BA,GAAWA,EAAQC,wBAAwBC,IAAM,IAAM,KAEjD,IAAXJ,EACFA,EAAQR,EAAMO,SAASM,OAAS,EACvBL,EAAQ,GACjBA,IAEFM,KAAKC,gBAAgBP,EACvB,EAEA,iBAAAQ,GACEhB,EAAMO,SAAW,IACZL,SAASC,iBAAiB,4BAC7Bc,KAAKP,GACUR,SAASgB,eACtBC,UAAUT,EAAQU,aAAa,SAASC,QAAQ,IAAK,MAI3D,EAEA,eAAAN,CAAgBP,GACd,MAAMc,EAASpB,SAASC,iBAAiB,2BACvCK,GAGF,IAAKc,GAAUA,EAAOC,UAAUC,SAAS,kBACvC,OAGFtB,SAASC,iBAAiB,qBAAqBsB,SAASf,IACtDA,EAAQa,UAAUG,OAAO,SAAU,iBAAiB,IAEtDJ,EAAOC,UAAUI,IAAI,SAAU,kBAE/B,MAAMC,EAAa1B,SAAS2B,cAAc,0BACpCC,EAASF,EAAWjB,wBAAwBC,IAC5CmB,EACJH,EAAWI,aAAeC,OAAOC,aAC5BN,EAAWI,aAAeC,OAAOC,aAAe,EACjD,EAMAC,EALYb,EAAOX,wBAAwBC,IAAMkB,EAChCM,KAAKC,IAC1BnC,SAASoC,gBAAgBC,aACzBN,OAAOC,aAAe,GAIL,EACjBZ,EAAOU,aAAe,EACtBD,EACIS,EAAYZ,EAAWY,UAAYL,EAEzCP,EAAWa,SAAS,CAClB7B,IAAK4B,EACLE,SAAU,UAEd,EAEA,YAAAC,GACE,MAAMC,WAAa,KACjB,MAAMC,EAAc/C,EAAKgD,iBACnBC,EAAM,kBACRF,GAAeA,EAAYG,eAAeD,GAC5ClD,IAAgBoD,qBAAqBJ,EAAYE,IAEjDlD,IAAgBoD,sBAAqB,EACvC,EAGIC,EAAc,YAEhBC,MAAMC,SAASC,IAAIL,eAAeE,GACpCC,MAAMC,SAASC,IAAIH,GACfN,aACA/C,IAAgBoD,sBAAqB,GAEzCL,YAEJ,GAcF,OAXI5C,EAAMC,SAASY,OAAS,GAC1Bb,EAAM2C,eACN3C,EAAMgB,qBAENd,SACGC,iBAAiB,uCACjBsB,SAAS6B,IACRA,EAAK5B,QAAQ,IAIZ1B,CACT,CAGA,IACEuD,KAAKC,MAAMC,GAAG,aAAa,KACzB1D,SAAS,GAEb,CAAE,MAAO2D,GAAI,CAEbxD,SAASyD,iBAAiB,mBAAoB5D", "ignoreList": []}