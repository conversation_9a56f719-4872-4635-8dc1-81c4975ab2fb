{"version": 3, "file": "index.umd.js", "sources": ["../node_modules/@swup/plugin/dist/index.modern.js", "../src/util.ts", "../src/index.ts", "../src/queue.ts", "../src/observer.ts"], "sourcesContent": ["function r(){return r=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},r.apply(this,arguments)}const n=r=>String(r).split(\".\").map(r=>String(parseInt(r||\"0\",10))).concat([\"0\",\"0\"]).slice(0,3).join(\".\");class e{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(r=>r()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error(\"You must define a name of plugin when creating a class.\")}_afterUnmount(){}_checkRequirements(){return\"object\"!=typeof this.requires||Object.entries(this.requires).forEach(([r,e])=>{if(!function(r,e,t){const s=function(r,n){var e;if(\"swup\"===r)return null!=(e=n.version)?e:\"\";{var t;const e=n.findPlugin(r);return null!=(t=null==e?void 0:e.version)?t:\"\"}}(r,t);return!!s&&((r,e)=>e.every(e=>{const[,t,s]=e.match(/^([\\D]+)?(.*)$/)||[];var o,i;return((r,n)=>{const e={\"\":r=>0===r,\">\":r=>r>0,\">=\":r=>r>=0,\"<\":r=>r<0,\"<=\":r=>r<=0};return(e[n]||e[\"\"])(r)})((i=s,o=n(o=r),i=n(i),o.localeCompare(i,void 0,{numeric:!0})),t||\">=\")}))(s,e)}(r,e=Array.isArray(e)?e:[e],this.swup)){const n=`${r} ${e.join(\", \")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${n}`)}}),!0}on(r,n,e={}){var t;n=!(t=n).name.startsWith(\"bound \")||t.hasOwnProperty(\"prototype\")?n.bind(this):n;const s=this.swup.hooks.on(r,n,e);return this.handlersToUnregister.push(s),s}once(n,e,t={}){return this.on(n,e,r({},t,{once:!0}))}before(n,e,t={}){return this.on(n,e,r({},t,{before:!0}))}replace(n,e,t={}){return this.on(n,e,r({},t,{replace:!0}))}off(r,n){return this.swup.hooks.off(r,n)}}export{e as default};\n//# sourceMappingURL=index.modern.js.map\n", "/**\n * Check if the user's connection is configured and fast enough\n * to preload data in the background.\n */\nexport function networkSupportsPreloading(): boolean {\n\tif (navigator.connection) {\n\t\tif (navigator.connection.saveData) {\n\t\t\treturn false;\n\t\t}\n\t\tif (navigator.connection.effectiveType?.endsWith('2g')) {\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn true;\n}\n\n/**\n * Does this device support true hover/pointer interactions?\n */\nexport function deviceSupportsHover() {\n\treturn window.matchMedia('(hover: hover)').matches;\n}\n\n/**\n * Safe requestIdleCallback function that falls back to setTimeout\n */\nexport const whenIdle = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));\n", "import Plugin from '@swup/plugin';\nimport { getCurrentUrl, Location } from 'swup';\nimport type {\n\tDelegateEvent,\n\tDelegateEventHandler,\n\tDelegateEventUnsubscribe,\n\tPageData,\n\tHookDefaultHandler\n} from 'swup';\nimport { deviceSupportsHover, networkSupportsPreloading, whenIdle } from './util.js';\nimport createQueue, { Queue } from './queue.js';\nimport createObserver, { Observer } from './observer.js';\n\ndeclare module 'swup' {\n\texport interface Swup {\n\t\t/**\n\t\t * Preload links by passing in either:\n\t\t * - a URL or an array of URLs\n\t\t * - a link element or an array of link elements\n\t\t */\n\t\tpreload?: (\n\t\t\tinput: string | string[] | HTMLAnchorElement | HTMLAnchorElement[]\n\t\t) => Promise<PageData | (PageData | void)[] | void>;\n\t\t/**\n\t\t * Preload any links on the current page manually marked for preloading.\n\t\t */\n\t\tpreloadLinks?: () => void;\n\t}\n\texport interface HookDefinitions {\n\t\t'link:hover': { el: HTMLAnchorElement; event: DelegateEvent };\n\t\t'page:preload': { page: PageData };\n\t}\n}\n\ntype VisibleLinkPreloadOptions = {\n\t/** Enable preloading of links entering the viewport */\n\tenabled: boolean;\n\t/** How much area of a link must be visible to preload it: 0 to 1.0 */\n\tthreshold: number;\n\t/** How long a link must be visible to preload it, in milliseconds */\n\tdelay: number;\n\t/** Containers to look for links in */\n\tcontainers: string[];\n\t/** Callback for opting out selected elements from preloading */\n\tignore: (el: HTMLAnchorElement) => boolean;\n};\n\nexport type PluginOptions = {\n\t/** The *concurrency limit* for simultaneous requests when preloading. */\n\tthrottle: number;\n\t/** Preload the initial page to allow instant back-button navigation. */\n\tpreloadInitialPage: boolean;\n\t/** Preload links when they are hovered, touched or focused. */\n\tpreloadHoveredLinks: boolean;\n\t/** Preload links when they enter the viewport. */\n\tpreloadVisibleLinks: VisibleLinkPreloadOptions;\n};\n\nexport type PluginInitOptions = Omit<PluginOptions, 'preloadVisibleLinks'> & {\n\t/** Preload links when they enter the viewport. */\n\tpreloadVisibleLinks: boolean | Partial<VisibleLinkPreloadOptions>;\n};\n\ntype PreloadOptions = {\n\t/** Priority of this preload: `true` for high, `false` for low. */\n\tpriority?: boolean;\n};\n\nexport default class SwupPreloadPlugin extends Plugin {\n\tname = 'SwupPreloadPlugin';\n\n\trequires = { swup: '>=4' };\n\n\tdefaults: PluginOptions = {\n\t\tthrottle: 5,\n\t\tpreloadInitialPage: true,\n\t\tpreloadHoveredLinks: true,\n\t\tpreloadVisibleLinks: {\n\t\t\tenabled: false,\n\t\t\tthreshold: 0.2,\n\t\t\tdelay: 500,\n\t\t\tcontainers: ['body'],\n\t\t\tignore: () => false\n\t\t}\n\t};\n\n\toptions: PluginOptions;\n\n\tprotected queue: Queue;\n\tprotected preloadObserver?: Observer;\n\tprotected preloadPromises = new Map<string, Promise<PageData | void>>();\n\n\tprotected mouseEnterDelegate?: DelegateEventUnsubscribe;\n\tprotected touchStartDelegate?: DelegateEventUnsubscribe;\n\tprotected focusDelegate?: DelegateEventUnsubscribe;\n\n\tconstructor(options: Partial<PluginInitOptions> = {}) {\n\t\tsuper();\n\n\t\t// Set all options except `preloadVisibleLinks` which is sanitized below\n\t\tconst { preloadVisibleLinks, ...otherOptions } = options;\n\t\tthis.options = { ...this.defaults, ...otherOptions };\n\n\t\t// Sanitize/merge `preloadVisibleLinks`` option\n\t\tif (typeof preloadVisibleLinks === 'object') {\n\t\t\tthis.options.preloadVisibleLinks = {\n\t\t\t\t...this.options.preloadVisibleLinks,\n\t\t\t\tenabled: true,\n\t\t\t\t...preloadVisibleLinks\n\t\t\t};\n\t\t} else {\n\t\t\tthis.options.preloadVisibleLinks.enabled = Boolean(preloadVisibleLinks);\n\t\t}\n\n\t\t// Bind public methods\n\t\tthis.preload = this.preload.bind(this);\n\n\t\t// Create global priority queue\n\t\tthis.queue = createQueue(this.options.throttle);\n\t}\n\n\tmount() {\n\t\tconst swup = this.swup;\n\n\t\tif (!swup.options.cache) {\n\t\t\tconsole.warn('SwupPreloadPlugin: swup cache needs to be enabled for preloading');\n\t\t\treturn;\n\t\t}\n\n\t\tswup.hooks.create('page:preload');\n\t\tswup.hooks.create('link:hover');\n\n\t\t// @ts-ignore: non-matching signatures (TODO: fix properly)\n\t\tswup.preload = this.preload;\n\t\tswup.preloadLinks = this.preloadLinks;\n\n\t\t// Register handlers for preloading on attention: mouseenter, touchstart, focus\n\t\tconst { linkSelector: selector } = swup.options;\n\t\tconst opts = { passive: true, capture: true };\n\t\tthis.mouseEnterDelegate = swup.delegateEvent(\n\t\t\tselector,\n\t\t\t'mouseenter',\n\t\t\tthis.onMouseEnter,\n\t\t\topts\n\t\t);\n\t\tthis.touchStartDelegate = swup.delegateEvent(\n\t\t\tselector,\n\t\t\t'touchstart',\n\t\t\tthis.onTouchStart,\n\t\t\topts\n\t\t);\n\t\tthis.focusDelegate = swup.delegateEvent(selector, 'focus', this.onFocus, opts);\n\n\t\t// Inject custom promise whenever a page is loaded\n\t\tthis.replace('page:load', this.onPageLoad);\n\n\t\t// Preload links with [data-swup-preload] attr\n\t\tif (this.options.preloadHoveredLinks) {\n\t\t\tthis.preloadLinks();\n\t\t\tthis.on('page:view', () => this.preloadLinks());\n\t\t}\n\n\t\t// Preload visible links in viewport\n\t\tif (this.options.preloadVisibleLinks.enabled) {\n\t\t\tthis.preloadVisibleLinks();\n\t\t\tthis.on('page:view', () => this.preloadVisibleLinks());\n\t\t}\n\n\t\t// Cache unmodified DOM of initial/current page\n\t\tif (this.options.preloadInitialPage) {\n\t\t\tthis.preload(getCurrentUrl());\n\t\t}\n\t}\n\n\tunmount() {\n\t\tthis.swup.preload = undefined;\n\t\tthis.swup.preloadLinks = undefined;\n\n\t\tthis.preloadPromises.clear();\n\n\t\tthis.mouseEnterDelegate?.destroy();\n\t\tthis.touchStartDelegate?.destroy();\n\t\tthis.focusDelegate?.destroy();\n\n\t\tthis.stopPreloadingVisibleLinks();\n\t}\n\n\t/**\n\t * Before core page load: return existing preload promise if available.\n\t */\n\tprotected onPageLoad: HookDefaultHandler<'page:load'> = (visit, args, defaultHandler) => {\n\t\tconst { url } = visit.to;\n\t\tif (url && this.preloadPromises.has(url)) {\n\t\t\treturn this.preloadPromises.get(url) as Promise<PageData>;\n\t\t}\n\t\treturn defaultHandler!(visit, args);\n\t};\n\n\t/**\n\t * When hovering over a link: preload the linked page with high priority.\n\t */\n\tprotected onMouseEnter: DelegateEventHandler = async (event) => {\n\t\t// Make sure mouseenter is only fired once even on links with nested html\n\t\tif (event.target !== event.delegateTarget) return;\n\n\t\t// Return early on devices that don't support hover\n\t\tif (!deviceSupportsHover()) return;\n\n\t\tconst el = event.delegateTarget;\n\t\tif (!(el instanceof HTMLAnchorElement)) return;\n\n\t\tthis.swup.hooks.callSync('link:hover', { el, event });\n\t\tthis.preload(el, { priority: true });\n\t};\n\n\t/**\n\t * When touching a link: preload the linked page with high priority.\n\t */\n\tprotected onTouchStart: DelegateEventHandler = (event) => {\n\t\t// Return early on devices that support hover\n\t\tif (deviceSupportsHover()) return;\n\n\t\tconst el = event.delegateTarget;\n\t\tif (!(el instanceof HTMLAnchorElement)) return;\n\n\t\tthis.preload(el, { priority: true });\n\t};\n\n\t/**\n\t * When focussing a link: preload the linked page with high priority.\n\t */\n\tprotected onFocus: DelegateEventHandler = (event) => {\n\t\tconst el = event.delegateTarget;\n\t\tif (!(el instanceof HTMLAnchorElement)) return;\n\n\t\tthis.preload(el, { priority: true });\n\t};\n\n\t/**\n\t * Preload links.\n\t *\n\t * The method accepts either:\n\t * - a URL or an array of URLs\n\t * - a link element or an array of link elements\n\t *\n\t * It returns either:\n\t * - a Promise resolving to the page data, if requesting a single page\n\t * - a Promise resolving to an array of page data, if requesting multiple pages\n\t */\n\tasync preload(url: string, options?: PreloadOptions): Promise<PageData | void>;\n\tasync preload(urls: string[], options?: PreloadOptions): Promise<(PageData | void)[]>;\n\tasync preload(el: HTMLAnchorElement, options?: PreloadOptions): Promise<PageData | void>;\n\tasync preload(els: HTMLAnchorElement[], options?: PreloadOptions): Promise<(PageData | void)[]>;\n\tasync preload(\n\t\tinput: string | HTMLAnchorElement,\n\t\toptions?: PreloadOptions\n\t): Promise<PageData | void>;\n\tasync preload(\n\t\tinput: string | string[] | HTMLAnchorElement | HTMLAnchorElement[],\n\t\toptions: PreloadOptions = {}\n\t): Promise<PageData | (PageData | void)[] | void> {\n\t\tlet url: string;\n\t\tlet el: HTMLAnchorElement | undefined;\n\t\tconst priority = options.priority ?? false;\n\n\t\t// Allow passing in array of urls or elements\n\t\tif (Array.isArray(input)) {\n\t\t\treturn Promise.all(input.map((link) => this.preload(link)));\n\t\t}\n\t\t// Allow passing in an anchor element\n\t\telse if (input instanceof HTMLAnchorElement) {\n\t\t\tel = input;\n\t\t\t({ url } = Location.fromElement(input));\n\t\t}\n\t\t// Allow passing in a url\n\t\telse if (typeof input === 'string') {\n\t\t\turl = input;\n\t\t}\n\t\t// Disallow other types\n\t\telse {\n\t\t\treturn;\n\t\t}\n\n\t\t// Already preloading? Return existing promise\n\t\tif (this.preloadPromises.has(url)) {\n\t\t\treturn this.preloadPromises.get(url);\n\t\t}\n\n\t\t// Should we preload?\n\t\tif (!this.shouldPreload(url, { el })) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Queue the preload with either low or high priority\n\t\t// The actual preload will happen when a spot in the queue is available\n\t\tconst queuedPromise = new Promise<PageData | void>((resolve) => {\n\t\t\tthis.queue.add(() => {\n\t\t\t\tthis.performPreload(url)\n\t\t\t\t\t.catch(() => {})\n\t\t\t\t\t.then((page) => resolve(page))\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tthis.queue.next();\n\t\t\t\t\t\tthis.preloadPromises.delete(url);\n\t\t\t\t\t});\n\t\t\t}, priority);\n\t\t});\n\n\t\tthis.preloadPromises.set(url, queuedPromise);\n\n\t\treturn queuedPromise;\n\t}\n\n\t/**\n\t * Preload any links on the current page manually marked for preloading.\n\t *\n\t * Links are marked for preloading by:\n\t * - adding a `data-swup-preload` attribute to the link itself\n\t * - adding a `data-swup-preload-all` attribute to a container of multiple links\n\t */\n\tpreloadLinks(): void {\n\t\twhenIdle(() => {\n\t\t\tconst selector = 'a[data-swup-preload], [data-swup-preload-all] a';\n\t\t\tconst links = Array.from(document.querySelectorAll<HTMLAnchorElement>(selector));\n\t\t\tlinks.forEach((el) => this.preload(el));\n\t\t});\n\t}\n\n\t/**\n\t * Start observing links in the viewport for preloading.\n\t * Calling this repeatedly re-checks for links after DOM updates.\n\t */\n\tprotected preloadVisibleLinks(): void {\n\t\t// Scan DOM for new links on repeated calls\n\t\tif (this.preloadObserver) {\n\t\t\tthis.preloadObserver.update();\n\t\t\treturn;\n\t\t}\n\n\t\tconst { threshold, delay, containers } = this.options.preloadVisibleLinks;\n\t\tconst callback = (el: HTMLAnchorElement) => this.preload(el);\n\t\tconst filter = (el: HTMLAnchorElement) => {\n\t\t\t/** First, run the custom callback */\n\t\t\tif (this.options.preloadVisibleLinks.ignore(el)) return false;\n\t\t\t/** Second, run all default checks */\n\t\t\treturn this.shouldPreload(el.href, { el });\n\t\t};\n\t\tthis.preloadObserver = createObserver({ threshold, delay, containers, callback, filter });\n\t\tthis.preloadObserver.start();\n\t}\n\n\t/**\n\t * Stop observing links in the viewport for preloading.\n\t */\n\tprotected stopPreloadingVisibleLinks(): void {\n\t\tif (this.preloadObserver) {\n\t\t\tthis.preloadObserver.stop();\n\t\t}\n\t}\n\n\t/**\n\t * Check whether a URL and/or element should trigger a preload.\n\t */\n\tprotected shouldPreload(location: string, { el }: { el?: HTMLAnchorElement } = {}): boolean {\n\t\tconst { url, href } = Location.fromUrl(location);\n\n\t\t// Network too slow?\n\t\tif (!networkSupportsPreloading()) return false;\n\t\t// Already in cache?\n\t\tif (this.swup.cache.has(url)) return false;\n\t\t// Already preloading?\n\t\tif (this.preloadPromises.has(url)) return false;\n\t\t// Should be ignored anyway?\n\t\tif (this.swup.shouldIgnoreVisit(href, { el })) return false;\n\t\t// Special condition for links: points to current page?\n\t\tif (el && this.swup.resolveUrl(url) === this.swup.resolveUrl(getCurrentUrl())) return false;\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Perform the actual preload fetch and trigger the preload hook.\n\t */\n\tprotected async performPreload(url: string): Promise<PageData> {\n\t\tconst page = await this.swup.fetchPage(url);\n\t\tawait this.swup.hooks.call('page:preload', { page });\n\t\treturn page;\n\t}\n}\n", "type QueueFunction = {\n\t(): void;\n\t__queued?: boolean;\n};\n\nexport type Queue = {\n\tadd: (fn: QueueFunction, highPriority?: boolean) => void;\n\tnext: () => void;\n};\n\nexport default function createQueue(limit: number = 1): Queue {\n\tconst qlow: QueueFunction[] = [];\n\tconst qhigh: QueueFunction[] = [];\n\tlet total = 0;\n\tlet running = 0;\n\n\tfunction add(fn: QueueFunction, highPriority: boolean = false): void {\n\t\t// Already added before?\n\t\tif (fn.__queued) {\n\t\t\t// Move from low to high-priority queue\n\t\t\tif (highPriority) {\n\t\t\t\tconst idx = qlow.indexOf(fn);\n\t\t\t\tif (idx >= 0) {\n\t\t\t\t\tconst removed = qlow.splice(idx, 1);\n\t\t\t\t\ttotal = total - removed.length;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// Mark as processed\n\t\tfn.__queued = true;\n\t\t// Push to queue: high or low\n\t\t(highPriority ? qhigh : qlow).push(fn);\n\t\t// Increment total\n\t\ttotal++;\n\t\t// Initialize queue if first item\n\t\tif (total <= 1) {\n\t\t\trun();\n\t\t}\n\t}\n\n\tfunction next(): void {\n\t\trunning--; // make room for next\n\t\trun();\n\t}\n\n\tfunction run(): void {\n\t\tif (running < limit && total > 0) {\n\t\t\tconst fn = qhigh.shift() || qlow.shift() || (() => {});\n\t\t\tfn();\n\t\t\ttotal--;\n\t\t\trunning++; // is now WIP\n\t\t}\n\t}\n\n\treturn { add, next };\n}\n", "import { whenIdle } from './util.js';\n\nexport type Observer = {\n\tstart: () => void;\n\tstop: () => void;\n\tupdate: () => void;\n};\n\nexport default function createObserver({\n\tthreshold,\n\tdelay,\n\tcontainers,\n\tcallback,\n\tfilter\n}: {\n\tthreshold: number;\n\tdelay: number;\n\tcontainers: string[];\n\tcallback: (el: HTMLAnchorElement) => void;\n\tfilter: (el: HTMLAnchorElement) => boolean;\n}): Observer {\n\tconst visibleLinks = new Map<string, Set<HTMLAnchorElement>>();\n\n\t// Create an observer to add/remove links when they enter the viewport\n\tconst observer = new IntersectionObserver(\n\t\t(entries) => {\n\t\t\tentries.forEach((entry) => {\n\t\t\t\tif (entry.isIntersecting) {\n\t\t\t\t\tadd(entry.target as HTMLAnchorElement);\n\t\t\t\t} else {\n\t\t\t\t\tremove(entry.target as HTMLAnchorElement);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t{ threshold }\n\t);\n\n\t// Preload link if it is still visible after a configurable timeout\n\tconst add = (el: HTMLAnchorElement) => {\n\t\tconst elements = visibleLinks.get(el.href) ?? new Set();\n\t\tvisibleLinks.set(el.href, elements);\n\t\telements.add(el);\n\n\t\tsetTimeout(() => {\n\t\t\tconst elements = visibleLinks.get(el.href);\n\t\t\tif (elements?.size) {\n\t\t\t\tcallback(el);\n\t\t\t\tobserver.unobserve(el);\n\t\t\t\telements.delete(el);\n\t\t\t}\n\t\t}, delay);\n\t};\n\n\t// Remove link from list of visible links\n\tconst remove = (el: HTMLAnchorElement) => visibleLinks.get(el.href)?.delete(el);\n\n\t// Clear list of visible links\n\tconst clear = () => visibleLinks.clear();\n\n\t// Scan DOM for preloadable links and start observing their visibility\n\tconst observe = () => {\n\t\twhenIdle(() => {\n\t\t\tconst selector = containers.map((root) => `${root} a[href]`).join(', ');\n\t\t\tconst links = Array.from(document.querySelectorAll<HTMLAnchorElement>(selector));\n\t\t\tlinks.filter((el) => filter(el)).forEach((el) => observer.observe(el));\n\t\t});\n\t};\n\n\treturn {\n\t\tstart: () => observe(),\n\t\tstop: () => observer.disconnect(),\n\t\tupdate: () => (clear(), observe())\n\t};\n}\n"], "names": ["n", "r", "String", "split", "map", "parseInt", "concat", "slice", "join", "e", "every", "t", "s", "match", "o", "i", "localeCompare", "numeric", "deviceSupportsHover", "window", "matchMedia", "matches", "whenIdle", "requestIdleCallback", "cb", "setTimeout", "Plugin", "constructor", "options", "super", "_this", "this", "name", "requires", "swup", "defaults", "throttle", "preloadInitialPage", "preloadHoveredLinks", "preloadVisibleLinks", "enabled", "threshold", "delay", "containers", "ignore", "queue", "preloadObserver", "preloadPromises", "Map", "mouseEnterDelegate", "touchStartDelegate", "focusDelegate", "onPageLoad", "visit", "args", "defaultHandler", "url", "to", "has", "get", "onMouseEnter", "event", "target", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "el", "HTMLAnchorElement", "hooks", "callSync", "preload", "priority", "reject", "onTouchStart", "onFocus", "otherOptions", "Boolean", "bind", "limit", "qlow", "qhigh", "total", "running", "run", "shift", "add", "fn", "highPriority", "__queued", "idx", "indexOf", "removed", "splice", "length", "push", "next", "createQueue", "mount", "cache", "console", "warn", "create", "preloadLinks", "linkSelector", "selector", "opts", "passive", "capture", "delegateEvent", "replace", "on", "getCurrentUrl", "unmount", "undefined", "clear", "destroy", "stopPreloadingVisibleLinks", "input", "_this2", "Array", "isArray", "all", "link", "Location", "fromElement", "shouldPreload", "queuedPromise", "performPreload", "catch", "then", "page", "finally", "delete", "set", "from", "document", "querySelectorAll", "for<PERSON>ach", "update", "_ref", "callback", "filter", "visibleLinks", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "remove", "elements", "href", "Set", "size", "unobserve", "observe", "root", "start", "stop", "disconnect", "createObserver", "location", "_temp", "fromUrl", "navigator", "connection", "saveData", "effectiveType", "endsWith", "networkSupportsPreloading", "shouldIgnoreVisit", "resolveUrl", "_this3", "fetchPage", "call"], "mappings": "wcAGO,MAAMA,EAAoBC,GACzBC,OAAOD,GACZE,MAAM,KACNC,IAAIH,GAAWC,OAAOG,SAASJ,GAAW,IAAK,MAC/CK,OAAO,CAAC,IAAK,MACbC,MAAM,EAAG,GACTC,KAAK,KAAA,0nBAiCwB,EAACP,EAAmBQ,IAC5CA,EAAaC,MAAOD,IAC1B,MAASE,CAAAA,EAAYC,GAAWH,EAASI,MAAM,mBAAqB,GA/BxC,IAACC,EAAWC,EAiCxC,MA1BsB,EAACd,EAA0BD,KAClD,MAAMS,EAAc,CACnB,GAAKR,GAAoB,IAANA,EACnB,IAAMA,GAAcA,EAAI,EACxB,KAAOA,GAAcA,GAAK,EAC1B,IAAMA,GAAcA,EAAI,EACxB,KAAOA,GAAcA,GAAK,GAG3B,OADqBQ,EAAYT,IAAeS,EAAY,KACxCR,EAhBqBc,EAOlB,EAPkBA,EAgCWH,EA/BpDE,EAAId,EAD0Bc,EAgCWb,GA9BzCc,EAAIf,EAAiBe,GACdD,EAAEE,cAAcD,OAAA,EAAc,CAAEE,SAAS,KA8BLN,GAA6B,KAAI,GAJ7C,62BCvBhB,SAAAO,IACf,OAAOC,OAAOC,WAAW,kBAAkBC,OAC5C,CAKa,MAAAC,EAAWH,OAAOI,qBAAmB,CAAMC,GAAOC,WAAWD,EAAI,yBC0C/BE,EA4B9CC,WAAAA,CAAYC,YAAAA,IAAAA,EAAsC,CAAA,GACjDC,QAAQ,MAAAC,EAkHRC,KA9IDC,KAAAA,KAAO,oBAAmBD,KAE1BE,SAAW,CAAEC,KAAM,OAAOH,KAE1BI,SAA0B,CACzBC,SAAU,EACVC,oBAAoB,EACpBC,qBAAqB,EACrBC,oBAAqB,CACpBC,SAAS,EACTC,UAAW,GACXC,MAAO,IACPC,WAAY,CAAC,QACbC,OAAQA,KAAM,IAIhBhB,KAAAA,oBAEUiB,WAAK,EAAAd,KACLe,qBACAC,EAAAA,KAAAA,gBAAkB,IAAIC,IAAuCjB,KAE7DkB,wBAAkB,EAAAlB,KAClBmB,wBACAC,EAAAA,KAAAA,mBAgGAC,EAAAA,KAAAA,WAA8C,CAACC,EAAOC,EAAMC,KACrE,MAAMC,IAAEA,GAAQH,EAAMI,GACtB,OAAID,GAAOzB,KAAKgB,gBAAgBW,IAAIF,GAC5BzB,KAAKgB,gBAAgBY,IAAIH,GAE1BD,EAAgBF,EAAOC,EAAI,EAMzBM,KAAAA,aAA4CC,SAAAA,OAErD,GAAIA,EAAMC,SAAWD,EAAME,eAAgB,OAAAC,QAAAC,UAG3C,IAAK/C,IAAuB,OAAA8C,QAAAC,UAE5B,MAAMC,EAAKL,EAAME,eACjB,OAAMG,aAAcC,mBAEpBrC,EAAKI,KAAKkC,MAAMC,SAAS,aAAc,CAAEH,KAAIL,UAC7C/B,EAAKwC,QAAQJ,EAAI,CAAEK,UAAU,IAAQP,QAAAC,WAHGD,QAAAC,SAIzC,CAAC,MAAAxD,GAAAuD,OAAAA,QAAAQ,OAAA/D,EAAA,CAAA,EAAAsB,KAKS0C,aAAsCZ,IAE/C,GAAI3C,IAAuB,OAE3B,MAAMgD,EAAKL,EAAME,eACXG,aAAcC,mBAEpBpC,KAAKuC,QAAQJ,EAAI,CAAEK,UAAU,GAC9B,EAKUG,KAAAA,QAAiCb,IAC1C,MAAMK,EAAKL,EAAME,eACXG,aAAcC,mBAEpBpC,KAAKuC,QAAQJ,EAAI,CAAEK,UAAU,GAC9B,EAxIC,MAAMhC,oBAAEA,KAAwBoC,GAAiB/C,EACjDG,KAAKH,QAAU,IAAKG,KAAKI,YAAawC,GAGH,iBAAxBpC,EACVR,KAAKH,QAAQW,oBAAsB,IAC/BR,KAAKH,QAAQW,oBAChBC,SAAS,KACND,GAGJR,KAAKH,QAAQW,oBAAoBC,QAAUoC,QAAQrC,GAIpDR,KAAKuC,QAAUvC,KAAKuC,QAAQO,KAAK9C,MAGjCA,KAAKc,MC5GO,SAAsBiC,YAAAA,IAAAA,EAAgB,GACnD,MAAMC,EAAwB,GACxBC,EAAyB,GAC/B,IAAIC,EAAQ,EACRC,EAAU,EAkCd,SAASC,IACJD,EAAUJ,GAASG,EAAQ,KACnBD,EAAMI,SAAWL,EAAKK,eAAoB,MAErDH,IACAC,IAEF,CAEA,MAAO,CAAEG,IAzCT,SAAaC,EAAmBC,GAE/B,YAF+BA,IAAAA,GAAwB,GAEnDD,EAAGE,SAAU,CAEhB,IAAID,EAOH,OAPiB,CACjB,MAAME,EAAMV,EAAKW,QAAQJ,GACzB,GAAIG,GAAO,EAAG,CACb,MAAME,EAAUZ,EAAKa,OAAOH,EAAK,GACjCR,GAAgBU,EAAQE,MACxB,CACD,CAGD,CAGDP,EAAGE,UAAW,GAEbD,EAAeP,EAAQD,GAAMe,KAAKR,GAEnCL,IAEIA,GAAS,GACZE,GAEF,EAgBcY,KAdd,WACCb,IACAC,GACD,EAYD,CD4Dea,CAAYjE,KAAKH,QAAQQ,SACvC,CAEA6D,KAAAA,GACC,MAAM/D,EAAOH,KAAKG,KAElB,IAAKA,EAAKN,QAAQsE,MAEjB,YADAC,QAAQC,KAAK,oEAIdlE,EAAKkC,MAAMiC,OAAO,gBAClBnE,EAAKkC,MAAMiC,OAAO,cAGlBnE,EAAKoC,QAAUvC,KAAKuC,QACpBpC,EAAKoE,aAAevE,KAAKuE,aAGzB,MAAQC,aAAcC,GAAatE,EAAKN,QAClC6E,EAAO,CAAEC,SAAS,EAAMC,SAAS,GACvC5E,KAAKkB,mBAAqBf,EAAK0E,cAC9BJ,EACA,aACAzE,KAAK6B,aACL6C,GAED1E,KAAKmB,mBAAqBhB,EAAK0E,cAC9BJ,EACA,aACAzE,KAAK0C,aACLgC,GAED1E,KAAKoB,cAAgBjB,EAAK0E,cAAcJ,EAAU,QAASzE,KAAK2C,QAAS+B,GAGzE1E,KAAK8E,QAAQ,YAAa9E,KAAKqB,YAG3BrB,KAAKH,QAAQU,sBAChBP,KAAKuE,eACLvE,KAAK+E,GAAG,YAAa,IAAM/E,KAAKuE,iBAI7BvE,KAAKH,QAAQW,oBAAoBC,UACpCT,KAAKQ,sBACLR,KAAK+E,GAAG,YAAa,IAAM/E,KAAKQ,wBAI7BR,KAAKH,QAAQS,oBAChBN,KAAKuC,QAAQyC,IAEf,CAEAC,OAAAA,GACCjF,KAAKG,KAAKoC,aAAU2C,EACpBlF,KAAKG,KAAKoE,kBAAeW,EAEzBlF,KAAKgB,gBAAgBmE,QAErBnF,KAAKkB,oBAAoBkE,UACzBpF,KAAKmB,oBAAoBiE,UACzBpF,KAAKoB,eAAegE,UAEpBpF,KAAKqF,4BACN,CAwEM9C,OAAAA,CACL+C,EACAzF,YAAAA,IAAAA,EAA0B,IAAE,UAAA0F,EAQYvF,KANxC,IAAIyB,EACAU,EACJ,MAAMK,EAAW3C,EAAQ2C,WAAY,EAGrC,GAAIgD,MAAMC,QAAQH,GACjB,OAAOrD,QAAQyD,IAAIJ,EAAMjH,IAAKsH,GAASJ,EAAKhD,QAAQoD,KAGhD,GAAIL,aAAiBlD,kBACzBD,EAAKmD,IACF7D,OAAQmE,EAASC,YAAYP,YAGP,iBAAVA,EAKf,OAAArD,QAAAC,UAJAT,EAAM6D,CAKN,CAGD,GAAIC,EAAKvE,gBAAgBW,IAAIF,GAC5B,OAAAQ,QAAAC,QAAOqD,EAAKvE,gBAAgBY,IAAIH,IAIjC,IAAK8D,EAAKO,cAAcrE,EAAK,CAAEU,OAC9B,OAAAF,QAAAC,UAKD,MAAM6D,EAAgB,IAAI9D,QAA0BC,IACnDqD,EAAKzE,MAAMwC,IAAI,KACdiC,EAAKS,eAAevE,GAClBwE,MAAM,QACNC,KAAMC,GAASjE,EAAQiE,IACvBC,QAAQ,KACRb,EAAKzE,MAAMkD,OACXuB,EAAKvE,gBAAgBqF,OAAO5E,EAC7B,IACCe,EACJ,GAIA,OAFA+C,EAAKvE,gBAAgBsF,IAAI7E,EAAKsE,GAE9B9D,QAAAC,QAAO6D,EACR,CAAC,MAAArH,UAAAuD,QAAAQ,OAAA/D,IASD6F,YAAAA,GACChF,EAAS,KAEMiG,MAAMe,KAAKC,SAASC,iBADjB,oDAEXC,QAASvE,GAAOnC,KAAKuC,QAAQJ,GACpC,EACD,CAMU3B,mBAAAA,GAET,GAAIR,KAAKe,gBAER,YADAf,KAAKe,gBAAgB4F,SAItB,MAAMjG,UAAEA,EAASC,MAAEA,EAAKC,WAAEA,GAAeZ,KAAKH,QAAQW,oBAQtDR,KAAKe,gBElViB,SAAc6F,GAYrC,IAZsClG,UACtCA,EAASC,MACTA,EAAKC,WACLA,EAAUiG,SACVA,EAAQC,OACRA,GAOAF,EACA,MAAMG,EAAe,IAAI9F,IAGnB+F,EAAW,IAAIC,qBACnBC,IACAA,EAAQR,QAASS,IACZA,EAAMC,eACT9D,EAAI6D,EAAMpF,QAEVsF,EAAOF,EAAMpF,OACb,EAEH,EACA,CAAErB,cAIG4C,EAAOnB,IACZ,MAAMmF,EAAWP,EAAanF,IAAIO,EAAGoF,OAAS,IAAIC,IAClDT,EAAaT,IAAInE,EAAGoF,KAAMD,GAC1BA,EAAShE,IAAInB,GAEbzC,WAAW,KACV,MAAM4H,EAAWP,EAAanF,IAAIO,EAAGoF,MACjCD,GAAUG,OACbZ,EAAS1E,GACT6E,EAASU,UAAUvF,GACnBmF,EAASjB,OAAOlE,GAChB,EACCxB,EAAK,EAIH0G,EAAUlF,GAA0B4E,EAAanF,IAAIO,EAAGoF,OAAOlB,OAAOlE,GAMtEwF,EAAUA,KACfpI,EAAS,KACR,MAAMkF,EAAW7D,EAAWvC,IAAKuJ,GAAY,GAAAA,aAAgBnJ,KAAK,MACpD+G,MAAMe,KAAKC,SAASC,iBAAoChC,IAChEqC,OAAQ3E,GAAO2E,EAAO3E,IAAKuE,QAASvE,GAAO6E,EAASW,QAAQxF,GAAG,EAEvE,EAEA,MAAO,CACN0F,MAAOA,IAAMF,IACbG,KAAMA,IAAMd,EAASe,aACrBpB,OAAQA,KAdWI,EAAa5B,QAcRwC,KAE1B,CFiRyBK,CAAe,CAAEtH,YAAWC,QAAOC,aAAYiG,SAPpD1E,GAA0BnC,KAAKuC,QAAQJ,GAOuB2E,OANhE3E,IAEXnC,KAAKH,QAAQW,oBAAoBK,OAAOsB,IAEjCnC,KAAC8F,cAAc3D,EAAGoF,KAAM,CAAEpF,SAGtCnC,KAAKe,gBAAgB8G,OACtB,CAKUxC,0BAAAA,GACLrF,KAAKe,iBACRf,KAAKe,gBAAgB+G,MAEvB,CAKUhC,aAAAA,CAAcmC,EAAgBC,GAAE,IAAA/F,GAAEA,cAAmC,CAAA,EAAE+F,EAChF,MAAMzG,IAAEA,EAAG8F,KAAEA,GAAS3B,EAASuC,QAAQF,GAGvC,oBDzWD,GAAIG,UAAUC,WAAY,CACzB,GAAID,UAAUC,WAAWC,SACxB,OACA,EACD,GAAIF,UAAUC,WAAWE,eAAeC,SAAS,MAChD,OACA,CACD,CACD,QACD,CCgWOC,IAEDzI,KAAKG,KAAKgE,MAAMxC,IAAIF,IAEpBzB,KAAKgB,gBAAgBW,IAAIF,IAEzBzB,KAAKG,KAAKuI,kBAAkBnB,EAAM,CAAEpF,QAEpCA,GAAMnC,KAAKG,KAAKwI,WAAWlH,KAASzB,KAAKG,KAAKwI,WAAW3D,KAG9D,CAKgBgB,cAAAA,CAAevE,GAAW,IAAA,MAAAmH,EACtB5I,KAAIiC,OAAAA,QAAAC,QAAJ0G,EAAKzI,KAAK0I,UAAUpH,IAAIyE,KAAA,SAArCC,GAAI,OAAAlE,QAAAC,QACJ0G,EAAKzI,KAAKkC,MAAMyG,KAAK,eAAgB,CAAE3C,UAAOD,KACpD,WAAA,OAAOC,CAAK,EAAA,EACb,CAAC,MAAAzH,GAAAuD,OAAAA,QAAAQ,OAAA/D,EACD,CAAA"}