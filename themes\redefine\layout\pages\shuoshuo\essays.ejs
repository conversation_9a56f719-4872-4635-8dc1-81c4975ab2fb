<div class="mb-4">
	<h1 class="page-title-header"><%= page.title %></h1>
	<ul class="flex flex-col mb-4 gap-6">
		<%
        const timezone = config.timezone || 'Asia/Shanghai';
        const newessays = theme.essays.sort((a, b) => moment(b.date).diff(a.date));
        %>
		<% for (const e of newessays) { %>
		<li class="w-full flex flex-row relative">
			<div class="w-10 h-10 mr-4 rounded-lg overflow-hidden border border-border-color p-[1px] shrink-0">
				<%- image_tag(theme.defaults.avatar, {class: "w-full h-full rounded-[6.2px]"}) %>
			</div>
			<div class="w-full border-border-color rounded-xl rounded-tl-none shadow-redefine-flat overflow-hidden">
				<!-- Pass the raw date here in a data attribute -->
				<% rawDate = moment(e.date).utc().format('YYYY-MM-DD HH:mm:ss') %>
				<div class="essay-date px-4 py-1.5 text-sm border-b border-border-color bg-zinc-50 dark:bg-zinc-800 text-third-text-color" data-date="<%= moment(rawDate).tz(timezone).format() %>">Loading Date...</div>
				<div id="shuoshuo-content" class="px-4 py-2"><%- markdown(e.content) %></div>
			</div>
			<div class="absolute left-[50.5px] top-3 transform w-2 h-2 border-solid border-t border-l bg-zinc-50 -rotate-45 border-border-color dark:bg-zinc-800"></div>
		</li>
		<% } %>
	</ul>
</div>