!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e||self).SwupSlideTheme=t()}(this,function(){function e(){return e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},e.apply(this,arguments)}const t=e=>String(e).split(".").map(e=>String(parseInt(e||"0",10))).concat(["0","0"]).slice(0,3).join(".");class s{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(e=>e()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error("You must define a name of plugin when creating a class.")}_afterUnmount(){}_checkRequirements(){return"object"!=typeof this.requires||Object.entries(this.requires).forEach(([e,s])=>{if(!function(e,s,n){const r=function(e,t){var s;if("swup"===e)return null!=(s=t.version)?s:"";{var n;const s=t.findPlugin(e);return null!=(n=null==s?void 0:s.version)?n:""}}(e,n);return!!r&&((e,s)=>s.every(s=>{const[,n,r]=s.match(/^([\D]+)?(.*)$/)||[];var i,o;return((e,t)=>{const s={"":e=>0===e,">":e=>e>0,">=":e=>e>=0,"<":e=>e<0,"<=":e=>e<=0};return(s[t]||s[""])(e)})((o=r,i=t(i=e),o=t(o),i.localeCompare(o,void 0,{numeric:!0})),n||">=")}))(r,s)}(e,s=Array.isArray(s)?s:[s],this.swup)){const t=`${e} ${s.join(", ")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${t}`)}}),!0}on(e,t,s={}){var n;t=!(n=t).name.startsWith("bound ")||n.hasOwnProperty("prototype")?t.bind(this):t;const r=this.swup.hooks.on(e,t,s);return this.handlersToUnregister.push(r),r}once(t,s,n={}){return this.on(t,s,e({},n,{once:!0}))}before(t,s,n={}){return this.on(t,s,e({},n,{before:!0}))}replace(t,s,n={}){return this.on(t,s,e({},n,{replace:!0}))}off(e,t){return this.swup.hooks.off(e,t)}}class n extends s{constructor(...e){super(...e),this._addedStyleElements=[],this._addedHTMLContent=[],this._classNameAddedToElements=[],this._addClassNameToElement=()=>{this._classNameAddedToElements.forEach(e=>{Array.from(document.querySelectorAll(e.selector)).forEach(t=>{t.classList.add(`swup-transition-${e.name}`)})})}}_beforeMount(){this._originalAnimationSelectorOption=String(this.swup.options.animationSelector),this.swup.options.animationSelector='[class*="swup-transition-"]',this.swup.hooks.on("content:replace",this._addClassNameToElement)}_afterUnmount(){this.swup.options.animationSelector=this._originalAnimationSelectorOption,this._addedStyleElements.forEach(e=>{e.outerHTML="",e=null}),this._addedHTMLContent.forEach(e=>{e.outerHTML="",e=null}),this._classNameAddedToElements.forEach(e=>{Array.from(document.querySelectorAll(e.selector)).forEach(e=>{e.className.split(" ").forEach(t=>{new RegExp("^swup-transition-").test(t)&&e.classList.remove(t)})})}),this.swup.hooks.off("content:replace",this._addClassNameToElement)}applyStyles(e){const t=document.createElement("style");t.setAttribute("data-swup-theme",""),t.appendChild(document.createTextNode(e)),document.head.prepend(t),this._addedStyleElements.push(t)}applyHTML(e){const t=document.createElement("div");t.innerHTML=e,document.body.appendChild(t),this._addedHTMLContent.push(t)}addClassName(e,t){this._classNameAddedToElements.push({selector:e,name:t}),this._addClassNameToElement()}}return class extends n{constructor(e){void 0===e&&(e={}),super(),this.name="SwupSlideTheme",this.defaults={mainElement:"#swup",reversed:!1},this.options={...this.defaults,...e}}mount(){this.applyStyles("html{--swup-slide-theme-direction:1;--swup-slide-theme-translate:60px;--swup-slide-theme-duration-fade:.3s;--swup-slide-theme-duration-slide:.4s;--swup-slide-theme-translate-forward:calc(var(--swup-slide-theme-direction)*var(--swup-slide-theme-translate));--swup-slide-theme-translate-backward:calc(var(--swup-slide-theme-translate-forward)*-1)}html.swup-theme-reverse{--swup-slide-theme-direction:-1}html.is-changing .swup-transition-main{opacity:1;transform:translateZ(0);transition:opacity var(--swup-slide-theme-duration-fade),transform var(--swup-slide-theme-duration-slide)}html.is-animating .swup-transition-main{opacity:0;transform:translate3d(0,var(--swup-slide-theme-translate-backward),0)}html.is-animating.is-leaving .swup-transition-main{transform:translate3d(0,var(--swup-slide-theme-translate-forward),0)}"),this.addClassName(this.options.mainElement,"main"),this.options.reversed&&document.documentElement.classList.add("swup-theme-reverse")}unmount(){document.documentElement.classList.remove("swup-theme-reverse")}}});