!function(t,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):(t||self).SwupScrollPlugin=o()}(this,function(){function t(){return t=Object.assign?Object.assign.bind():function(t){for(var o=1;o<arguments.length;o++){var s=arguments[o];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},t.apply(this,arguments)}const o=t=>String(t).split(".").map(t=>String(parseInt(t||"0",10))).concat(["0","0"]).slice(0,3).join(".");class s{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(t=>t()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error("You must define a name of plugin when creating a class.")}_afterUnmount(){}_checkRequirements(){return"object"!=typeof this.requires||Object.entries(this.requires).forEach(([t,s])=>{if(!function(t,s,i){const n=function(t,o){var s;if("swup"===t)return null!=(s=o.version)?s:"";{var i;const s=o.findPlugin(t);return null!=(i=null==s?void 0:s.version)?i:""}}(t,i);return!!n&&((t,s)=>s.every(s=>{const[,i,n]=s.match(/^([\D]+)?(.*)$/)||[];var e,r;return((t,o)=>{const s={"":t=>0===t,">":t=>t>0,">=":t=>t>=0,"<":t=>t<0,"<=":t=>t<=0};return(s[o]||s[""])(t)})((r=n,e=o(e=t),r=o(r),e.localeCompare(r,void 0,{numeric:!0})),i||">=")}))(n,s)}(t,s=Array.isArray(s)?s:[s],this.swup)){const o=`${t} ${s.join(", ")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${o}`)}}),!0}on(t,o,s={}){var i;o=!(i=o).name.startsWith("bound ")||i.hasOwnProperty("prototype")?o.bind(this):o;const n=this.swup.hooks.on(t,o,s);return this.handlersToUnregister.push(n),n}once(o,s,i={}){return this.on(o,s,t({},i,{once:!0}))}before(o,s,i={}){return this.on(o,s,t({},i,{before:!0}))}replace(o,s,i={}){return this.on(o,s,t({},i,{replace:!0}))}off(t,o){return this.swup.hooks.off(t,o)}}const i=(t,o=document)=>Array.from(o.querySelectorAll(t));function n(){return n=Object.assign?Object.assign.bind():function(t){for(var o=1;o<arguments.length;o++){var s=arguments[o];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(t[i]=s[i])}return t},n.apply(this,arguments)}class e{constructor(t){this._raf=null,this._positionY=0,this._velocityY=0,this._targetPositionY=0,this._targetPositionYWithOffset=0,this._direction=0,this.scrollTo=t=>{if(t&&t.nodeType)this._targetPositionY=Math.round(t.getBoundingClientRect().top+window.pageYOffset);else{if(parseInt(this._targetPositionY)!==this._targetPositionY)return void console.error("Argument must be a number or an element.");this._targetPositionY=Math.round(t)}this._targetPositionY>document.documentElement.scrollHeight-window.innerHeight&&(this._targetPositionY=document.documentElement.scrollHeight-window.innerHeight),this._positionY=document.body.scrollTop||document.documentElement.scrollTop,this._direction=this._positionY>this._targetPositionY?-1:1,this._targetPositionYWithOffset=this._targetPositionY+this._direction,this._velocityY=0,this._positionY!==this._targetPositionY?(this.options.onStart(),this._animate()):this.options.onAlreadyAtPositions()},this._animate=()=>{this._update(),this._render(),1===this._direction&&this._targetPositionY>this._positionY||-1===this._direction&&this._targetPositionY<this._positionY?(this._raf=requestAnimationFrame(this._animate),this.options.onTick()):(this._positionY=this._targetPositionY,this._render(),this._raf=null,this.options.onTick(),this.options.onEnd())},this._update=()=>{const t=this._targetPositionYWithOffset-this._positionY;return this._velocityY+=t*this.options.acceleration,this._velocityY*=this.options.friction,this._positionY+=this._velocityY,Math.abs(t)},this._render=()=>{window.scrollTo(0,this._positionY)},this.options=n({},{onAlreadyAtPositions:()=>{},onCancel:()=>{},onEnd:()=>{},onStart:()=>{},onTick:()=>{},friction:.7,acceleration:.04},t),t&&t.friction&&(this.options.friction=1-t.friction),window.addEventListener("mousewheel",t=>{this._raf&&(this.options.onCancel(),cancelAnimationFrame(this._raf),this._raf=null)},{passive:!0})}}return class extends s{constructor(t){var o;void 0===t&&(t={}),super(),o=this,this.name="SwupScrollPlugin",this.requires={swup:">=4.2.0"},this.scrl=void 0,this.defaults={doScrollingRightAway:!1,animateScroll:{betweenPages:!0,samePageWithHash:!0,samePage:!0},scrollFriction:.3,scrollAcceleration:.04,getAnchorElement:void 0,offset:0,scrollContainers:"[data-swup-scroll-container]",shouldResetScrollPosition:()=>!0},this.options=void 0,this.cachedScrollPositions={},this.previousScrollRestoration=void 0,this.currentCacheKey=void 0,this.getAnchorElement=function(t){return void 0===t&&(t=""),"function"==typeof o.options.getAnchorElement?o.options.getAnchorElement(t):o.swup.getAnchorElement(t)},this.getOffset=t=>t?"function"==typeof this.options.offset?parseInt(String(this.options.offset(t)),10):parseInt(String(this.options.offset),10):0,this.handleScrollToTop=()=>(this.swup.scrollTo?.(0,this.shouldAnimate("samePage")),!0),this.handleScrollToAnchor=(t,o)=>{let{hash:s}=o;return this.maybeScrollToAnchor(s,this.shouldAnimate("samePageWithHash"))},this.onVisitStart=t=>{this.maybeResetScrollPositions(t),this.cacheScrollPositions(t.from.url);const o=t.scroll.target||t.to.hash;t.scroll.scrolledToContent=!1,this.options.doScrollingRightAway&&!o&&(t.scroll.scrolledToContent=!0,this.doScrollingBetweenPages(t))},this.onScrollToContent=t=>{t.scroll.scrolledToContent||this.doScrollingBetweenPages(t),this.restoreScrollContainers(t.to.url)},this.doScrollingBetweenPages=t=>{if(t.history.popstate&&!t.animation.animate)return;if(this.maybeScrollToAnchor(t.scroll.target||t.to.hash,this.shouldAnimate("betweenPages")))return;if(!t.scroll.reset)return;const o=this.getCachedScrollPositions(this.swup.resolveUrl((({hash:t}={})=>location.pathname+location.search+(t?location.hash:""))())),s=o?.window?.top||0;requestAnimationFrame(()=>this.swup.scrollTo?.(s,this.shouldAnimate("betweenPages")))},this.maybeResetScrollPositions=t=>{const{url:o}=t.to,{el:s}=t.trigger;s&&this.options.shouldResetScrollPosition(s)&&this.resetScrollPositions(o)},this.options={...this.defaults,...t}}mount(){var t=this;const o=this.swup;o.hooks.create("scroll:start"),o.hooks.create("scroll:end"),this.scrl=new e({onStart:()=>o.hooks.callSync("scroll:start"),onEnd:()=>o.hooks.callSync("scroll:end"),onCancel:()=>o.hooks.callSync("scroll:end"),friction:this.options.scrollFriction,acceleration:this.options.scrollAcceleration}),o.scrollTo=function(s,i){void 0===i&&(i=!0),i?t.scrl.scrollTo(s):(o.hooks.callSync("scroll:start"),window.scrollTo(0,s),o.hooks.callSync("scroll:end"))},this.previousScrollRestoration=window.history.scrollRestoration,o.options.animateHistoryBrowsing&&(window.history.scrollRestoration="manual"),this.on("visit:start",this.onVisitStart),this.replace("content:scroll",this.onScrollToContent),this.replace("scroll:top",this.handleScrollToTop),this.replace("scroll:anchor",this.handleScrollToAnchor)}unmount(){super.unmount(),this.previousScrollRestoration&&(window.history.scrollRestoration=this.previousScrollRestoration),this.cachedScrollPositions={},delete this.swup.scrollTo,delete this.scrl}shouldAnimate(t){return"boolean"==typeof this.options.animateScroll?this.options.animateScroll:this.options.animateScroll[t]}maybeScrollToAnchor(t,o){if(void 0===o&&(o=!1),!t)return!1;const s=this.getAnchorElement(t);if(!s)return console.warn(`Anchor target ${t} not found`),!1;if(!(s instanceof Element))return console.warn(`Anchor target ${t} is not a DOM node`),!1;const{top:i}=s.getBoundingClientRect(),n=i+window.scrollY-this.getOffset(s);return this.swup.scrollTo?.(n,o),!0}cacheScrollPositions(t){const o=i(this.options.scrollContainers).map(t=>({top:t.scrollTop,left:t.scrollLeft})),s={window:{top:window.scrollY,left:window.scrollX},containers:o};this.cachedScrollPositions[t]=s}resetScrollPositions(t){const o=this.swup.resolveUrl(t);delete this.cachedScrollPositions[o]}getCachedScrollPositions(t){const o=this.swup.resolveUrl(t);return this.cachedScrollPositions[o]}restoreScrollContainers(t){const o=this.getCachedScrollPositions(t);o&&0!==o.containers.length&&i(this.options.scrollContainers).forEach((t,s)=>{const i=o.containers[s];null!=i&&(t.scrollTop=i.top,t.scrollLeft=i.left)})}}});
//# sourceMappingURL=SwupScrollPlugin.min.js.map
