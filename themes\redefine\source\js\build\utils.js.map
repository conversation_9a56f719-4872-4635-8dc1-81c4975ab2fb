{"version": 3, "file": "utils.js", "names": ["navbarShrink", "initTOC", "main", "imageViewer", "navigationState", "isNavigating", "initUtils", "utils", "html_root_dom", "document", "querySelector", "pageContainer_dom", "pageTop_dom", "homeBanner_dom", "homeBannerBackground_dom", "scrollProgressBar_dom", "pjaxProgressBar_dom", "backToTopButton_dom", "toolsList", "to<PERSON><PERSON><PERSON><PERSON>", "innerHeight", "window", "pjaxProgressBarTimer", "prevScrollValue", "fontSizeLevel", "triggerViewHeight", "isHasScrollProgressBar", "theme", "global", "scroll_progress", "bar", "isHasScrollPercent", "percentage", "updateScrollStyle", "scrollTop", "pageYOffset", "documentElement", "scrollHeight", "clientHeight", "percent", "this", "calculatePercentage", "updateScrollProgressBar", "updateScrollPercent", "updatePageTopVisibility", "progressPercent", "toFixed", "visibility", "style", "width", "percentDom", "showButton", "undefined", "classList", "toggle", "innerHTML", "navbar", "auto_hide", "hidePageTop", "remove", "percentageValue", "Math", "round", "isNaN", "isFinite", "registerWindowScroll", "addEventListener", "updateTOCScroll", "updateNavbarShrink", "updateAutoHideTools", "debounce", "updateHomeBannerBlur", "articles", "toc", "enable", "hasOwnProperty", "updateActiveTOCLink", "init", "func", "delay", "timer", "clearTimeout", "setTimeout", "apply", "arguments", "home_banner", "location", "pathname", "config", "root", "blurValue", "scrollY", "requestAnimationFrame", "filter", "webkitFilter", "e", "console", "error", "y", "height", "body", "windowHeight", "toolList", "getElementsByClassName", "aplayer", "getElementById", "i", "length", "tools", "add", "toggleToolsList", "side_tools", "auto_expand", "fontAdjPlus_dom", "fontAdMinus_dom", "globalFontSizeAdjust", "htmlRoot", "fontAdjustPlus", "fontAdjustMinus", "fontSize", "defaultView", "getComputedStyle", "baseFontSize", "parseFloat", "styleStatus", "getStyleStatus", "setFontSize", "level", "setStyleStatus", "increaseFontSize", "min", "decreaseFontSize", "max", "goComment", "goComment_dom", "target", "offset", "getBoundingClientRect", "top", "scrollTo", "behavior", "getElementHeight", "selectors", "dom", "inithomeBannerHeight", "initPageHeightHandle", "allDomHeight", "pb_dom", "marginTopValue", "floor", "marginTop", "setHowLongAgoLanguage", "p1", "p2", "replace", "getHowLongAgo", "timestamp", "l", "lang_ago", "__Y", "__M", "__W", "__d", "__h", "__m", "__s", "year", "month", "week", "day", "hour", "minute", "second", "relativeTimeInHome", "post", "querySelectorAll", "df", "home", "article_date_format", "for<PERSON>ach", "v", "nowDate", "Date", "now", "postDate", "dataset", "date", "split", "getTime"], "sources": ["0"], "mappings": "uBACSA,MAAoB,8CACpBC,MAAe,kCACfC,MAAY,mBACdC,MAAiB,gCAEjB,MAAMC,gBAAkB,CAC7BC,cAAc,kBAGD,SAASC,YACtB,MAAMC,EAAQ,CACZC,cAAeC,SAASC,cAAc,QACtCC,kBAAmBF,SAASC,cAAc,mBAC1CE,YAAaH,SAASC,cAAc,wBACpCG,eAAgBJ,SAASC,cAAc,0BACvCI,yBAA0BL,SAASC,cAAc,2BACjDK,sBAAuBN,SAASC,cAAc,wBAC9CM,oBAAqBP,SAASC,cAAc,sBAC5CO,oBAAqBR,SAASC,cAAc,uBAC5CQ,UAAWT,SAASC,cAAc,sBAClCS,aAAcV,SAASC,cAAc,sBAErCU,YAAaC,OAAOD,YACpBE,qBAAsB,KACtBC,gBAAiB,EACjBC,cAAe,EACfC,kBAAmB,GAAMJ,OAAOD,YAEhCM,wBAA6D,IAArCC,MAAMC,OAAOC,gBAAgBC,IACrDC,oBAAgE,IAA5CJ,MAAMC,OAAOC,gBAAgBG,WAGjD,iBAAAC,GACE,MAAMC,EACJb,OAAOc,aAAe1B,SAAS2B,gBAAgBF,UAC3CG,EAAe5B,SAAS2B,gBAAgBC,aACxCC,EACJjB,OAAOD,aAAeX,SAAS2B,gBAAgBE,aAC3CC,EAAUC,KAAKC,oBACnBP,EACAG,EACAC,GAGFE,KAAKE,wBAAwBH,GAC7BC,KAAKG,oBAAoBJ,GACzBC,KAAKI,wBAAwBV,EAAWI,GAExCE,KAAKjB,gBAAkBW,CACzB,EAEA,uBAAAQ,CAAwBH,GACtB,GAAIC,KAAKd,uBAAwB,CAC/B,MAAMmB,EAAkBN,EAAQO,QAAQ,GAClCC,EAAyB,IAAZR,EAAgB,SAAW,UAE9CC,KAAKzB,sBAAsBiC,MAAMD,WAAaA,EAC9CP,KAAKzB,sBAAsBiC,MAAMC,MAAQ,GAAGJ,IAC9C,CACF,EAEA,mBAAAF,CAAoBJ,GAClB,GAAIC,KAAKT,mBAAoB,CAC3B,MAAMmB,EAAaV,KAAKvB,oBAAoBP,cAAc,YACpDyC,EAAyB,IAAZZ,QAA6Ba,IAAZb,EAEpCC,KAAKvB,oBAAoBoC,UAAUC,OAAO,OAAQH,GAClDD,EAAWK,UAAYhB,EAAQO,QAAQ,EACzC,CACF,EAEA,uBAAAF,CAAwBV,EAAWI,GACjC,GAAIX,MAAM6B,OAAOC,UAAW,CAC1B,MAAMlC,EAAkBiB,KAAKjB,gBACvBmC,EACJnC,EAAkBe,GAAgBJ,EAAYX,EAEhDiB,KAAK5B,YAAYyC,UAAUC,OAAO,OAAQI,EAC5C,MACElB,KAAK5B,YAAYyC,UAAUM,OAAO,OAEtC,EAEA,mBAAAlB,CAAoBP,EAAWG,EAAcC,GAC3C,IAAIsB,EAAkBC,KAAKC,MACxB5B,GAAaG,EAAeC,GAAiB,KAWhD,OAREyB,MAAMH,IACNA,EAAkB,IACjBI,SAASJ,GAEVA,EAAkB,EACTA,EAAkB,MAC3BA,EAAkB,KAEbA,CACT,EAGA,oBAAAK,GACE5C,OAAO6C,iBAAiB,UAAU,KAChC1B,KAAKP,oBACLO,KAAK2B,kBACL3B,KAAK4B,qBAEL5B,KAAK6B,qBAAqB,IAE5BhD,OAAO6C,iBACL,SACA1B,KAAK8B,UAAS,IAAM9B,KAAK+B,wBAAwB,IAErD,EAEA,eAAAJ,GAEIxC,MAAM6C,SAASC,IAAIC,QACnBzE,IAAU0E,eAAe,wBAEzB1E,IAAU2E,qBAEd,EAEA,kBAAAR,GACOhE,gBAAgBC,cACnBL,EAAa6E,MAEjB,EAEA,QAAAP,CAASQ,EAAMC,GACb,IAAIC,EACJ,OAAO,WACLC,aAAaD,GACbA,EAAQE,YAAW,IAAMJ,EAAKK,MAAM3C,KAAM4C,YAAYL,EACxD,CACF,EAEA,oBAAAR,GACE,GAAK/B,KAAK1B,0BAGoB,UAA5Ba,MAAM0D,YAAYrC,OAClBsC,SAASC,WAAaC,OAAOC,KAC7B,CACA,MACMC,GADUrE,OAAOsE,SAAWtE,OAAOc,cACZK,KAAKf,kBAAoB,GAAK,EAE3D,IACEmE,uBAAsB,KACpBpD,KAAK1B,yBAAyBkC,MAAM6C,OAAS,QAAQH,OACrDlD,KAAK1B,yBAAyBkC,MAAM8C,aAAe,QAAQJ,MAAc,GAE7E,CAAE,MAAOK,GAEPC,QAAQC,MAAM,8BAA+BF,EAC/C,CACF,CACF,EAEA,mBAAA1B,GACE,MAAM6B,EAAI7E,OAAOsE,QACXQ,EAAS1F,SAAS2F,KAAK/D,aACvBgE,EAAehF,OAAOD,YACtBkF,EAAW7F,SAAS8F,uBACxB,8BAEIC,EAAU/F,SAASgG,eAAe,WAExC,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAASK,OAAQD,IAAK,CACxC,MAAME,EAAQN,EAASI,GACnBR,GAAK,IACHZ,SAASC,WAAaC,OAAOC,OAC/BmB,EAAMvD,UAAUwD,IAAI,QACJ,OAAZL,GACFA,EAAQnD,UAAUwD,IAAI,SAGjBX,EAAIG,GAAgBF,EAAS,IACtCS,EAAMvD,UAAUwD,IAAI,QACJ,OAAZL,GACFA,EAAQnD,UAAUwD,IAAI,UAGxBD,EAAMvD,UAAUM,OAAO,QACP,OAAZ6C,GACFA,EAAQnD,UAAUM,OAAO,QAG/B,CACF,EAEA,eAAAmD,GAEMnF,MAAMC,OAAOmF,YAAcpF,MAAMC,OAAOmF,WAAWC,aACrDxE,KAAKtB,UAAUmC,UAAUwD,IAAI,QAG/BrE,KAAKrB,aAAa+C,iBAAiB,SAAS,KAC1C1B,KAAKtB,UAAUmC,UAAUC,OAAO,OAAO,GAE3C,EAEA2D,gBAAiBxG,SAASC,cAAc,0BACxCwG,gBAAiBzG,SAASC,cAAc,2BACxC,oBAAAyG,GACE,MAAMC,EAAW5E,KAAKhC,cAChB6G,EAAiB7E,KAAKyE,gBACtBK,EAAkB9E,KAAK0E,gBAEvBK,EAAW9G,SAAS+G,YAAYC,iBACpChH,SAAS2F,MACTmB,SACIG,EAAeC,WAAWJ,GAEhC,IAAI/F,EAAgB,EACpB,MAAMoG,EAAc1H,EAAK2H,iBAMzB,SAASC,YAAYC,GACnB,MAAMR,EAAWG,GAAgB,EAAY,IAARK,GACrCX,EAASpE,MAAMuE,SAAW,GAAGA,MAC7BrH,EAAK0H,YAAYpG,cAAgBuG,EACjC7H,EAAK8H,gBACP,CAVIJ,IACFpG,EAAgBoG,EAAYpG,cAC5BsG,YAAYtG,IAoBd6F,EAAenD,iBAAiB,SAVhC,SAAS+D,mBACPzG,EAAgBqC,KAAKqE,IAAI1G,EAAgB,EAAG,GAC5CsG,YAAYtG,EACd,IAQA8F,EAAgBpD,iBAAiB,SANjC,SAASiE,mBACP3G,EAAgBqC,KAAKuE,IAAI5G,EAAgB,EAAG,GAC5CsG,YAAYtG,EACd,GAIF,EAEA,SAAA6G,GACE7F,KAAK8F,cAAgB7H,SAASC,cAAc,eACxC8B,KAAK8F,eACP9F,KAAK8F,cAAcpE,iBAAiB,SAAS,KAC3C,MAAMqE,EAAS9H,SAASC,cAAc,mBACtC,GAAI6H,EAAQ,CACV,MAAMC,EAASD,EAAOE,wBAAwBC,IAAMrH,OAAOsE,QAC3DtE,OAAOsH,SAAS,CACdD,IAAKF,EACLI,SAAU,UAEd,IAGN,EAGA,gBAAAC,CAAiBC,GACf,MAAMC,EAAMtI,SAASC,cAAcoI,GACnC,OAAOC,EAAMA,EAAIN,wBAAwBtC,OAAS,CACpD,EAGA,oBAAA6C,GACExG,KAAK3B,iBACF2B,KAAK3B,eAAemC,MAAMmD,OAAS3D,KAAKpB,YAAc,KAC3D,EAGA,oBAAA6H,GACE,GAAIzG,KAAK3B,eAAgB,OACzB,MAGMqI,EAHU1G,KAAKqG,iBAAiB,wBACtBrG,KAAKqG,iBAAiB,sBACtBrG,KAAKqG,iBAAiB,wBAEhCzH,EAAcC,OAAOD,YACrB+H,EAAS1I,SAASC,cAAc,wBACtC,GAAIwI,EAAe9H,EAAa,CAC9B,MAAMgI,EAAiBvF,KAAKwF,MAAMjI,EAAc8H,GAC5CE,EAAiB,IACnBD,EAAOnG,MAAMsG,UAAeF,EAAiB,EAApB,KAE7B,CACF,EAGAG,sBAAqB,CAACC,EAAIC,IACjBA,EAAGC,QAAQ,MAAOF,GAG3B,aAAAG,CAAcC,GACZ,MAAMC,EAAIC,SAEJC,EAAMlG,KAAKwF,MAAMO,EAAY,OAAsB,IACnDI,EAAMnG,KAAKwF,MAAMO,EAAY,QAC7BK,EAAMpG,KAAKwF,MAAMO,EAAY,MAAiB,GAC9CM,EAAMrG,KAAKwF,MAAMO,EAAY,OAC7BO,EAAMtG,KAAKwF,MAAOO,EAAY,KAAa,IAC3CQ,EAAMvG,KAAKwF,MAAOO,EAAY,GAAM,IACpCS,EAAMxG,KAAKwF,MAAMO,EAAY,IAEnC,OAAIG,EAAM,EACDvH,KAAK+G,sBAAsBQ,EAAKF,EAAES,MAChCN,EAAM,EACRxH,KAAK+G,sBAAsBS,EAAKH,EAAEU,OAChCN,EAAM,EACRzH,KAAK+G,sBAAsBU,EAAKJ,EAAEW,MAChCN,EAAM,EACR1H,KAAK+G,sBAAsBW,EAAKL,EAAEY,KAChCN,EAAM,EACR3H,KAAK+G,sBAAsBY,EAAKN,EAAEa,MAChCN,EAAM,EACR5H,KAAK+G,sBAAsBa,EAAKP,EAAEc,QAChCN,EAAM,EACR7H,KAAK+G,sBAAsBc,EAAKR,EAAEe,aADpC,CAGT,EAEA,kBAAAC,GACE,MAAMC,EAAOrK,SAASsK,iBACpB,8CAEIC,EAAKrJ,MAAMsJ,KAAKC,oBACX,aAAPF,EACFF,GACEA,EAAKK,SAASC,IACZ,MAAMC,EAAUC,KAAKC,MACfC,EAAW,IAAIF,KACnBF,EAAEK,QAAQC,KAAKC,MAAM,QAAQ,IAC7BC,UACFR,EAAE7H,UAAYf,KAAKmH,cACjB9F,KAAKwF,OAAOgC,EAAUG,GAAY,KACnC,IAEW,SAAPR,GACTF,GACEA,EAAKK,SAASC,IACZ,MAAMC,EAAUC,KAAKC,MACfC,EAAW,IAAIF,KACnBF,EAAEK,QAAQC,KAAKC,MAAM,QAAQ,IAC7BC,UACgB/H,KAAKwF,OACpBgC,EAAUG,GAAY,OAET,IACdJ,EAAE7H,UAAYf,KAAKmH,cACjB9F,KAAKwF,OAAOgC,EAAUG,GAAY,MAEtC,GAGR,GAGFjL,EAAM8D,sBAGN9D,EAAM0D,uBAGN1D,EAAMuG,kBAGNvG,EAAM4G,uBAGN5G,EAAM8H,YAGN9H,EAAM0I,uBAGN1I,EAAMyI,uBAGNzI,EAAMsK,qBAGN1K,GACF", "ignoreList": []}