<style>
    .home-article-item,
    .sidebar-links,
    .sidebar-content,
    a.page-number,
    a.extend,
    .sidebar-links .links:hover,
    .right-bottom-tools,
    footer.footer {
        background-color: var(--background-color-transparent-80) !important;
    }
    .right-bottom-tools:hover,
    a.page-number:hover,
    a.extend:hover {
        background-color: var(--primary-color) !important;
    }
    .site-info,
    .home-article-sticky-label {
        background-color: var(--background-color-transparent-15) !important;
    }
    .home-article-sticky-label {
        backdrop-filter: none !important;
    }
    .home-banner-background {
        filter: blur(15px);
    }
</style>

<div class="home-banner-background transition-fade fixed top-0 left-0 w-screen h-screen scale-125 sm:scale-110 box-border will-change-transform bg-cover">
    <img src="<%- url_for(theme.home_banner.image.light) %>" alt="home-banner-background" class="w-full h-full object-cover dark:hidden">
    <img src="<%- url_for(theme.home_banner.image.dark) %>" alt="home-banner-background" class="w-full h-full object-cover hidden dark:block">
</div>

