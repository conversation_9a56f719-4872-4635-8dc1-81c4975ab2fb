## db，可串行化



**可以把它想象成一个高效的厨师团队做菜：**

- **并发执行**：多个厨师同时在厨房里忙碌，有的在切菜，有的在炒菜，有的在准备调料。他们共享厨房空间和一些工具，操作是交织在一起的。
- **串行执行**：只有一个厨师，他必须做完一道菜的所有步骤，才能开始做下一道菜。效率会很低。
- **可串行化**：尽管多个厨师同时工作（并发），但他们之间有很好的协调（比如通过口头约定、流程单等，类似于数据库的并发控制机制）。最终，所有菜品完成的质量和顺序，看起来就好像是这些菜品被一个超级高效的厨师一道接一道做出来的一样。外面的顾客感受不到厨房内部的并行操作，只关心菜品是否正确以及上菜的某种合理顺序。



## C++有意思的点



`++it` 和 `it++`（`it` 为迭代器）：前者效率更高，因为前者是递增后然后直接使用这个变量，而后者则是返回旧值的拷贝再递增变量



重载方法：
```cpp
// Prefix ++it
// Increments it and returns a reference to same object
Iterator& operator++(T);

// Postfix it++
// Increments it and returns a copy of the old value
Iterator operator++();
```

