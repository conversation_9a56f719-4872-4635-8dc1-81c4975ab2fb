@require '../common/variables'

.home-content-container
  if (hexo-config('home.sidebar.enable') && hexo-config('home.sidebar.position') == 'right')
    margin-left $spacing-unit

    +redefine-tablet()
      margin-left 0

    +redefine-mobile()
      margin-left 0

  if (hexo-config('home.sidebar.enable') && hexo-config('home.sidebar.position') == 'left')
    margin-right $spacing-unit

    +redefine-tablet()
      margin-right 0

    +redefine-mobile()
      margin-right 0

  .seo-reader-text
    border 0
    clip rect(1px, 1px, 1px, 1px)
    -webkit-clip-path inset(50%)
    clip-path inset(50%)
    height 1px
    margin -1px
    overflow hidden
    padding 0
    position absolute !important
    width 1px
    word-wrap normal !important
    word-break normal

  .home-article-list
    .home-article-item
      position relative
      redefine-container(
        true,
        1.015,
        1.015,
        0px,
        38px
      )

      .home-article-thumbnail img
          width 100%
          height 100%
          object-fit cover
          position absolute
          top 50%
          transform translateY(-50%)

      .home-article-title
        position relative
        font-weight 600
        font-size 1.4rem
        line-height 1.5
        color var(--second-text-color)
        font-family $english-font-family, $chinese-font-family, 'Noto Sans SC', sans-serif

        +redefine-tablet()
          font-size 1.3rem

        +redefine-mobile()
          font-size 1.2rem

        margin 0

      .home-article-content
        word-wrap break-word
        text-align justify
        color var(--default-text-color)

      .home-article-meta-info-container
        display flex
        justify-content space-between
        align-items center
        font-size 0.92rem
        color var(--third-text-color)

        .home-article-meta-info
          letter-spacing 0.5px

          span
            margin-right 10px

            &:last-child
              margin-right 0

            ul, li
              display inline

          .home-article-category
            +redefine-mobile()
              display none

          .home-article-tag
            +redefine-tablet()
              display none

        hr
          border none
          flex 1
          height 1px
          background var(--border-color)
          margin 0 10px

        a
          color var(--third-text-color)

          &:hover
            color var(--primary-color)
