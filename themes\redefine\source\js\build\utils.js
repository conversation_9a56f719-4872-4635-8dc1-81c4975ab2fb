import{navbarShrink as e}from"./layouts/navbarShrink.js";import{initTOC as t}from"./layouts/toc.js";import{main as o}from"./main.js";import n from"./tools/imageViewer.js";export const navigationState={isNavigating:!1};export default function initUtils(){const i={html_root_dom:document.querySelector("html"),pageContainer_dom:document.querySelector(".page-container"),pageTop_dom:document.querySelector(".main-content-header"),homeBanner_dom:document.querySelector(".home-banner-container"),homeBannerBackground_dom:document.querySelector(".home-banner-background"),scrollProgressBar_dom:document.querySelector(".scroll-progress-bar"),pjaxProgressBar_dom:document.querySelector(".pjax-progress-bar"),backToTopButton_dom:document.querySelector(".tool-scroll-to-top"),toolsList:document.querySelector(".hidden-tools-list"),toggleButton:document.querySelector(".toggle-tools-list"),innerHeight:window.innerHeight,pjaxProgressBarTimer:null,prevScrollValue:0,fontSizeLevel:0,triggerViewHeight:.5*window.innerHeight,isHasScrollProgressBar:!0===theme.global.scroll_progress.bar,isHasScrollPercent:!0===theme.global.scroll_progress.percentage,updateScrollStyle(){const e=window.pageYOffset||document.documentElement.scrollTop,t=document.documentElement.scrollHeight,o=window.innerHeight||document.documentElement.clientHeight,n=this.calculatePercentage(e,t,o);this.updateScrollProgressBar(n),this.updateScrollPercent(n),this.updatePageTopVisibility(e,o),this.prevScrollValue=e},updateScrollProgressBar(e){if(this.isHasScrollProgressBar){const t=e.toFixed(3),o=0===e?"hidden":"visible";this.scrollProgressBar_dom.style.visibility=o,this.scrollProgressBar_dom.style.width=`${t}%`}},updateScrollPercent(e){if(this.isHasScrollPercent){const t=this.backToTopButton_dom.querySelector(".percent"),o=0!==e&&void 0!==e;this.backToTopButton_dom.classList.toggle("show",o),t.innerHTML=e.toFixed(0)}},updatePageTopVisibility(e,t){if(theme.navbar.auto_hide){const o=this.prevScrollValue,n=o>t&&e>o;this.pageTop_dom.classList.toggle("hide",n)}else this.pageTop_dom.classList.remove("hide")},calculatePercentage(e,t,o){let n=Math.round(e/(t-o)*100);return isNaN(n)||n<0||!isFinite(n)?n=0:n>100&&(n=100),n},registerWindowScroll(){window.addEventListener("scroll",(()=>{this.updateScrollStyle(),this.updateTOCScroll(),this.updateNavbarShrink(),this.updateAutoHideTools()})),window.addEventListener("scroll",this.debounce((()=>this.updateHomeBannerBlur()),20))},updateTOCScroll(){theme.articles.toc.enable&&t().hasOwnProperty("updateActiveTOCLink")&&t().updateActiveTOCLink()},updateNavbarShrink(){navigationState.isNavigating||e.init()},debounce(e,t){let o;return function(){clearTimeout(o),o=setTimeout((()=>e.apply(this,arguments)),t)}},updateHomeBannerBlur(){if(this.homeBannerBackground_dom&&"fixed"===theme.home_banner.style&&location.pathname===config.root){const e=(window.scrollY||window.pageYOffset)>=this.triggerViewHeight?15:0;try{requestAnimationFrame((()=>{this.homeBannerBackground_dom.style.filter=`blur(${e}px)`,this.homeBannerBackground_dom.style.webkitFilter=`blur(${e}px)`}))}catch(e){console.error("Error updating banner blur:",e)}}},updateAutoHideTools(){const e=window.scrollY,t=document.body.scrollHeight,o=window.innerHeight,n=document.getElementsByClassName("right-side-tools-container"),i=document.getElementById("aplayer");for(let r=0;r<n.length;r++){const s=n[r];e<=100?location.pathname===config.root&&(s.classList.add("hide"),null!==i&&i.classList.add("hide")):e+o>=t-20?(s.classList.add("hide"),null!==i&&i.classList.add("hide")):(s.classList.remove("hide"),null!==i&&i.classList.remove("hide"))}},toggleToolsList(){theme.global.side_tools&&theme.global.side_tools.auto_expand&&this.toolsList.classList.add("show"),this.toggleButton.addEventListener("click",(()=>{this.toolsList.classList.toggle("show")}))},fontAdjPlus_dom:document.querySelector(".tool-font-adjust-plus"),fontAdMinus_dom:document.querySelector(".tool-font-adjust-minus"),globalFontSizeAdjust(){const e=this.html_root_dom,t=this.fontAdjPlus_dom,n=this.fontAdMinus_dom,i=document.defaultView.getComputedStyle(document.body).fontSize,r=parseFloat(i);let s=0;const l=o.getStyleStatus();function setFontSize(t){const n=r*(1+.05*t);e.style.fontSize=`${n}px`,o.styleStatus.fontSizeLevel=t,o.setStyleStatus()}l&&(s=l.fontSizeLevel,setFontSize(s)),t.addEventListener("click",(function increaseFontSize(){s=Math.min(s+1,5),setFontSize(s)})),n.addEventListener("click",(function decreaseFontSize(){s=Math.max(s-1,0),setFontSize(s)}))},goComment(){this.goComment_dom=document.querySelector(".go-comment"),this.goComment_dom&&this.goComment_dom.addEventListener("click",(()=>{const e=document.querySelector("#comment-anchor");if(e){const t=e.getBoundingClientRect().top+window.scrollY;window.scrollTo({top:t,behavior:"smooth"})}}))},getElementHeight(e){const t=document.querySelector(e);return t?t.getBoundingClientRect().height:0},inithomeBannerHeight(){this.homeBanner_dom&&(this.homeBanner_dom.style.height=this.innerHeight+"px")},initPageHeightHandle(){if(this.homeBanner_dom)return;const e=this.getElementHeight(".main-content-header")+this.getElementHeight(".main-content-body")+this.getElementHeight(".main-content-footer"),t=window.innerHeight,o=document.querySelector(".main-content-footer");if(e<t){const n=Math.floor(t-e);n>0&&(o.style.marginTop=n-2+"px")}},setHowLongAgoLanguage:(e,t)=>t.replace(/%s/g,e),getHowLongAgo(e){const t=lang_ago,o=Math.floor(e/2592e3/12),n=Math.floor(e/2592e3),i=Math.floor(e/86400/7),r=Math.floor(e/86400),s=Math.floor(e/3600%24),l=Math.floor(e/60%60),a=Math.floor(e%60);return o>0?this.setHowLongAgoLanguage(o,t.year):n>0?this.setHowLongAgoLanguage(n,t.month):i>0?this.setHowLongAgoLanguage(i,t.week):r>0?this.setHowLongAgoLanguage(r,t.day):s>0?this.setHowLongAgoLanguage(s,t.hour):l>0?this.setHowLongAgoLanguage(l,t.minute):a>0?this.setHowLongAgoLanguage(a,t.second):void 0},relativeTimeInHome(){const e=document.querySelectorAll(".home-article-meta-info .home-article-date"),t=theme.home.article_date_format;"relative"===t?e&&e.forEach((e=>{const t=Date.now(),o=new Date(e.dataset.date.split(" GMT")[0]).getTime();e.innerHTML=this.getHowLongAgo(Math.floor((t-o)/1e3))})):"auto"===t&&e&&e.forEach((e=>{const t=Date.now(),o=new Date(e.dataset.date.split(" GMT")[0]).getTime();Math.floor((t-o)/864e5)<7&&(e.innerHTML=this.getHowLongAgo(Math.floor((t-o)/1e3)))}))}};i.updateAutoHideTools(),i.registerWindowScroll(),i.toggleToolsList(),i.globalFontSizeAdjust(),i.goComment(),i.initPageHeightHandle(),i.inithomeBannerHeight(),i.relativeTimeInHome(),n()}
//# sourceMappingURL=utils.js.map