$aplayer-height = 66px
$lrc-height = 30px
$aplayer-height-lrc= $aplayer-height + $lrc-height - 6

@require '../../common/variables'

.aplayer {
  //font-family Arial, Helvetica, sans-serif
  margin 5px
  border-radius $redefine-border-radius-medium
  overflow hidden
  user-select none
  line-height initial
  position relative
  transition opacity 0.3s ease

  +redefine-mobile() {
    display none
  }

  &.hide {
    opacity 0
    transition opacity 0.3s ease
    pointer-events none
  }

  * {
      box-sizing content-box
  }

  svg {
      width 100%
      height 100%
      
      path,
      circle {
          fill #fff
      }
  }

  &.aplayer-withlist {
    .aplayer-info {
        border-bottom 1px solid #e9e9e9
    }
    .aplayer-list {
        display block
    }
    .aplayer-info .aplayer-controller .aplayer-time .aplayer-icon.aplayer-icon-menu {
        display inline
    }
    .aplayer-icon-order {
        display inline
    }
  }

  &.aplayer-withlrc {
    .aplayer-pic {
      height $aplayer-height-lrc
      width $aplayer-height-lrc
    }
    .aplayer-info {
      margin-left $aplayer-height-lrc
      height $aplayer-height-lrc
      padding 10px 7px 0 7px
    }
    .aplayer-lrc {
      display block
    }
  }

  &.aplayer-narrow {
    width $aplayer-height
    
    .aplayer-info {
      display none
    }
    .aplayer-list {
      display none
    }
    .aplayer-pic,
    .aplayer-body {

    }
  }

  &.aplayer-fixed {
    position fixed
    bottom $spacing-unit * 0.8
    left $spacing-unit * 0.8
    right 0
    margin 0
    z-index 99
    overflow-y visible
    max-width 400px
    border-radius $redefine-border-radius-medium

    .aplayer-list {
      margin-bottom 75px
      border 1px solid var(--shadow-color-1)
    }

    .aplayer-body {
      position fixed
      bottom $spacing-unit * 0.8
      left $spacing-unit * 0.8
      right 0
      margin 0
      z-index 99
      padding-right 18px
      transition all 0.3s ease
      max-width 400px
      border-radius $redefine-border-radius-medium
    }

    .aplayer-lrc {
      display block
      position fixed
      bottom 10px
      left 0
      right 0
      margin 0
      z-index 98
      pointer-events none
      text-shadow -1px -1px 0 #fff

      &before,
      &after {
          display none
      }
    }

    .aplayer-info {
      transform scaleX(1)
      transform-origin 0 0
      transition all 0.3s ease
      border 1px solid var(--shadow-color-1)
      white-space nowrap
      //box-shadow var(--redefine-box-shadow)
      border-radius $redefine-border-radius-medium
      background var(--background-color-transparent)
      backdrop-filter blur(10px)
      -webkit-backdrop-filter blur(10px)

      .aplayer-music {
        width calc(100% - 105px)
      }
    }

    .aplayer-miniswitcher {
      display block
      opacity 0
      transition opacity 0.3s ease
    }

    &.aplayer-narrow {
        .aplayer-info {
          display block
          transform scaleX(0)
        }
        .aplayer-body {
          width $aplayer-height !important
        }

        .aplayer-miniswitcher .aplayer-icon {
          transform rotateY(0)
        }
    }

    .aplayer-icon-back,
    .aplayer-icon-play,
    .aplayer-icon-forward,
    .aplayer-icon-lrc {
      display inline-block
    }

    .aplayer-icon-back,
    .aplayer-icon-play,
    .aplayer-icon-forward,
    .aplayer-icon-menu {
      position absolute
      bottom 27px
      width 20px
      height 20px
    }

    .aplayer-icon-back {
      right 75px
    }

    .aplayer-icon-play {
      right 50px
    }

    .aplayer-icon-forward {
      right 25px
    }

    .aplayer-icon-menu {
      right 0
    }
  }

  &.aplayer-mobile {
    .aplayer-icon-volume-down {
      display none
    }
  }

  &.aplayer-arrow {
    .aplayer-icon-order,
    .aplayer-icon-loop {
      display none
    }
  }

  &.aplayer-loading {
    .aplayer-info .aplayer-controller .aplayer-loading-icon {
      display block
    }

    .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb {
      transform scale(1)
    }
  }

  .aplayer-body {
    position relative
  }

  .aplayer-icon {
    width 15px
    height 15px
    border none
    background-color transparent
    outline none
    cursor pointer
    opacity .8
    vertical-align middle
    padding 0
    font-size 12px
    margin 0
    display inline-block

    path {
      transition all .2s ease-in-out
    }
  }

  .aplayer-icon-order,
  .aplayer-icon-back,
  .aplayer-icon-play,
  .aplayer-icon-forward,
  .aplayer-icon-lrc {
    display none
  }

  .aplayer-icon-lrc-inactivity {
    svg {
      opacity 0.4
    }
  }

  .aplayer-icon-forward {
    transform rotate(180deg)
  }

  .aplayer-lrc-content {
    display none
  }

  .aplayer-pic {
    position relative
    float left
    height $aplayer-height
    width $aplayer-height
    background-size cover
    background-position center
    transition all 0.3s ease
    cursor pointer
    border 1px solid var(--shadow-color-1)
    border-radius $redefine-border-radius-medium

    &hover .aplayer-button {
        opacity 1
    }

    .aplayer-button {
      position absolute
      border-radius 50%
      opacity 0.8
      text-shadow 0 1px 1px rgba(0, 0, 0, 0.2)
      box-shadow 0 1px 1px rgba(0, 0, 0, 0.2)
      background rgba(0, 0, 0, 0.2)
      transition all 0.1s ease

      path {
        fill #fff
      }
    }

    .aplayer-hide {
      display none
    }

    .aplayer-play {
      width 26px
      height 26px
      border 2px solid #fff
      bottom 50%
      right 50%
      margin 0 -15px -15px 0
      svg {
        position absolute
        top 3px
        left 4px
        height 20px
        width 20px
      }
    }

    .aplayer-pause {
      width 16px
      height 16px
      border 2px solid #fff
      bottom 4px
      right 4px
      svg {
        position absolute
        top 2px
        left 2px
        height 12px
        width 12px
      }
    }
  }

  .aplayer-info {
      margin-left $aplayer-height + 10px
      padding 14px 7px 0 10px
      height $aplayer-height + 2px
      box-sizing border-box

      .aplayer-music {
          overflow hidden
          white-space nowrap
          text-overflow ellipsis
          margin 0 0 13px 5px
          user-select text
          cursor default
          padding-bottom 2px
          height 20px

          .aplayer-title {
              font-size 14px
          }

          .aplayer-author {
              font-size 12px
              color #666
          }
      }

      .aplayer-controller {
          position relative
          display flex

          .aplayer-bar-wrap {
              margin 0 0 0 5px
              padding 4px 0
              cursor pointer !important
              flex 1

              &hover {
                  .aplayer-bar .aplayer-played .aplayer-thumb {
                      transform scale(1)
                  }
              }

              .aplayer-bar {
                  position relative
                  height 2px
                  width 100%
                  background #cdcdcd

                  .aplayer-loaded {
                      position absolute
                      left 0
                      top 0
                      bottom 0
                      background #aaa
                      height 2px
                      transition all 0.5s ease
                  }

                  .aplayer-played {
                      position absolute
                      left 0
                      top 0
                      bottom 0
                      height 2px

                      .aplayer-thumb {
                          display none
                          position absolute
                          top 0
                          right 5px
                          margin-top -4px
                          margin-right -10px
                          height 10px
                          width 10px
                          border-radius 50%
                          cursor pointer
                          transition all .3s ease-in-out
                          transform scale(0)
                      }
                  }
              }
          }

          .aplayer-time {
              position relative
              right 0
              bottom 4px
              height 17px
              color #999
              font-size 11px
              padding-left 7px

              .aplayer-time-inner {
                  vertical-align middle
              }

              .aplayer-icon {
                  cursor pointer
                  transition all 0.2s ease

                  path {
                      fill #666
                  }

                  &.aplayer-icon-loop {
                      margin-right 2px
                  }

                  &hover {
                      path {
                          fill #000
                      }
                  }

                  &.aplayer-icon-menu {
                      display none
                  }
              }

              &.aplayer-time-narrow {
                  .aplayer-icon-mode {
                      display none
                  }

                  .aplayer-icon-menu {
                      display none
                  }
              }
          }

          .aplayer-volume-wrap {
              position relative
              display inline-block
              margin-left 3px
              cursor pointer !important

              &hover .aplayer-volume-bar-wrap {
                  height 40px
              }

              .aplayer-volume-bar-wrap {
                  position absolute
                  bottom 15px
                  right -3px
                  width 25px
                  height 0
                  z-index 99
                  overflow hidden
                  transition all .2s ease-in-out

                  &.aplayer-volume-bar-wrap-active {
                      height 40px
                  }

                  .aplayer-volume-bar {
                      position absolute
                      bottom 0
                      right 10px
                      width 5px
                      height 35px
                      background #aaa
                      border-radius 2.5px
                      overflow hidden

                      .aplayer-volume {
                          position absolute
                          bottom 0
                          right 0
                          width 5px
                          transition all 0.1s ease
                      }
                  }
              }
          }

          .aplayer-loading-icon {
              display none

              svg {
                  position absolute
                  animation rotate 1s linear infinite
              }
          }
      }
  }

  .aplayer-lrc {
      display none
      position relative
      height $lrc-height
      text-align center
      overflow hidden
      margin -10px 0 7px

      &before {
          position absolute
          top 0
          z-index 1
          display block
          overflow hidden
          width 100%
          height 10%
          content ' '
          background -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%)
          background -webkit-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%)
          background linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(255,255,255,0) 100%)
      }

      &after {
          position absolute
          bottom 0
          z-index 1
          display block
          overflow hidden
          width 100%
          height 33%
          content ' '
          background -moz-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 100%)
          background -webkit-linear-gradient(top, rgba(255,255,255,0) 0%,rgba(255,255,255,0.8) 100%)
          background linear-gradient(to bottom, rgba(255,255,255,0) 0%,rgba(255,255,255,0.8) 100%)
      }

      p {
          font-size 12px
          color #666
          line-height 16px !important
          height 16px !important
          padding 0 !important
          margin 0 !important
          transition all 0.5s ease-out
          opacity 0.4
          overflow hidden

          &.aplayer-lrc-current {
              opacity 1
              overflow visible
              height initial !important
              min-height 16px
          }
      }

      &.aplayer-lrc-hide {
          display none
      }

      .aplayer-lrc-contents {
          width 100%
          transition all 0.5s ease-out
          user-select text
          cursor default
      }
  }

  .aplayer-list {
    overflow auto
    transition all 0.5s ease
    will-change height
    display none
    overflow hidden
    list-style-type none
    margin 0
    padding 5px
    overflow-y auto
    border-radius $redefine-border-radius-medium
    background var(--background-color-transparent)
    backdrop-filter blur(10px)
    -webkit-backdrop-filter blur(10px)


    &-webkit-scrollbar {
        width 5px
    }

    &-webkit-scrollbar-thumb {
        border-radius 3px
        background-color var(--scroll-bar-bg-color)
    }

    &-webkit-scrollbar-thumbhover {
        background-color var(--scroll-bar-bg-color-hover)
    }

    li {
        position relative
        height 32px
        line-height 32px
        padding 0 15px
        font-size 12px
        //border-top 1px solid #e9e9e9
        cursor pointer
        transition all 0.2s ease
        overflow hidden
        margin 0
        border-radius $redefine-border-radius-small

        &first-child {
            border-top none
        }

        &hover {
            background #efefef
        }

        &.aplayer-list-light {
            background var(--third-background-color-transparent)

            .aplayer-list-cur {
                display inline-block
            }
        }

        .aplayer-list-cur {
            display none
            width 3px
            height 18px
            position absolute
            left 5px
            top 7px
            cursor pointer
            border-radius 2px
        }
        .aplayer-list-index {
            color #666
            margin-right 12px
            cursor pointer
        }
        .aplayer-list-author {
            color #666
            float right
            cursor pointer
        }
    }
  }

  .aplayer-list-hide {
      display none !important
  }

  .aplayer-notice {
      opacity 0
      position absolute
      top 50%
      left 50%
      transform translate(-50%, -50%)
      font-size 12px
      border-radius 4px
      padding 5px 10px
      transition all .3s ease-in-out
      overflow hidden
      color #fff
      pointer-events none
      background-color #f4f4f5
      color #909399
  }

  .aplayer-miniswitcher {
      display none
      position absolute
      top 0px
      right 0
      bottom 0px
      height calc(100% - 2px)
      background var(--third-background-color-transparent)
      border 1px solid var(--shadow-color-1)
      width 18px
      border-radius $redefine-border-radius-medium
      backdrop-filter blur(10px)
      -webkit-backdrop-filter blur(10px)

      .aplayer-icon {
          height 100%
          width 100%
          transform rotateY(180deg)
          transition all 0.3s ease

          path {
              fill #666
          }

          &hover {
              path {
                  fill #000
              }
          }
      }
  }

  &:hover .aplayer-miniswitcher {
    opacity 1
    transition opacity 0.3s ease
  }

  &:hover .aplayer-body {
    padding-right 28px
    transition padding-right 0.3s ease
  }
}

@keyframes aplayer-roll {
  0%{
    left 0
  }
  100%{
    left -100%
  }
}

@keyframes rotate {
  0% {
    transform rotate(0)
  }
  100% {
    transform rotate(360deg)
  }
}

if (hexo-config('plugins.aplayer.enable') == true && hexo-config('plugins.aplayer.type') == "mini") {
  .aplayer {
    position fixed
    bottom 46px
    right 39px
    margin 0
    z-index 99
    overflow-y hidden
    max-width 400px
    width $right-bottom-tools-width - 2px !important
    height $right-bottom-tools-width - 2px !important
    border-radius $right-bottom-tools-border-radius - 0.8px !important

    +redefine-tablet() {
      display none
    }

    +redefine-mobile() {
      display none
    }

  }


  .aplayer-pic {
    position fixed !important
    width $right-bottom-tools-width - 4px !important
    height $right-bottom-tools-width - 4px !important
    border-radius $right-bottom-tools-border-radius - 0.8px !important
  }
  .aplayer-play {
    width: 13px !important
    height: 13px !important
    border: 2px solid #fff !important
    bottom: 50% !important
    right: 50% !important
    margin: 0 -9px -9px 0 !important
  }
  .aplayer-play svg {
    position: absolute !important
    top 0px !important
    left: 1px !important
    height: 13px  !important
    width: 13px !important
  }

}
