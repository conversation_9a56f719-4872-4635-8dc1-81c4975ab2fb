.progress-bar-container
    position fixed
    top 0
    left 0
    width 100%
    z-index $z-index-9

    if (hexo-config('global.scroll_progress.bar') == true)
        .scroll-progress-bar
            position absolute
            top 0
            left 0
            width 0
            height $scroll-progress-bar-height
            visibility hidden
            z-index $z-index-7
            background var(--primary-color)
            transition-t('width, opacity', '0, 0', '0.1, 0.1', 'ease, ease')

            &.hide
                display none !important

html.is-animating
    .scroll-progress-bar
        opacity 0
        transition opacity 0.1s ease-out



.swup-progress-bar
    background: #12c2e9;
    height 2px
    background linear-gradient(to right, #12c2e9, #c471ed, #f64f59);