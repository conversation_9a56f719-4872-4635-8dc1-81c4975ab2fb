{"version": 3, "file": "typed.js", "names": ["config", "usrTypeSpeed", "theme", "home_banner", "subtitle", "typing_speed", "usrBackSpeed", "backing_speed", "usrBackDelay", "backing_delay", "usrStartDelay", "starting_delay", "usr<PERSON><PERSON>", "loop", "usrSmartBackspace", "smart_backspace", "usrHitokotoAPI", "<PERSON><PERSON><PERSON>", "api", "initTyped", "id", "typing", "dataList", "Typed", "strings", "typeSpeed", "smartBackspace", "backSpeed", "<PERSON><PERSON><PERSON><PERSON>", "startDelay", "enable", "fetch", "then", "response", "json", "data", "from_who", "show_author", "catch", "console", "error", "sentenceList", "text", "document", "getElementById"], "sources": ["0"], "mappings": "OAGO,MAAMA,OAAS,CACpBC,aAAcC,MAAMC,YAAYC,SAASC,aACzCC,aAAcJ,MAAMC,YAAYC,SAASG,cACzCC,aAAcN,MAAMC,YAAYC,SAASK,cACzCC,cAAeR,MAAMC,YAAYC,SAASO,eAC1CC,QAASV,MAAMC,YAAYC,SAASS,KACpCC,kBAAmBZ,MAAMC,YAAYC,SAASW,gBAC9CC,eAAgBd,MAAMC,YAAYC,SAASa,SAASC,oBAGvC,SAASC,UAAUC,GAChC,MAAMnB,aACJA,EAAYK,aACZA,EAAYE,aACZA,EAAYE,cACZA,EAAaE,QACbA,EAAOE,kBACPA,EAAiBE,eACjBA,GACEhB,OAEJ,SAASqB,OAAOC,GACH,IAAIC,MAAM,IAAMH,EAAI,CAC7BI,QAAS,CAACF,GACVG,UAAWxB,GAAgB,IAC3ByB,eAAgBZ,IAAqB,EACrCa,UAAWrB,GAAgB,GAC3BsB,UAAWpB,GAAgB,KAC3BK,KAAMD,IAAW,EACjBiB,WAAYnB,GAAiB,KAEjC,CAEA,GAAIR,MAAMC,YAAYC,SAASa,SAASa,OACtCC,MAAMf,GACHgB,MAAMC,GAAaA,EAASC,SAC5BF,MAAMG,IACDA,EAAKC,UAAYlC,MAAMC,YAAYC,SAASa,SAASoB,YACvDhB,OAAOc,EAAKlB,SAAW,KAAOkB,EAAKC,UAEnCf,OAAOc,EAAKlB,SACd,IAEDqB,MAAMC,QAAQC,WACZ,CACL,MAAMC,EAAe,IAAIvC,MAAMC,YAAYC,SAASsC,MACpD,GAAIC,SAASC,eAAexB,GAAK,CACpB,IAAIG,MAAM,IAAMH,EAAI,CAC7BI,QAASiB,EACThB,UAAWxB,GAAgB,IAC3ByB,eAAgBZ,IAAqB,EACrCa,UAAWrB,GAAgB,GAC3BsB,UAAWpB,GAAgB,KAC3BK,KAAMD,IAAW,EACjBiB,WAAYnB,GAAiB,KAEjC,CACF,CACF", "ignoreList": []}