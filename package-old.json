{"name": "hexo-site", "version": "0.0.0", "private": true, "scripts": {"build": "hexo generate", "clean": "hexo clean", "deploy": "hexo deploy", "server": "hexo server"}, "hexo": {"version": "7.3.0"}, "dependencies": {"eslint": "^9.26.0", "glob-promise": "^6.0.7", "hexo": "^7.3.0", "hexo-all-minifier": "^0.5.7", "hexo-deployer-git": "^4.0.0", "hexo-generator-archive": "^2.0.0", "hexo-generator-category": "^2.0.0", "hexo-generator-index": "^4.0.0", "hexo-generator-searchdb": "^1.5.0", "hexo-generator-tag": "^2.0.0", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^7.0.0", "hexo-renderer-stylus": "^3.0.1", "hexo-server": "^3.0.0", "hexo-theme-landscape": "^1.0.0", "hexo-theme-redefine": "^2.8.2", "hexo-wordcount": "^6.0.1", "husky": "^9.1.6", "js-yaml": "^4.1.0", "nodejieba": "^3.4.4", "postcss": "^8.4.47", "standard-version": "^9.5.0", "tailwindcss": "^3.4.14", "terser": "^5.36.0"}}