@require 'code-theme.styl'

$code-block
  -webkit-text-size-adjust none
  overflow auto
  margin 20px 0
  padding 0
  font-size 1rem
  line-height 1.5rem
  color var(--highlight-foreground)
  background var(--highlight-background)

pre, code
  line-height 1.5 !important
  if (hexo-config('articles.code_block.font.enable') && hexo-config('articles.code_block.font.family'))
    font-family hexo-config('articles.code_block.font.family'), '<PERSON>eist Mono', Courier, monospace !important
  else
    font-family 'Geist Mono', Courier, monospace !important

pre.mermaid
  background-color var(--background-color)

  svg
    display block
    margin auto

code
  padding 3px 4px
  word-wrap break-word
  border-radius $redefine-border-radius-xsmall
  color var(--inline-code-foreground)
  background var(--inline-code-background)

.main-content code
  font-size: 0.9em

pre
  @extend $code-block
  font-size 0.9rem !important
  padding-top 14px !important
  tab-size 4

  code
    padding 0
    color var(--highlight-foreground)
    background none
    text-shadow none

.highlight
  @extend $code-block
  border-radius $redefine-border-radius-large-bottom
  padding-bottom 5px
  overflow auto
  margin 0 !important

  if (hexo-config('articles.code_block.style') == 'mac')
    border-top 1px solid var(--shadow-color-1)

  &:hover::-webkit-scrollbar-thumb
    border 3px solid rgba(183, 183, 183, 0.3)

  &::-webkit-scrollbar
    width 6px
    height 6px
    transition all 0.2s ease
    display block

  &::-webkit-scrollbar-track
    background transparent
    border none

  &::-webkit-scrollbar-thumb
    border-radius 20px
    border 3px solid rgba(183, 183, 183, 0)
    transition all 0.2s ease

  pre
    border none
    margin 0
    padding 10px 0

  table
    margin 0
    width auto
    border none
    border-spacing unset

  td
    border none
    padding 0

  figcaption
    font-size 1rem
    color var(--highlight-foreground)
    line-height 1rem
    margin-bottom 1rem

    a
      float right
      color var(--highlight-foreground)

      &:hover
        border-bottom-color var(--highlight-foreground)

  .gutter pre
    padding-left 10px
    padding-right 10px
    color var(--highlight-gutter-color)
    text-align center
    background-color var(--highlight-gutter-bg-color)

  .code pre
    width 100%
    padding-left 10px
    padding-right 10px
    background-color var(--highlight-background)

.gutter
  -webkit-user-select none
  -moz-user-select none
  -ms-user-select none
  user-select none

.gist table
  width auto

  td
    border none

pre
  .keyword, .literal, .symbol, .name
    color var(--highlight-keyword)

  .link
    color var(--highlight-link)
    text-decoration underline

  .built_in, .type
    color var(--highlight-built-in)

  .number, .class
    color var(--highlight-number)

  .string, .meta .string
    color var(--highlight-string)

  .regexp, .template-tag
    color var(--highlight-regexp)

  .subst, .function, .title, .params, .formula
    color var(--highlight-subst)

  .comment, .quote
    color var(--highlight-comment)
    font-style italic

  .doctag
    color var(--highlight-doctag)

  .meta, .meta .keyword, .tag
    color var(--highlight-meta)

  .variable, .template-variable
    color var(--highlight-variable)

  .attr, .attribute
    color var(--highlight-attr)

  .section
    color var(--highlight-section)

  .emphasis
    font-style italic

  .strong
    font-weight bold

  .bullet, .selector-tag, .selector-id, .selector-class, .selector-attr, .selector-pseudo
    color var(--highlight-bullet)

  .addition
    color var(--highlight-addition)
    display inline-block
    width 100%

  .deletion
    color var(--highlight-deletion)
    display inline-block
    width 100%
