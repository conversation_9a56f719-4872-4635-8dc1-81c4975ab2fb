$icon-size = 1.2rem

@require '../../common/variables'

.search-pop-overlay
  position fixed
  display flex
  height 100%
  width 100%
  left 0
  top 0
  background rgba(0, 0, 0, 0)
  visibility hidden
  z-index $z-index-8
  transition-t('visibility, background', '0, 0', '0.3, 0.3', 'ease, ease')

  &.active
    visibility visible
    background rgba(0, 0, 0, 0.35)

    .search-popup
      transform scale(1)

  .search-popup
    background var(--background-color)
    border-radius 5px
    height 80%
    width 70%
    margin auto
    transform scale(0)
    z-index $z-index-6
    transition-t('transform', '0', '0.3', 'ease')

    +redefine-tablet()
      width 80%

    +redefine-mobile()
      width 90%

    .search-header
      background var(--fourth-text-color)
      border-top-left-radius 2px
      border-top-right-radius 2px
      display flex
      padding 10px

      .search-input-field-pre, .popup-btn-close
        font-size $icon-size
        padding 0 10px
        display flex
        align-items center

      .search-input-field-pre
        cursor pointer
        color var(--third-text-color)

      .popup-btn-close
        color var(--default-text-color)

      .search-input-container
        flex-grow 1
        padding 2px

        .search-input
          background transparent
          border 0
          outline 0
          width 100%
          font-size 1.2rem
          color var(--default-text-color)

          &::-webkit-search-cancel-button
            display none

      .popup-btn-close
        cursor pointer

        &:hover .fas, far, fab
          color var(--first-text-color)

    #search-result
      display flex
      height calc(100% - 55px)
      overflow auto
      padding 5px 25px

      .search-result-list
        width 100%
        height 100%
        font-size 1rem

        li
          border-bottom 1px dashed var(--border-color)
          padding 10px 0
          margin 10px 0
          box-sizing border-box

          &:last-child
            border-bottom none

          .search-result-title
            position relative
            font-weight bold
            margin-bottom 10px
            padding-left 16px
            display flex
            align-items center

            &::after
              content ''
              position absolute
              width 5px
              height 5px
              border-radius $redefine-border-radius-full
              top 50%
              transform translateY(-50%)
              left 0
              background var(--default-text-color)

          .search-result
            line-height 2rem
            margin 0
            padding-left 16px
            word-wrap break-word

          a
            &:hover
              color var(--default-text-color)

          .search-keyword
            border-bottom 1px dashed var(--primary-color)
            color var(--primary-color)
            font-weight bold

      #no-result
        color var(--third-text-color)
        margin auto
