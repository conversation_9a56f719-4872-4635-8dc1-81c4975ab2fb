name: Bug 提交 [中文版本]
description: 提交 Redefine 主题的 Bug 报告
title: "[BUG] "
labels: ["bug"]
assignees: ["EvanNotFound"]
body:
  - type: markdown
    attributes:
      value: |
        感谢您提交 Bug 报告！请填写以下信息以帮助我们更好地解决问题。

  - type: checkboxes
    id: pre-check
    attributes:
      label: 提交前检查
      options:
        - label: 我已执行 `hexo clean` 并重新生成，问题仍然存在
          required: true
        - label: 我已更新到[最新版本](https://redefine-docs.ohevan.com/getting-started#%E6%9B%B4%E6%96%B0)
          required: true
        - label: 我已确认[同步了主题最新配置文件](https://redefine-docs.ohevan.com/getting-started#%E8%BF%81%E7%A7%BB%E9%85%8D%E7%BD%AE)
          required: true

  - type: textarea
    id: bug-description
    attributes:
      label: Bug 描述
      description: 请详细描述您遇到的问题，并提供相关配置文件内容（敏感信息请用 *** 替换）
      placeholder: |
        问题描述：
        
        相关配置：        
        ```yaml
        # 在此粘贴配置        
        ```
    validations:
      required: true

  - type: textarea
    id: reproduce
    attributes:
      label: 复现步骤
      value: |
        1. 
        2. 
        3. 
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: 预期行为
      description: 请描述正常情况下应该是什么样的

  - type: textarea
    id: screenshots
    attributes:
      label: 截图
      description: 如果可以，请提供截图以帮助说明问题

  - type: textarea
    id: logs
    attributes:
      label: 相关日志输出
      description: 请复制并粘贴任何相关的日志输出。这将自动格式化为代码，因此不需要反引号。
      render: shell

  - type: dropdown
    id: browsers
    attributes:
      label: 使用的浏览器
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - 其他

  - type: dropdown
    id: os
    attributes:
      label: 操作系统
      options:
        - Windows
        - macOS
        - Linux
        - iOS
        - Android
        - 其他

  - type: input
    id: version
    attributes:
      label: 浏览器版本
      placeholder: "例如：Chrome 108.0.5359.124"

  - type: dropdown
    id: priority
    validations:
      required: true
    attributes:
      label: 优先级
      description: 请根据问题的严重程度选择优先级
      options:
        - 低（慢慢来）
        - 中（尽快解决）
        - 高（需立即解决）

  - type: textarea
    id: additional-info
    attributes:
      label: 其他信息
      placeholder: 还有什么需要补充的信息吗？
