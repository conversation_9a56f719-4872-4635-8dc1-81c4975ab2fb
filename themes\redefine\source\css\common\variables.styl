@require './theme.styl'

// ========================================================================================
//                                   REDEFINE THEME VARIABLE                                 
// ========================================================================================


// ========================================================================================
//                                           LAYOUT
// ========================================================================================
$navbar-height = 70px;                                           // navbar height
$navbar-shrink-height = $navbar-height * 0.72;                   // navbar shrink height
$scroll-progress-bar-height = 2px;                               // scroll progress bar height
$main-content-width = 80%;                                       // main content width (PC)
$main-content-width-tablet = 86%;                                // main content width (tablet)
$main-content-width-mobile = 90%;                                // main content width (mobile)
$post-tool-button-width = 38px;                                  // post tool button width
$spacing-unit = 38px;                                 // component-spacing-value (PC)

// main content max width
$temp-content-max-width = hexo-config('global.content_max_width')
$content-max-width = $temp-content-max-width ? convert($temp-content-max-width) : 1000px
$has-toc-content-max-width = $content-max-width * 1.2

$navbar-width-home-data = hexo-config('navbar.width.home')
$navbar-width-home = $navbar-width-home-data ? convert($navbar-width-home-data) : 1200px
$navbar-width-pages-data = hexo-config('navbar.width.pages')
$navbar-width-pages = $navbar-width-pages-data ? convert($navbar-width-pages-data) : 1000px


// ========================================================================================
//                                         TITLES
// ========================================================================================

$h1-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h1')
$h1-heading-margin-top = $h1-heading-margin-top-data ? convert($h1-heading-margin-top-data) : 3.2rem
$h2-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h2')
$h2-heading-margin-top = $h2-heading-margin-top-data ? convert($h2-heading-margin-top-data) : 2.4rem
$h3-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h3')
$h3-heading-margin-top = $h3-heading-margin-top-data ? convert($h3-heading-margin-top-data) : 1.9rem
$h4-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h4')
$h4-heading-margin-top = $h4-heading-margin-top-data ? convert($h4-heading-margin-top-data) : 1.6rem
$h5-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h5')
$h5-heading-margin-top = $h5-heading-margin-top-data ? convert($h5-heading-margin-top-data) : 1.4rem
$h6-heading-margin-top-data = hexo-config('articles.style.headings_top_spacing.h6')
$h6-heading-margin-top = $h6-heading-margin-top-data ? convert($h6-heading-margin-top-data) : 1.3rem


// ========================================================================================
//                                          MEDIA
// ========================================================================================
$media-max-width = 768px;                                 // media query max width (tablet)
$media-max-width-mobile = 640px;                          // media query max width (mobile)

redefine-tablet() {
  @media (max-width $media-max-width){
    {block}
  }
}

redefine-mobile() {
  @media (max-width $media-max-width-mobile){
    {block}
  }
}

// ========================================================================================
//                                         Z-INDEX
// ========================================================================================
$z-index-1 = 1001
$z-index-2 = 1002
$z-index-3 = 1003
$z-index-4 = 1004
$z-index-5 = 1005
$z-index-6 = 1006
$z-index-7 = 1007
$z-index-8 = 1008
$z-index-9 = 1009


// ========================================================================================
//                                      BORDER RADIUS
// ========================================================================================

$redefine-border-radius-none = 0px
$redefine-border-radius-xsmall = 4px
$redefine-border-radius-xsmall-top = 4px 4px 0px 0px
$redefine-border-radius-xsmall-bottom = 0px 0px 4px 4px
$redefine-border-radius-small = 9px
$redefine-border-radius-small-top = 9px 9px 0px 0px
$redefine-border-radius-small-bottom = 0px 0px 9px 9px
$redefine-border-radius-medium = 14px
$redefine-border-radius-medium-top = 14px 14px 0px 0px
$redefine-border-radius-medium-bottom = 0px 0px 14px 14px
$redefine-border-radius-large = 18px
$redefine-border-radius-large-top = 18px 18px 0px 0px
$redefine-border-radius-large-bottom = 0px 0px 18px 18px
$redefine-border-radius-xlarge = 24px
$redefine-border-radius-xlarge-top = 24px 24px 0px 0px
$redefine-border-radius-xlarge-bottom = 0px 0px 24px 24px
$redefine-border-radius-xxlarge = 48px
$redefine-border-radius-xxlarge-top = 48px 48px 0px 0px
$redefine-border-radius-xxlarge-bottom = 0px 0px 48px 48px
$redefine-border-radius-full = 50%
$redefine-border-radius-full-top = 50% 50% 0px 0px
$redefine-border-radius-full-bottom = 0px 0px 50% 50%

$temp-usr-img-border-radius = hexo-config('articles.style.image_border_radius')
$usr-img-border-radius = $temp-usr-img-border-radius ? convert($temp-usr-img-border-radius) : $redefine-border-radius-medium

// ========================================================================================
//                                    MERMAID THEME
// ========================================================================================
$mermaid-theme = hexo-config('mermaid.theme.light')
$dark-mermaid-theme = hexo-config('mermaid.theme.dark')



// ========================================================================================
//                                     FONT VARIABLES
// ========================================================================================

if hexo-config('global.fonts.chinese.enable') {
  $temp-chinese-font-family = hexo-config('global.fonts.chinese.family')
  $chinese-font-family = $temp-chinese-font-family ? $temp-chinese-font-family : 'PingFang SC'
} else {
  $chinese-font-family = -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Microsoft YaHei', 'Heiti SC', 'WenQuanYi Micro Hei', sans-serif;
}

if hexo-config('global.fonts.english.enable') {
  $temp-english-font-family = hexo-config('global.fonts.english.family')
  $english-font-family = $temp-english-font-family ? $temp-english-font-family : 'Geist Variable'
} else {
  $english-font-family = 'Geist Variable'
}

$default-font-family = Geist Variable, Noto Sans SC, -apple-system, system-ui, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial
$default-font-size = 16px
$temp-default-article-font-size = hexo-config('articles.style.font_size')
$temp-default-line-height = hexo-config('articles.style.line_height')
$default-article-font-size = $temp-default-article-font-size ? convert($temp-default-article-font-size) : 16px
$default-line-height = $temp-default-line-height ? $temp-default-line-height : 1.5
$default-font-weight = 400


