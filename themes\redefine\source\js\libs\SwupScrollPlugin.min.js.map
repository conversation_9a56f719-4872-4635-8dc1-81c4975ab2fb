{"version": 3, "file": "SwupScrollPlugin.min.js.map", "sources": ["../node_modules/@swup/plugin/dist/index.modern.js", "../node_modules/scrl/dist/index.modern.js", "../src/index.ts"], "sourcesContent": ["function r(){return r=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},r.apply(this,arguments)}const n=r=>String(r).split(\".\").map(r=>String(parseInt(r||\"0\",10))).concat([\"0\",\"0\"]).slice(0,3).join(\".\");class e{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(r=>r()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error(\"You must define a name of plugin when creating a class.\")}_afterUnmount(){}_checkRequirements(){return\"object\"!=typeof this.requires||Object.entries(this.requires).forEach(([r,e])=>{if(!function(r,e,t){const s=function(r,n){var e;if(\"swup\"===r)return null!=(e=n.version)?e:\"\";{var t;const e=n.findPlugin(r);return null!=(t=null==e?void 0:e.version)?t:\"\"}}(r,t);return!!s&&((r,e)=>e.every(e=>{const[,t,s]=e.match(/^([\\D]+)?(.*)$/)||[];var o,i;return((r,n)=>{const e={\"\":r=>0===r,\">\":r=>r>0,\">=\":r=>r>=0,\"<\":r=>r<0,\"<=\":r=>r<=0};return(e[n]||e[\"\"])(r)})((i=s,o=n(o=r),i=n(i),o.localeCompare(i,void 0,{numeric:!0})),t||\">=\")}))(s,e)}(r,e=Array.isArray(e)?e:[e],this.swup)){const n=`${r} ${e.join(\", \")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${n}`)}}),!0}on(r,n,e={}){var t;n=!(t=n).name.startsWith(\"bound \")||t.hasOwnProperty(\"prototype\")?n.bind(this):n;const s=this.swup.hooks.on(r,n,e);return this.handlersToUnregister.push(s),s}once(n,e,t={}){return this.on(n,e,r({},t,{once:!0}))}before(n,e,t={}){return this.on(n,e,r({},t,{before:!0}))}replace(n,e,t={}){return this.on(n,e,r({},t,{replace:!0}))}off(r,n){return this.swup.hooks.off(r,n)}}export{e as default};\n//# sourceMappingURL=index.modern.js.map\n", "function t(){return t=Object.assign?Object.assign.bind():function(t){for(var i=1;i<arguments.length;i++){var o=arguments[i];for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(t[s]=o[s])}return t},t.apply(this,arguments)}class i{constructor(i){this._raf=null,this._positionY=0,this._velocityY=0,this._targetPositionY=0,this._targetPositionYWithOffset=0,this._direction=0,this.scrollTo=t=>{if(t&&t.nodeType)this._targetPositionY=Math.round(t.getBoundingClientRect().top+window.pageYOffset);else{if(parseInt(this._targetPositionY)!==this._targetPositionY)return void console.error(\"Argument must be a number or an element.\");this._targetPositionY=Math.round(t)}this._targetPositionY>document.documentElement.scrollHeight-window.innerHeight&&(this._targetPositionY=document.documentElement.scrollHeight-window.innerHeight),this._positionY=document.body.scrollTop||document.documentElement.scrollTop,this._direction=this._positionY>this._targetPositionY?-1:1,this._targetPositionYWithOffset=this._targetPositionY+this._direction,this._velocityY=0,this._positionY!==this._targetPositionY?(this.options.onStart(),this._animate()):this.options.onAlreadyAtPositions()},this._animate=()=>{this._update(),this._render(),1===this._direction&&this._targetPositionY>this._positionY||-1===this._direction&&this._targetPositionY<this._positionY?(this._raf=requestAnimationFrame(this._animate),this.options.onTick()):(this._positionY=this._targetPositionY,this._render(),this._raf=null,this.options.onTick(),this.options.onEnd())},this._update=()=>{const t=this._targetPositionYWithOffset-this._positionY;return this._velocityY+=t*this.options.acceleration,this._velocityY*=this.options.friction,this._positionY+=this._velocityY,Math.abs(t)},this._render=()=>{window.scrollTo(0,this._positionY)},this.options=t({},{onAlreadyAtPositions:()=>{},onCancel:()=>{},onEnd:()=>{},onStart:()=>{},onTick:()=>{},friction:.7,acceleration:.04},i),i&&i.friction&&(this.options.friction=1-i.friction),window.addEventListener(\"mousewheel\",t=>{this._raf&&(this.options.onCancel(),cancelAnimationFrame(this._raf),this._raf=null)},{passive:!0})}}export{i as default};\n//# sourceMappingURL=index.modern.js.map\n", "import Plugin from '@swup/plugin';\nimport { Handler, Visit, getCurrentUrl, queryAll } from 'swup';\n// @ts-expect-error\nimport Scrl from 'scrl';\n\nexport type Options = {\n\tdoScrollingRightAway: boolean;\n\tanimateScroll: {\n\t\tbetweenPages: boolean;\n\t\tsamePageWithHash: boolean;\n\t\tsamePage: boolean;\n\t};\n\tscrollFriction: number;\n\tscrollAcceleration: number;\n\tgetAnchorElement?: (hash: string) => Element | null;\n\toffset: number | ((el: Element) => number);\n\tscrollContainers: `[data-swup-scroll-container]`;\n\tshouldResetScrollPosition: (trigger: Element) => boolean;\n};\n\ntype ScrollPosition = {\n\ttop: number;\n\tleft: number;\n};\n\ntype ScrollPositionsCacheEntry = {\n\twindow: ScrollPosition;\n\tcontainers: ScrollPosition[];\n};\n\ntype ScrollPositionsCache = Record<string, ScrollPositionsCacheEntry>;\n\ndeclare module 'swup' {\n\texport interface Swup {\n\t\tscrollTo?: (offset: number, animate: boolean) => void;\n\t}\n\n\texport interface VisitScroll {\n\t\tscrolledToContent?: boolean;\n\t}\n\n\texport interface HookDefinitions {\n\t\t'scroll:start': {};\n\t\t'scroll:end': {};\n\t}\n}\n\n/**\n * Scroll Plugin\n */\nexport default class SwupScrollPlugin extends Plugin {\n\tname = 'SwupScrollPlugin';\n\n\trequires = { swup: '>=4.2.0' };\n\n\tscrl: any;\n\n\tdefaults: Options = {\n\t\tdoScrollingRightAway: false,\n\t\tanimateScroll: {\n\t\t\tbetweenPages: true,\n\t\t\tsamePageWithHash: true,\n\t\t\tsamePage: true\n\t\t},\n\t\tscrollFriction: 0.3,\n\t\tscrollAcceleration: 0.04,\n\t\tgetAnchorElement: undefined,\n\t\toffset: 0,\n\t\tscrollContainers: `[data-swup-scroll-container]`,\n\t\tshouldResetScrollPosition: () => true\n\t};\n\n\toptions: Options;\n\n\tcachedScrollPositions: ScrollPositionsCache = {};\n\tpreviousScrollRestoration?: ScrollRestoration;\n\tcurrentCacheKey?: string;\n\n\tconstructor(options: Partial<Options> = {}) {\n\t\tsuper();\n\t\tthis.options = { ...this.defaults, ...options };\n\t}\n\n\tmount() {\n\t\tconst swup = this.swup;\n\n\t\tswup.hooks.create('scroll:start');\n\t\tswup.hooks.create('scroll:end');\n\n\t\t// Initialize Scrl lib for smooth animations\n\t\tthis.scrl = new Scrl({\n\t\t\tonStart: () => swup.hooks.callSync('scroll:start'),\n\t\t\tonEnd: () => swup.hooks.callSync('scroll:end'),\n\t\t\tonCancel: () => swup.hooks.callSync('scroll:end'),\n\t\t\tfriction: this.options.scrollFriction,\n\t\t\tacceleration: this.options.scrollAcceleration\n\t\t});\n\n\t\t// Add scrollTo method to swup and animate based on current animateScroll option\n\t\tswup.scrollTo = (offset, animate = true) => {\n\t\t\tif (animate) {\n\t\t\t\tthis.scrl.scrollTo(offset);\n\t\t\t} else {\n\t\t\t\tswup.hooks.callSync('scroll:start');\n\t\t\t\twindow.scrollTo(0, offset);\n\t\t\t\tswup.hooks.callSync('scroll:end');\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Disable browser scroll restoration for history visits\n\t\t * if `swup.options.animateHistoryBrowsing` is true\n\t\t * Store the previous setting to be able to properly restore it on unmount\n\t\t */\n\t\tthis.previousScrollRestoration = window.history.scrollRestoration;\n\t\tif (swup.options.animateHistoryBrowsing) {\n\t\t\twindow.history.scrollRestoration = 'manual';\n\t\t}\n\n\t\t// scroll to the top of the page when a visit starts, before replacing the content\n\t\tthis.on('visit:start', this.onVisitStart);\n\n\t\t// scroll to the top or target element after replacing the content\n\t\tthis.replace('content:scroll', this.onScrollToContent);\n\n\t\t// scroll to the top of the page\n\t\tthis.replace('scroll:top', this.handleScrollToTop);\n\n\t\t// scroll to an anchor on the same page\n\t\tthis.replace('scroll:anchor', this.handleScrollToAnchor);\n\t}\n\n\t/**\n\t * Runs when the plugin is unmounted\n\t */\n\tunmount() {\n\t\tsuper.unmount();\n\n\t\tif (this.previousScrollRestoration) {\n\t\t\twindow.history.scrollRestoration = this.previousScrollRestoration;\n\t\t}\n\n\t\tthis.cachedScrollPositions = {};\n\t\tdelete this.swup.scrollTo;\n\t\tdelete this.scrl;\n\t}\n\n\t/**\n\t * Detects if a scroll should be animated, based on context\n\t */\n\tshouldAnimate(context: keyof Options['animateScroll']): boolean {\n\t\tif (typeof this.options.animateScroll === 'boolean') {\n\t\t\treturn this.options.animateScroll;\n\t\t}\n\t\treturn this.options.animateScroll[context];\n\t}\n\n\t/**\n\t * Get an element based on anchor\n\t */\n\tgetAnchorElement = (hash: string = ''): Element | null => {\n\t\t// Look for a custom function provided via the plugin options\n\t\tif (typeof this.options.getAnchorElement === 'function') {\n\t\t\treturn this.options.getAnchorElement(hash);\n\t\t}\n\n\t\treturn this.swup.getAnchorElement(hash);\n\t};\n\n\t/**\n\t * Get the offset for a scroll\n\t */\n\tgetOffset = (el?: Element): number => {\n\t\tif (!el) return 0;\n\t\t// If options.offset is a function, apply and return it\n\t\tif (typeof this.options.offset === 'function') {\n\t\t\treturn parseInt(String(this.options.offset(el)), 10);\n\t\t}\n\t\t// Otherwise, return the sanitized offset\n\t\treturn parseInt(String(this.options.offset), 10);\n\t};\n\n\t/**\n\t * Scroll to top on `scroll:top` hook\n\t */\n\thandleScrollToTop: Handler<'scroll:top'> = () => {\n\t\tthis.swup.scrollTo?.(0, this.shouldAnimate('samePage'));\n\t\treturn true;\n\t};\n\n\t/**\n\t * Scroll to anchor on `scroll:anchor` hook\n\t */\n\thandleScrollToAnchor: Handler<'scroll:anchor'> = (visit, { hash }) => {\n\t\treturn this.maybeScrollToAnchor(hash, this.shouldAnimate('samePageWithHash'));\n\t};\n\n\t/**\n\t * Attempts to scroll to an anchor\n\t */\n\tmaybeScrollToAnchor(hash?: string, animate: boolean = false): boolean {\n\t\tif (!hash) {\n\t\t\treturn false;\n\t\t}\n\n\t\tconst element = this.getAnchorElement(hash);\n\t\tif (!element) {\n\t\t\tconsole.warn(`Anchor target ${hash} not found`);\n\t\t\treturn false;\n\t\t}\n\t\tif (!(element instanceof Element)) {\n\t\t\tconsole.warn(`Anchor target ${hash} is not a DOM node`);\n\t\t\treturn false;\n\t\t}\n\n\t\tconst { top: elementTop } = element.getBoundingClientRect();\n\t\tconst top = elementTop + window.scrollY - this.getOffset(element);\n\t\tthis.swup.scrollTo?.(top, animate);\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Check whether to scroll in `visit:start` hook\n\t */\n\tonVisitStart: Handler<'visit:start'> = (visit) => {\n\t\tthis.maybeResetScrollPositions(visit);\n\t\tthis.cacheScrollPositions(visit.from.url);\n\n\t\tconst scrollTarget = visit.scroll.target || visit.to.hash;\n\n\t\tvisit.scroll.scrolledToContent = false;\n\n\t\tif (this.options.doScrollingRightAway && !scrollTarget) {\n\t\t\tvisit.scroll.scrolledToContent = true;\n\t\t\tthis.doScrollingBetweenPages(visit);\n\t\t}\n\t};\n\n\t/**\n\t * Check whether to scroll in `content:scroll` hook\n\t */\n\tonScrollToContent: Handler<'content:scroll'> = (visit) => {\n\t\tif (!visit.scroll.scrolledToContent) {\n\t\t\tthis.doScrollingBetweenPages(visit);\n\t\t}\n\t\tthis.restoreScrollContainers(visit.to.url);\n\t};\n\n\t/**\n\t * Scrolls the window\n\t */\n\tdoScrollingBetweenPages = (visit: Visit): void => {\n\t\t// Bail early on popstate if not animated: browser will handle it\n\t\tif (visit.history.popstate && !visit.animation.animate) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Try scrolling to a given anchor\n\t\tconst scrollTarget = visit.scroll.target || visit.to.hash;\n\t\tif (this.maybeScrollToAnchor(scrollTarget, this.shouldAnimate('betweenPages'))) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Allow not resetting scroll position\n\t\tif (!visit.scroll.reset) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Finally, scroll to either the stored scroll position or to the very top of the page\n\t\tconst scrollPositions = this.getCachedScrollPositions(this.swup.resolveUrl(getCurrentUrl()));\n\t\tconst top = scrollPositions?.window?.top || 0;\n\n\t\t// Give possible JavaScript time to execute before scrolling\n\t\trequestAnimationFrame(() => this.swup.scrollTo?.(top, this.shouldAnimate('betweenPages')));\n\t};\n\n\t/**\n\t * Resets cached scroll positions for visits with a trigger element,\n\t * where shouldResetScrollPosition returns true for that trigger\n\t */\n\tmaybeResetScrollPositions = (visit: Visit): void => {\n\t\tconst { url } = visit.to;\n\t\tconst { el } = visit.trigger;\n\n\t\tif (el && this.options.shouldResetScrollPosition(el)) {\n\t\t\tthis.resetScrollPositions(url);\n\t\t}\n\t};\n\n\t/**\n\t * Stores the scroll positions for the current URL\n\t */\n\tcacheScrollPositions(url: string): void {\n\t\t// retrieve the current scroll position for all containers\n\t\tconst containers = queryAll(this.options.scrollContainers).map((el) => ({\n\t\t\ttop: el.scrollTop,\n\t\t\tleft: el.scrollLeft\n\t\t}));\n\n\t\t// construct the final object entry, with the window scroll positions added\n\t\tconst positions = {\n\t\t\twindow: { top: window.scrollY, left: window.scrollX },\n\t\t\tcontainers\n\t\t};\n\n\t\tthis.cachedScrollPositions[url] = positions;\n\t}\n\n\t/**\n\t * Resets stored scroll positions for a given URL\n\t */\n\tresetScrollPositions(url: string): void {\n\t\tconst cacheKey = this.swup.resolveUrl(url);\n\t\tdelete this.cachedScrollPositions[cacheKey];\n\t}\n\n\t/**\n\t * Get the stored scroll positions for a given URL from the cache\n\t */\n\tgetCachedScrollPositions(url: string): ScrollPositionsCacheEntry | undefined {\n\t\tconst cacheKey = this.swup.resolveUrl(url);\n\t\treturn this.cachedScrollPositions[cacheKey];\n\t}\n\n\t/**\n\t * Restore the scroll positions for all matching scrollContainers\n\t */\n\trestoreScrollContainers(url: string): void {\n\t\t// get the stored scroll positions from the cache\n\t\tconst scrollPositions = this.getCachedScrollPositions(url);\n\t\tif (!scrollPositions || scrollPositions.containers.length === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\t// cycle through all containers on the current page and restore their scroll positions, if appropriate\n\t\tqueryAll(this.options.scrollContainers).forEach((el, index) => {\n\t\t\tconst scrollPosition = scrollPositions.containers[index];\n\t\t\tif (scrollPosition == null) return;\n\t\t\tel.scrollTop = scrollPosition.top;\n\t\t\tel.scrollLeft = scrollPosition.left;\n\t\t});\n\t}\n\n}\n"], "names": ["normalizeVersion", "version", "String", "split", "map", "segment", "parseInt", "concat", "slice", "join", "installed", "requirements", "every", "required", "comparator", "match", "a", "b", "comparisonResult", "comparators", "r", "localeCompare", "numeric", "Scrl", "constructor", "options", "this", "_raf", "_positionY", "_velocityY", "_targetPositionY", "_targetPositionYWithOffset", "_direction", "scrollTo", "offset", "nodeType", "Math", "round", "getBoundingClientRect", "top", "window", "pageYOffset", "console", "error", "document", "documentElement", "scrollHeight", "innerHeight", "body", "scrollTop", "onStart", "_animate", "onAlreadyAtPositions", "_update", "_render", "requestAnimationFrame", "onTick", "onEnd", "distance", "acceleration", "friction", "abs", "_extends", "onCancel", "addEventListener", "event", "cancelAnimationFrame", "passive", "Plugin", "_this", "super", "name", "requires", "swup", "scrl", "defaults", "doScrollingRightAway", "animateScroll", "betweenPages", "samePageWithHash", "samePage", "scrollFriction", "scrollAcceleration", "getAnchorElement", "undefined", "scrollContainers", "shouldResetScrollPosition", "cachedScrollPositions", "previousScrollRestoration", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hash", "getOffset", "el", "handleScrollToTop", "shouldAnimate", "handleScrollToAnchor", "visit", "_ref", "maybeScrollToAnchor", "onVisitStart", "maybeResetScrollPositions", "cacheScrollPositions", "from", "url", "scrollTarget", "scroll", "target", "to", "scrolled<PERSON><PERSON><PERSON><PERSON>nt", "doScrollingBetweenPages", "onScrollToContent", "restoreScrollContainers", "history", "popstate", "animation", "animate", "reset", "getCachedScrollPositions", "resolveUrl", "getCurrentUrl", "scrollPositions", "trigger", "resetScrollPositions", "mount", "_this2", "hooks", "create", "callSync", "scrollRestoration", "animateHistoryBrowsing", "on", "replace", "unmount", "context", "element", "warn", "Element", "elementTop", "scrollY", "containers", "queryAll", "left", "scrollLeft", "positions", "scrollX", "cache<PERSON>ey", "length", "for<PERSON>ach", "index", "scrollPosition"], "mappings": "ucAGO,MAAsBA,EAAIC,GACzBC,OAAOD,GACZE,MAAM,KACNC,IAAIC,GAAWH,OAAOI,SAASD,GAAW,IAAK,MAC/CE,OAAO,CAAC,IAAK,MACbC,MAAM,EAAG,GACTC,KAAK,+nBAiCwB,EAACC,EAAmBC,IAChCA,EAACC,MAAOC,IAC1B,MAAA,CAASC,EAAYb,GAAWY,EAASE,MAAM,mBAAqB,GA/BxC,IAACC,EAAWC,EAiCxC,MA1BsB,EAACC,EAA0BJ,KAClD,MAAiBK,EAAG,CACnB,GAAKC,GAAoB,IAANA,EACnB,IAAMA,GAAcA,EAAI,EACxB,KAAOA,GAAcA,GAAK,EAC1B,IAAMA,GAAcA,EAAI,EACxB,KAAOA,GAAcA,GAAK,GAG3B,OADqBD,EAAYL,IAAeK,EAAY,KACxCD,EAAgB,EATb,EAPkBD,EAgCWhB,EA/BpDe,EAAIhB,EAD0BgB,EAgCWN,GA9BzCO,EAAIjB,EAAiBiB,GACdD,EAAEK,cAAcJ,OAAAA,EAAc,CAAEK,SAAS,KA8BLR,GAA6B,KAAI,GAJ7C,4zBC1CXS,MAAAA,EAQjBC,YAAYC,GAASC,KAPrBC,KAAO,KAAID,KACXE,WAAa,EAACF,KACdG,WAAa,EAACH,KACdI,iBAAmB,EAACJ,KACpBK,2BAA6B,EAACL,KAC9BM,WAAa,EAACN,KAqCdO,SAAYC,IACR,GAAIA,GAAUA,EAAOC,SAEjBT,KAAKI,iBAAmBM,KAAKC,MAAMH,EAAOI,wBAAwBC,IAAMC,OAAOC,iBACxEnC,CAAAA,GAAAA,SAASoB,KAAKI,oBAAsBJ,KAAKI,iBAKhD,YADAY,QAAQC,MAAM,4CAFdjB,KAAKI,iBAAmBM,KAAKC,MAAMH,EAIvC,CAGIR,KAAKI,iBAAmBc,SAASC,gBAAgBC,aAAeN,OAAOO,cACvErB,KAAKI,iBAAmBc,SAASC,gBAAgBC,aAAeN,OAAOO,aAI3ErB,KAAKE,WAAagB,SAASI,KAAKC,WAAaL,SAASC,gBAAgBI,UACtEvB,KAAKM,WAAcN,KAAKE,WAAaF,KAAKI,kBAAqB,EAAI,EACnEJ,KAAKK,2BAA6BL,KAAKI,iBAAmBJ,KAAKM,WAC/DN,KAAKG,WAAa,EAEdH,KAAKE,aAAeF,KAAKI,kBAEzBJ,KAAKD,QAAQyB,UACbxB,KAAKyB,YAGLzB,KAAKD,QAAQ2B,sBACjB,EAGJD,KAAWA,SAAA,KACUzB,KAAK2B,UACtB3B,KAAK4B,UAEmB,IAApB5B,KAAKM,YAAoBN,KAAKI,iBAAmBJ,KAAKE,aAAmC,IAArBF,KAAKM,YAAqBN,KAAKI,iBAAmBJ,KAAKE,YAE3HF,KAAKC,KAAO4B,sBAAsB7B,KAAKyB,UACvCzB,KAAKD,QAAQ+B,WAGb9B,KAAKE,WAAaF,KAAKI,iBACvBJ,KAAK4B,UACL5B,KAAKC,KAAO,KACZD,KAAKD,QAAQ+B,SACb9B,KAAKD,QAAQgC,QAEjB,EACH/B,KAED2B,QAAU,KACN,MAAMK,EAAWhC,KAAKK,2BAA6BL,KAAKE,WAQxD,OALAF,KAAKG,YAFc6B,EAAWhC,KAAKD,QAAQkC,aAI3CjC,KAAKG,YAAcH,KAAKD,QAAQmC,SAChClC,KAAKE,YAAcF,KAAKG,WAEjBO,KAAKyB,IAAIH,EAAQ,EAG5BJ,KAAUA,QAAA,KACNd,OAAOP,SAAS,EAAGP,KAAKE,WAAU,EAvFlCF,KAAKD,QAAOqC,EAXK,GAAA,CACbV,qBAAsB,OACtBW,SAAU,OACVN,MAAO,OACPP,QAAS,OACTM,OAAQ,OACRI,SAAU,GACVD,aAAc,KAMXlC,GAIHA,GAAWA,EAAQmC,WACnBlC,KAAKD,QAAQmC,SAAW,EAAInC,EAAQmC,UAIxCpB,OAAOwB,iBAAiB,aAAcC,IAC9BvC,KAAKC,OACLD,KAAKD,QAAQsC,WACbG,qBAAqBxC,KAAKC,MAC1BD,KAAKC,KAAO,KAChB,EACD,CACCwC,SAAS,GAEjB,SCSiB,cAA+BC,EA4BnD5C,YAAYC,GAA8B,IAAA4C,WAA9B5C,IAAAA,EAA4B,CAAE,GACzC6C,QA5BDC,EAAAA,KAAAA,KAAAA,KAAO,wBAEPC,SAAW,CAAEC,KAAM,WAAW/C,KAE9BgD,UAEAC,EAAAA,KAAAA,SAAoB,CACnBC,sBAAsB,EACtBC,cAAe,CACdC,cAAc,EACdC,kBAAkB,EAClBC,UAAU,GAEXC,eAAgB,GAChBC,mBAAoB,IACpBC,sBAAkBC,EAClBlD,OAAQ,EACRmD,iBAAgD,+BAChDC,0BAA2B,KAAM,GAGlC7D,KAAAA,aAEA8D,EAAAA,KAAAA,sBAA8C,CAAA,EAC9CC,KAAAA,+BACAC,EAAAA,KAAAA,qBAoFAN,EAAAA,KAAAA,iBAAmB,SAACO,GAEnB,YAFkC,IAAfA,IAAAA,EAAe,IAEW,mBAAlCrB,EAAK5C,QAAQ0D,iBACZd,EAAC5C,QAAQ0D,iBAAiBO,GAG/BrB,EAAKI,KAAKU,iBAAiBO,EACnC,EAAChE,KAKDiE,UAAaC,GACPA,EAE8B,mBAApBlE,KAACD,QAAQS,OAChB5B,SAASJ,OAAOwB,KAAKD,QAAQS,OAAO0D,IAAM,IAG3CtF,SAASJ,OAAOwB,KAAKD,QAAQS,QAAS,IAN7B,OAYjB2D,kBAA2C,KAC1CnE,KAAK+C,KAAKxC,WAAW,EAAGP,KAAKoE,cAAc,cACpC,GACPpE,KAKDqE,qBAAiD,CAACC,EAAKC,KAAE,IAAAP,KAAEA,GAC1DO,EAAA,YAAYC,oBAAoBR,EAAMhE,KAAKoE,cAAc,oBAAmB,EA+B7EK,KAAAA,aAAwCH,IACvCtE,KAAK0E,0BAA0BJ,GAC/BtE,KAAK2E,qBAAqBL,EAAMM,KAAKC,KAErC,MAAkBC,EAAGR,EAAMS,OAAOC,QAAUV,EAAMW,GAAGjB,KAErDM,EAAMS,OAAOG,mBAAoB,EAE7BlF,KAAKD,QAAQmD,uBAAyB4B,IACzCR,EAAMS,OAAOG,mBAAoB,EACjClF,KAAKmF,wBAAwBb,GAC7B,EAMFc,KAAAA,kBAAgDd,IAC1CA,EAAMS,OAAOG,mBACjBlF,KAAKmF,wBAAwBb,GAE9BtE,KAAKqF,wBAAwBf,EAAMW,GAAGJ,IACvC,EAAC7E,KAKDmF,wBAA2Bb,IAE1B,GAAIA,EAAMgB,QAAQC,WAAajB,EAAMkB,UAAUC,QAC9C,OAKD,GAAIzF,KAAKwE,oBADYF,EAAMS,OAAOC,QAAUV,EAAMW,GAAGjB,KACVhE,KAAKoE,cAAc,iBAC7D,OAID,IAAKE,EAAMS,OAAOW,MACjB,OAID,QAAwB1F,KAAK2F,yBAAyB3F,KAAK+C,KAAK6C,kFAAWC,OAC/DC,GAAiBhF,QAAQD,KAAO,EAG5CgB,sBAAsB,IAAM7B,KAAK+C,KAAKxC,WAAWM,EAAKb,KAAKoE,cAAc,iBAAgB,EAO1FM,KAAAA,0BAA6BJ,IAC5B,MAAMO,IAAEA,GAAQP,EAAMW,IAChBf,GAAEA,GAAOI,EAAMyB,QAEjB7B,GAAMlE,KAAKD,QAAQ6D,0BAA0BM,IAChDlE,KAAKgG,qBAAqBnB,EAC1B,EA/MD7E,KAAKD,QAAU,IAAKC,KAAKiD,YAAalD,EACvC,CAEAkG,QAAK,IAAAC,EAAAlG,KACJ,MAAM+C,EAAO/C,KAAK+C,KAElBA,EAAKoD,MAAMC,OAAO,gBAClBrD,EAAKoD,MAAMC,OAAO,cAGlBpG,KAAKgD,KAAO,IAAInD,EAAK,CACpB2B,QAAS,IAAMuB,EAAKoD,MAAME,SAAS,gBACnCtE,MAAO,IAAMgB,EAAKoD,MAAME,SAAS,cACjChE,SAAU,IAAMU,EAAKoD,MAAME,SAAS,cACpCnE,SAAUlC,KAAKD,QAAQwD,eACvBtB,aAAcjC,KAAKD,QAAQyD,qBAI5BT,EAAKxC,SAAW,SAACC,EAAQiF,QAAO,IAAPA,IAAAA,GAAU,GAC9BA,EACHS,EAAKlD,KAAKzC,SAASC,IAEnBuC,EAAKoD,MAAME,SAAS,gBACpBvF,OAAOP,SAAS,EAAGC,GACnBuC,EAAKoD,MAAME,SAAS,cAEtB,EAOArG,KAAK8D,0BAA4BhD,OAAOwE,QAAQgB,kBAC5CvD,EAAKhD,QAAQwG,yBAChBzF,OAAOwE,QAAQgB,kBAAoB,UAIpCtG,KAAKwG,GAAG,cAAexG,KAAKyE,cAG5BzE,KAAKyG,QAAQ,iBAAkBzG,KAAKoF,mBAGpCpF,KAAKyG,QAAQ,aAAczG,KAAKmE,mBAGhCnE,KAAKyG,QAAQ,gBAAiBzG,KAAKqE,qBACpC,CAKAqC,UACC9D,MAAM8D,UAEF1G,KAAK8D,4BACRhD,OAAOwE,QAAQgB,kBAAoBtG,KAAK8D,2BAGzC9D,KAAK6D,sBAAwB,CAAE,cACnBd,KAAKxC,gBACNP,KAACgD,IACb,CAKAoB,cAAcuC,GACb,MAA0C,uBAA1B5G,QAAQoD,cACZnD,KAACD,QAAQoD,cAEdnD,KAAKD,QAAQoD,cAAcwD,EACnC,CA6CAnC,oBAAoBR,EAAeyB,GAClC,QADkCA,IAAAA,IAAAA,GAAmB,IAChDzB,EACJ,SAGD,MAAa4C,EAAG5G,KAAKyD,iBAAiBO,GACtC,IAAK4C,EAEJ,OADA5F,QAAQ6F,sBAAsB7C,kBAG/B,KAAM4C,aAAmBE,SAExB,OADA9F,QAAQ6F,sBAAsB7C,wBACvB,EAGR,MAAQnD,IAAKkG,GAAeH,EAAQhG,wBAC9BC,EAAMkG,EAAajG,OAAOkG,QAAUhH,KAAKiE,UAAU2C,GAGzD,OAFA5G,KAAK+C,KAAKxC,WAAWM,EAAK4E,IAEnB,CACR,CAyEAd,qBAAqBE,GAEpB,MAAgBoC,EAAGC,EAASlH,KAAKD,QAAQ4D,kBAAkBjF,IAAKwF,KAC/DrD,IAAKqD,EAAG3C,UACR4F,KAAMjD,EAAGkD,cAIKC,EAAG,CACjBvG,OAAQ,CAAED,IAAKC,OAAOkG,QAASG,KAAMrG,OAAOwG,SAC5CL,cAGDjH,KAAK6D,sBAAsBgB,GAAOwC,CACnC,CAKArB,qBAAqBnB,GACpB,MAAM0C,EAAWvH,KAAK+C,KAAK6C,WAAWf,UAC3B7E,KAAC6D,sBAAsB0D,EACnC,CAKA5B,yBAAyBd,GACxB,MAAM0C,EAAWvH,KAAK+C,KAAK6C,WAAWf,GACtC,OAAW7E,KAAC6D,sBAAsB0D,EACnC,CAKAlC,wBAAwBR,GAEvB,QAAwB7E,KAAK2F,yBAAyBd,GACjDiB,GAAyD,IAAtCA,EAAgBmB,WAAWO,QAKnDN,EAASlH,KAAKD,QAAQ4D,kBAAkB8D,QAAQ,CAACvD,EAAIwD,KACpD,MAAMC,EAAiB7B,EAAgBmB,WAAWS,GAC5B,MAAlBC,IACJzD,EAAG3C,UAAYoG,EAAe9G,IAC9BqD,EAAGkD,WAAaO,EAAeR,KAChC,EACD"}