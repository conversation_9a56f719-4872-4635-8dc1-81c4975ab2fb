export default()=>{HTMLElement.prototype.wrap=function(e){this.parentNode.insertBefore(e,this),this.parentNode.removeChild(this),e.appendChild(this)},document.querySelectorAll("figure.highlight").forEach((e=>{const t=document.createElement("div");e.wrap(t),t.classList.add("highlight-container"),t.insertAdjacentHTML("beforeend",'<div class="copy-button"><i class="fa-regular fa-copy"></i></div>'),t.insertAdjacentHTML("beforeend",'<div class="fold-button"><i class="fa-solid fa-chevron-down"></i></div>');const o=t.querySelector(".copy-button"),a=t.querySelector(".fold-button");o.addEventListener("click",(()=>{const e=[...t.querySelectorAll(".code .line")].map((e=>e.innerText)).join("\n");navigator.clipboard.writeText(e),o.querySelector("i").className="fa-regular fa-check",setTimeout((()=>{o.querySelector("i").className="fa-regular fa-copy"}),1e3)})),a.addEventListener("click",(()=>{t.classList.toggle("folded"),a.querySelector("i").className=t.classList.contains("folded")?"fa-solid fa-chevron-up":"fa-solid fa-chevron-down"}))}))};
//# sourceMappingURL=codeBlock.js.map