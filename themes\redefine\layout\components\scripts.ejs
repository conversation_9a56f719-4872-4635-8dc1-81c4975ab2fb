<%- renderJS([
    'tools/imageViewer.js',
    'utils.js',
    'main.js',
    'layouts/navbarShrink.js',
    'tools/scrollTopBottom.js',
    'tools/lightDarkSwitch.js',
    'layouts/categoryList.js'
],{
    module: true
}) %>

<% if (theme.navbar.search.enable) { %>
    <%- renderJS('tools/localSearch.js', {
        module: true
    }) %>
<% } %>

<% if (theme.articles.code_block.copy) { %>
    <%- renderJS('tools/codeBlock.js', {
        module: true
    }) %>
<% } %>

<% if (theme.articles.lazyload) { %>
    <%- renderJS('layouts/lazyload.js', {
        module: true
    }) %>
<% } %>

<% if (theme.footer.runtime) { %>
    <%- renderJS('tools/runtime.js') %>
    <%- renderJS('libs/odometer.min.js') %>
    <%- renderCSS('assets/odometer-theme-minimal.css') %>
<% } %>

<% if (theme.home_banner.subtitle.length !== 0) { %>
  <%- renderJS('libs/Typed.min.js') %>
  <%- renderJS('plugins/typed.js', {
      module: true
  }) %>
<% } %>

<% if (theme.plugins.mermaid.enable) { %>
    <% if (theme.plugins.mermaid.version === '11.4.1') { %>
        <%- renderJS('libs/mermaid.min.js') %>
    <% } else { %>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@<%= theme.plugins.mermaid.version %>/dist/mermaid.min.js"></script>
    <% } %>
    <%- renderJS('plugins/mermaid.js') %>
<% } %>

<% if (theme.masonry || theme.photos || theme.gallery) { %>
    <%- renderJS('libs/minimasonry.min.js') %>
    <%- renderJS('plugins/masonry.js', {
        module: true
    }) %>
<% } %>

<% if (theme.global.preloader === false || theme.global.preloader.enable === false) { %>
    <%- renderJS('libs/anime.min.js')%>
<% } %>


<% if (theme.articles.toc.enable) { %>
    <%- renderJS([
        'tools/tocToggle.js',
        'layouts/toc.js',
        'plugins/tabs.js'
    ], {
        module: true,
        swupReload: true
    }) %>
<% } %>

<%- renderJS('libs/moment-with-locales.min.js', { swupReload: true }) %>
<%- renderJS('layouts/essays.js', { swupReload: true, module: true }) %>

<% if (theme.articles.pangu_js) { %>
    <%- renderJS([
        'libs/pangu.min.js',
        'plugins/pangu.js'
    ]) %>
<% } %>

<% if (theme.bookmarks && theme.bookmarks.length !== 0) { %>
    <%- renderJS('layouts/bookmarkNav.js', {
        module: true
    }) %>
<% } %>