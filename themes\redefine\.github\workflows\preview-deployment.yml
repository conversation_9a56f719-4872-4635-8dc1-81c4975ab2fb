name: Vercel Preview Deployment

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  workflow_dispatch:
  push:
    branches:
      - dev
  pull_request_target:
    branches:
      - dev
      - main

jobs:
  preview-deployment:
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.event_name == 'pull_request_target' && 'PR Preview' || 'Preview' }}
      url: ${{ steps.deploy-to-vercel.outputs.preview-url }}
    steps:
      - name: Determine checkout details
        id: checkout-details
        uses: actions/github-script@v7
        with:
          script: |
            const isPr = context.eventName === 'pull_request_target'
            const ref = isPr ? context.payload.pull_request.head.ref : context.ref
            const repo = isPr ? context.payload.pull_request.head.repo.full_name : context.repo.full_name
            core.setOutput('ref', ref)
            core.setOutput('repo', repo)

      - name: Checkout theme repository
        uses: actions/checkout@v4
        with:
          path: 'theme'
          ref: ${{ steps.checkout-details.outputs.ref }}
          repository: ${{ steps.checkout-details.outputs.repo }}


      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x' # Specify your required Node.js version

      - name: Clone Hexo site repository
        run: git clone https://github.com/EvanNotFound/redefine-demo.git hexo-site

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'  # Ensure compatibility with Python 3.x

      - name: Install PyYAML
        run: pip install pyyaml

      - name: Enable Developer Mode in Config
        run: |
          echo "import yaml
          import logging

          # Set up logging
          logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

          try:
              # Load YAML file
              with open('hexo-site/_config.redefine.yml', 'r') as file:
                  config = yaml.safe_load(file)
                  logging.info('YAML file loaded successfully.')

              # Modify the developer mode and CDN settings
              config['developer']['enable'] = True
              logging.info('Developer mode set to True.')

              config['cdn']['enable'] = False
              logging.info('CDN set to False.')

              # Save the modified YAML file
              with open('hexo-site/_config.redefine.yml', 'w') as file:
                  yaml.safe_dump(config, file, default_flow_style=False, sort_keys=False)
                  logging.info('YAML file saved with new settings.')

          except Exception as e:
              logging.error('An error occurred: {}'.format(e))
          " > update_yaml.py
          python update_yaml.py

      - name: Move theme into Hexo site
        run: |
          mv theme hexo-site/themes/redefine

      - name: Install dependencies
        run: |
          cd hexo-site
          npm install

      - name: Build Hexo
        run: |
          cd hexo-site
          npm install
          npm uninstall hexo-theme-redefine
          npm run build

      - name: Change directory to hexo-site
        run: echo "HEXO_SITE_DIR=${{ github.workspace }}/hexo-site" >> $GITHUB_ENV

      - name: Install Vercel CLI
        run: npm install -g vercel@22.0.1

      - name: Deploy to Vercel Action
        id: deploy-to-vercel
        uses: EvanNotFound/vercel-deployment-for-github-actions@v1.2.0
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          WORKING_DIRECTORY: ${{ env.HEXO_SITE_DIR }}
          VERCEL_SCOPE: ${{ secrets.VERCEL_SCOPE }}
          PRODUCTION: false
          DEPLOY_PR_FROM_FORK: true
          ALIAS_DOMAINS: |
            redefine-preview.vercel.app
            redefine-preview-git-{SHA}.vercel.app
          PR_PREVIEW_DOMAIN: "redefine-preview-pr-{PR}.vercel.app"