{"version": 3, "file": "categoryList.js", "names": ["toggleStyle", "element", "style", "firstValue", "secondValue", "setupCategoryList", "parentElements", "Array", "from", "document", "querySelectorAll", "filter", "item", "parentElement", "classList", "contains", "for<PERSON>ach", "childElements", "childElement", "maxHeight", "marginTop", "addEventListener", "clickedElementTopOffset", "offsetTop", "siblingElement", "siblingChildElement", "swup", "hooks", "on", "e", "console", "error"], "sources": ["0"], "mappings": "AAAA,MAAMA,YAAc,CAACC,EAASC,EAAOC,EAAYC,KAC/CH,EAAQC,MAAMA,GACZD,EAAQC,MAAMA,KAAWC,EAAaC,EAAcD,CAAU,EAG5DE,kBAAoB,KACxB,MAAMC,EAAiBC,MAAMC,KAC3BC,SAASC,iBAAiB,4BAC1BC,QAAQC,GACRA,EAAKC,cAAcC,UAAUC,SAAS,uBAGxCT,EAAeU,SAASH,IACtB,MAAMI,EAAgBJ,EAAcH,iBAClC,4BAEFO,EAAcD,SAASE,IACrBA,EAAahB,MAAMiB,UAAY,MAC/BD,EAAahB,MAAMkB,UAAY,KAAK,IAGtCP,EAAcQ,iBAAiB,SAAS,KACtC,MAAMC,EAA0BT,EAAcU,UAC9CN,EAAcD,SAASE,IACrBlB,YAAYkB,EAAc,YAAa,MAAO,UAC9ClB,YAAYkB,EAAc,YAAa,MAAO,OAAO,IAGvDZ,EAAeU,SAASQ,IACtB,GACEA,EAAeD,YAAcD,GAC7BE,IAAmBX,EACnB,CAC6BW,EAAed,iBAC1C,4BAEmBM,SAASS,IAC5BzB,YAAYyB,EAAqB,YAAa,MAAO,UACrDzB,YAAYyB,EAAqB,YAAa,MAAO,OAAO,GAEhE,IACA,GACF,GACF,EAGJ,IACEC,KAAKC,MAAMC,GAAG,YAAavB,kBAC7B,CAAE,MAAOwB,GACPC,QAAQC,MAAMF,EAChB,CAEApB,SAASY,iBAAiB,mBAAoBhB", "ignoreList": []}