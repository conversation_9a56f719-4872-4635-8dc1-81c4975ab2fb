.twikoo-container
  #twikoo
    .tk-meta-input
      .el-input
        .el-input-group__prepend
          color var(--default-text-color)

        input
          color var(--default-text-color)

    .tk-input
      textarea
        color var(--default-text-color)

    .actions
      .tk-row-actions-start
        .tk-action-icon
          svg
            fill var(--default-text-color)

          &.OwO
            .OwO-body
              color var(--default-text-color)
              background-color var(--background-color)

      .tk-preview
        span
          background var(--third-background-color)
          color var(--default-text-color)

    .tk-preview-container
      color var(--default-text-color)

    .tk-comments-container
      .tk-comments-count
        span
          color var(--default-text-color)

    .tk-main
      margin-bottom 20px

      .tk-meta
        .tk-nick
          color var(--default-text-color)

        .tk-tag
          &.tk-tag-green
            background -webkit-linear-gradient(45deg, #e3565e, #ee854b, #f6c258, #90c68a, #5fb3b3, #6699cc, #c594c5)
            background linear-gradient(45deg, #e3565e, #ee854b, #f6c258, #90c68a, #5fb3b3, #6699cc, #c594c5)
            color #fff
            border none
            border-radius 5px

        .tk-time
          time
            color var(--default-text-color)

      .tk-content
        color var(--default-text-color)

      .tk-expand
        color var(--third-text-color)
