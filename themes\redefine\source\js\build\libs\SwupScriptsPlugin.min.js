!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t||self).SwupScriptsPlugin=e()}(this,function(){function t(){return t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},t.apply(this,arguments)}const e=t=>String(t).split(".").map(t=>String(parseInt(t||"0",10))).concat(["0","0"]).slice(0,3).join(".");class r{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(t=>t()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error("You must define a name of plugin when creating a class.")}_afterUnmount(){}_checkRequirements(){return"object"!=typeof this.requires||Object.entries(this.requires).forEach(([t,r])=>{if(!function(t,r,n){const o=function(t,e){var r;if("swup"===t)return null!=(r=e.version)?r:"";{var n;const r=e.findPlugin(t);return null!=(n=null==r?void 0:r.version)?n:""}}(t,n);return!!o&&((t,r)=>r.every(r=>{const[,n,o]=r.match(/^([\D]+)?(.*)$/)||[];var s,i;return((t,e)=>{const r={"":t=>0===t,">":t=>t>0,">=":t=>t>=0,"<":t=>t<0,"<=":t=>t<=0};return(r[e]||r[""])(t)})((i=o,s=e(s=t),i=e(i),s.localeCompare(i,void 0,{numeric:!0})),n||">=")}))(o,r)}(t,r=Array.isArray(r)?r:[r],this.swup)){const e=`${t} ${r.join(", ")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${e}`)}}),!0}on(t,e,r={}){var n;e=!(n=e).name.startsWith("bound ")||n.hasOwnProperty("prototype")?e.bind(this):e;const o=this.swup.hooks.on(t,e,r);return this.handlersToUnregister.push(o),o}once(e,r,n={}){return this.on(e,r,t({},n,{once:!0}))}before(e,r,n={}){return this.on(e,r,t({},n,{before:!0}))}replace(e,r,n={}){return this.on(e,r,t({},n,{replace:!0}))}off(t,e){return this.swup.hooks.off(t,e)}}return class extends r{constructor(t){void 0===t&&(t={}),super(),this.name="SwupScriptsPlugin",this.requires={swup:">=4"},this.defaults={head:!0,body:!0,optin:!1},this.options={...this.defaults,...t}}mount(){this.on("content:replace",this.runScripts)}runScripts(){const{head:t,body:e,optin:r}=this.options,n=this.getScope({head:t,body:e});if(!n)return;const o=Array.from(n.querySelectorAll(r?"script[data-swup-reload-script]":"script:not([data-swup-ignore-script])"));o.forEach(t=>this.runScript(t)),this.swup.log(`Executed ${o.length} scripts.`)}runScript(t){const e=document.createElement("script");for(const{name:r,value:n}of t.attributes)e.setAttribute(r,n);return e.textContent=t.textContent,t.replaceWith(e),e}getScope(t){let{head:e,body:r}=t;return e&&r?document:e?document.head:r?document.body:null}}});