export default function imageViewer(){let e=!1,t=1,n=!1,r=!1,i=0,o=0,s=0,a=0,c=0;const l=document.querySelector(".image-viewer-container");if(!l)return void console.warn("Image viewer container not found. Exiting imageViewer function.");const d=l.querySelector("img");if(!d)return void console.warn("Target image not found in image viewer container. Exiting imageViewer function.");const showHandle=e=>{document.body.style.overflow=e?"hidden":"auto",e?l.classList.add("active"):l.classList.remove("active")};let u=0;const dragEndHandle=e=>{n&&e.stopPropagation(),n=!1,d.style.cursor="grab"};d.addEventListener("wheel",(e=>{e.preventDefault();const n=d.getBoundingClientRect(),r=e.clientX-n.left,i=e.clientY-n.top,o=r-n.width/2,s=i-n.height/2,l=t;t+=-.001*e.deltaY,t=Math.min(Math.max(.8,t),4),l<t?(a-=o*(t-l),c-=s*(t-l)):(a=0,c=0),d.style.transform=`translate(${a}px, ${c}px) scale(${t})`}),{passive:!1}),d.addEventListener("mousedown",(e=>{e.preventDefault(),n=!0,o=e.clientX,s=e.clientY,d.style.cursor="grabbing"}),{passive:!1}),d.addEventListener("mousemove",(e=>{if(n){const n=(new Date).getTime();if(n-u<100)return;u=n;const i=e.clientX-o,l=e.clientY-s;a+=i,c+=l,o=e.clientX,s=e.clientY,d.style.transform=`translate(${a}px, ${c}px) scale(${t})`,r=!0}}),{passive:!1}),d.addEventListener("mouseup",dragEndHandle,{passive:!1}),d.addEventListener("mouseleave",dragEndHandle,{passive:!1}),l.addEventListener("click",(n=>{r||(e=!1,showHandle(e),t=1,a=0,c=0,d.style.transform=`translate(${a}px, ${c}px) scale(${t})`),r=!1}));const m=document.querySelectorAll(".markdown-body img, .masonry-item img, #shuoshuo-content img"),escapeKeyListener=n=>{"Escape"===n.key&&e&&(e=!1,showHandle(e),t=1,a=0,c=0,d.style.transform=`translate(${a}px, ${c}px) scale(${t})`,document.removeEventListener("keydown",escapeKeyListener))};if(m.length>0){m.forEach(((t,n)=>{t.addEventListener("click",(()=>{i=n,e=!0,showHandle(e),d.src=t.src,document.addEventListener("keydown",escapeKeyListener)}))}));const handleArrowKeys=t=>{if(!e)return;if("ArrowUp"===t.key||"ArrowLeft"===t.key)i=(i-1+m.length)%m.length;else{if("ArrowDown"!==t.key&&"ArrowRight"!==t.key)return;i=(i+1)%m.length}const n=m[i];let r=n.src;n.hasAttribute("lazyload")&&(r=n.getAttribute("data-src"),n.src=r,n.removeAttribute("lazyload")),d.src=r};document.addEventListener("keydown",handleArrowKeys)}}
//# sourceMappingURL=imageViewer.js.map