const footerRuntime=()=>{const e=theme.footerStart;window.setTimeout(footerRuntime,1e3);const t=new Date(e),n=((new Date).getTime()-t.getTime())/864e5,o=Math.floor(n),m=24*(n-o),d=Math.floor(m),r=60*(m-d),i=Math.floor(60*(m-d)),u=Math.floor(60*(r-i)),a=document.getElementById("runtime_days"),s=document.getElementById("runtime_hours"),M=document.getElementById("runtime_minutes"),c=document.getElementById("runtime_seconds");a&&(a.innerHTML=o),s&&(s.innerHTML=d),M&&(M.innerHTML=i),c&&(c.innerHTML=u)};window.addEventListener("DOMContentLoaded",footerRuntime);
//# sourceMappingURL=runtime.js.map