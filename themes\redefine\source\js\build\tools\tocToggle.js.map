{"version": 3, "file": "tocToggle.js", "names": ["main", "initTocToggle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toggle<PERSON><PERSON>", "document", "querySelector", "postPageContainerDom", "toggleBarIcon", "articleContentContainerDom", "mainContentDom", "isOpenPageAside", "initToggleBarButton", "this", "addEventListener", "styleStatus", "setStyleStatus", "changePageLayoutWhenOpenToggle", "toggleClassName", "element", "className", "condition", "classList", "toggle", "isOpen", "pageAsideHandleOfTOC", "style", "display", "swup", "hooks", "on", "e"], "sources": ["0"], "mappings": "eAESA,MAAY,oBAEd,SAASC,gBACd,MAAMC,EAAY,CAChBC,UAAWC,SAASC,cAAc,sBAClCC,qBAAsBF,SAASC,cAAc,wBAC7CE,cAAeH,SAASC,cAAc,wBACtCG,2BAA4BJ,SAASC,cACnC,8BAEFI,eAAgBL,SAASC,cAAc,iBAEvCK,iBAAiB,EAEjB,mBAAAC,GACEC,KAAKT,WACHS,KAAKT,UAAUU,iBAAiB,SAAS,KACvCD,KAAKF,iBAAmBE,KAAKF,gBAC7BV,EAAKc,YAAYJ,gBAAkBE,KAAKF,gBACxCV,EAAKe,iBACLH,KAAKI,+BAA+BJ,KAAKF,gBAAgB,GAE/D,EAEA,eAAAO,CAAgBC,EAASC,EAAWC,GAC9BF,GACFA,EAAQG,UAAUC,OAAOH,EAAWC,EAExC,EACA,8BAAAJ,CAA+BO,GAC7BX,KAAKK,gBAAgBL,KAAKL,cAAe,MAAOgB,GAChDX,KAAKK,gBAAgBL,KAAKL,cAAe,YAAagB,GACtDX,KAAKK,gBAAgBL,KAAKL,cAAe,cAAegB,GACxDX,KAAKK,gBAAgBL,KAAKN,qBAAsB,WAAYiB,GAC5DX,KAAKK,gBAAgBL,KAAKH,eAAgB,UAAWc,EACvD,EAEA,oBAAAC,CAAqBD,GACnBX,KAAKT,UAAUsB,MAAMC,QAAU,OAC/Bd,KAAKF,gBAAkBa,EACvBX,KAAKI,+BAA+BO,EACtC,GAIF,OADArB,EAAUS,sBACHT,CACT,CAGA,IACEyB,KAAKC,MAAMC,GAAG,aAAa,KACzB5B,eAAe,GAEnB,CAAE,MAAO6B,GAAI,CAEb1B,SAASS,iBAAiB,mBAAoBZ", "ignoreList": []}