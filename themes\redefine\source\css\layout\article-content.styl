@require '../common/variables'

$arrow-icon-width = 16px
$post-nav-max-width = 220px
$article-title-font-size = 3.2rem
$temp-toc-width = hexo-config('global.sidebar_width')
$toc-container-width = $temp-toc-width ? convert($temp-toc-width) : 210px
$without-toc-container-width = calc(100% - $temp-toc-width)
$gap = 30px

.post-page-container
  gap 0px

  +redefine-mobile()
    overflow-x hidden


  &.show-toc
    gap 30px

    .toc-content-container
      display block
      opacity 1
      transition opacity 0.2s ease-in 0.2s
      width $toc-container-width

      &:hover::-webkit-scrollbar-thumb
        border 3px solid rgba(183, 183, 183, 0.3)

      &::-webkit-scrollbar
        width 6px
        height 6px
        transition all 0.2s ease
        display block

      &::-webkit-scrollbar-track
        background transparent
        border none

      &::-webkit-scrollbar-thumb
        border-radius 20px
        border 3px solid rgba(183, 183, 183, 0)
        transition all 0.2s ease

    .article-content-container
      width calc(100% - 210px) !important

  +redefine-tablet()
    .toc-content-container
      display none !important

    .article-content-container
      width 100% !important

    &.show-toc
      .toc-content-container
        display none

      .article-content-container
        width 100% !important

  .article-content-container
    width 100%
    transition width 0.2s ease
    redefine-container(
      false,
      0,
      0,
      0,
      30px
    )

    +redefine-mobile()
      padding 1.2rem 0.2rem
      box-shadow none !important
      max-width 100vw

    .article-header .avatar img
          border-radius: $redefine-border-radius-medium * 0.87
          width 100%
          height 100%
          // background var(--avatar-background-color)

    .article-header, .article-header-meta-info
      margin-top 16px

      +redefine-tablet()
        transform scale(0.9)
        transform-origin left top

    .article-content
      margin-top $spacing-unit
      padding-bottom 10px
      word-wrap break-word
      border-bottom 1px solid var(--border-color)
      color var(--default-text-color)

      &>:first-child
        margin-top 0 !important

    .article-prev, .article-next

        a
          position relative
          display block
          width 100%
          height 100%
          box-sizing border-box

          &.prev
            padding-left $arrow-icon-width

          &.next
            padding-right $arrow-icon-width

          .arrow-icon
            position absolute
            top 0
            height 100%
            width $arrow-icon-width

            &.left
              left 0

            &.right
              right 0

          .title
            width 100%
            height 100%

      .post-nav-item
        display none

        +redefine-tablet()
          display inline-block

      .post-nav-title-item
        overflow hidden
        text-overflow ellipsis
        white-space nowrap

        +redefine-tablet()
          display none

    .recommended-article
      overflow visible !important

      .recommended-mobile
        display none
        +redefine-tablet()
          display block

      .recommended-desktop
        display block
        +redefine-tablet()
          display none

      .recommended-article-group
        padding 10px 0
        display flex
        justify-content space-between
        flex-wrap wrap
        overflow visible !important

        @media screen and (max-width $device-tablet)
          height 190px
          overflow scroll

          &::-webkit-scrollbar
            width 0 !important

          -ms-overflow-style none

        .recommended-article-item
          redefine-container(
            false,
            0,
            0,
            10px,
            0
          )
          box-shadow var(--redefine-box-shadow-flat)
          padding 12px !important
          display flex
          flex-wrap wrap
          align-content center
          justify-content center
          align-items center
          overflow hidden
          width 32%
          max-height 200px
          margin-top: $gap * 0.5
          margin-right 0

          +redefine-tablet()
            width 49%

          img
            display flex
            width calc(100% + 24px)
            height 150px
            object-fit cover
            border-radius $redefine-border-radius-medium-top
            margin -12px -12px 0 -12px !important

          span
            margin-top 0.5rem
            display block
            display -webkit-box
            -webkit-box-orient vertical
            -webkit-line-clamp 1
            overflow hidden

  .toc-content-container
    white-space nowrap
    opacity 0
    position sticky
    top: $navbar-height + $spacing-unit
    // border-radius $redefine-border-radius-large
    border-radius $redefine-border-radius-none
    // display none
    // visibility hidden
    // width $toc-container-width
    width 0
    height auto
    max-height calc(100vh - 210px)
    //margin-left 30px
    // z-index $z-index-6
    overflow-y auto
    overflow-x hidden
    float right
    padding 10px
    box-sizing border-box
    box-shadow none !important
    transition opacity 0.3s ease-out 0.3s
    hover-style(
      false,
      0,
      0
    )
    transition-t('display', '0', '0.3', 'ease-out')

    +redefine-tablet()
      left 0 !important
      display none !important

  figure.image-caption
    img
      margin-bottom 0

    figcaption
      margin-top 5px
      margin-bottom 10px
      text-align center
      font-size 0.9rem
      color var(--third-text-color)