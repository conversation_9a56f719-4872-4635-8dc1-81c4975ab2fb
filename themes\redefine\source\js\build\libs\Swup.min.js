!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t||self).Swup=e()}(this,function(){const t=(t,e)=>String(t).toLowerCase().replace(/[\s/_.]+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+|-+$/g,"")||e||"",e=function(t){let{hash:e}=void 0===t?{}:t;return location.pathname+location.search+(e?location.hash:"")},n=function(t,n){void 0===n&&(n={});const i={url:t=t||e({hash:!0}),random:Math.random(),source:"swup",...n};history.pushState(i,"",t)},i=function(t,n){void 0===t&&(t=null),void 0===n&&(n={}),t=t||e({hash:!0});const i={...history.state||{},url:t,random:Math.random(),source:"swup",...n};history.replaceState(i,"",t)},r=new WeakMap;function o(t,e,n,i){if(!t&&!r.has(e))return!1;const o=r.get(e)??new WeakMap;r.set(e,o);const s=o.get(n)??new Set;o.set(n,s);const a=s.has(i);return t?s.add(i):s.delete(i),a&&t}const s=(t,e,n,i)=>{const r=new AbortController;return function(t,e,n,i={}){const{signal:r,base:s=document}=i;if(r?.aborted)return;const{once:a,...c}=i,l=s instanceof Document?s.documentElement:s,h=Boolean("object"==typeof i?i.capture:i),u=i=>{const r=function(t,e){let n=t.target;if(n instanceof Text&&(n=n.parentElement),n instanceof Element&&t.currentTarget instanceof Element){const i=n.closest(e);if(i&&t.currentTarget.contains(i))return i}}(i,t);if(r){const t=Object.assign(i,{delegateTarget:r});n.call(l,t),a&&(l.removeEventListener(e,u,c),o(!1,l,n,d))}},d=JSON.stringify({selector:t,type:e,capture:h});o(!0,l,n,d)||l.addEventListener(e,u,c),r?.addEventListener("abort",()=>{o(!1,l,n,d)})}(t,e,n,i={...i,signal:r.signal}),{destroy:()=>r.abort()}};class a extends URL{constructor(t,e){void 0===e&&(e=document.baseURI),super(t.toString(),e)}get url(){return this.pathname+this.search}static fromElement(t){const e=t.getAttribute("href")||t.getAttribute("xlink:href")||"";return new a(e)}static fromUrl(t){return new a(t)}}class c{constructor(t){this.swup=void 0,this.pages=new Map,this.swup=t}get size(){return this.pages.size}get all(){const t=new Map;return this.pages.forEach((e,n)=>{t.set(n,{...e})}),t}has(t){return this.pages.has(this.resolve(t))}get(t){const e=this.pages.get(this.resolve(t));return e?{...e}:e}set(t,e){t=this.resolve(t),e={...e,url:t},this.pages.set(t,e),this.swup.hooks.callSync("cache:set",{page:e})}update(t,e){t=this.resolve(t);const n={...this.get(t),...e,url:t};this.pages.set(t,n)}delete(t){this.pages.delete(this.resolve(t))}clear(){this.pages.clear(),this.swup.hooks.callSync("cache:clear",void 0)}prune(t){this.pages.forEach((e,n)=>{t(n,e)&&this.delete(n)})}resolve(t){const{url:e}=a.fromUrl(t);return this.swup.resolveUrl(e)}}const l=function(t,e){return void 0===e&&(e=document),e.querySelector(t)},h=function(t,e){return void 0===e&&(e=document),Array.from(e.querySelectorAll(t))},u=()=>new Promise(t=>{requestAnimationFrame(()=>{requestAnimationFrame(()=>{t()})})});function d(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then}const m=t=>window.CSS&&window.CSS.escape?CSS.escape(t):t,f=t=>1e3*Number(t.slice(0,-1).replace(",","."));class p{constructor(t){this.swup=void 0,this.swupClasses=["to-","is-changing","is-rendering","is-popstate","is-animating"],this.swup=t}get selectors(){const{scope:t}=this.swup.visit.animation;return"containers"===t?this.swup.visit.containers:"html"===t?["html"]:Array.isArray(t)?t:[]}get selector(){return this.selectors.join(",")}get targets(){return this.selector.trim()?h(this.selector):[]}add(){this.targets.forEach(t=>t.classList.add(...[].slice.call(arguments)))}remove(){this.targets.forEach(t=>t.classList.remove(...[].slice.call(arguments)))}clear(){this.targets.forEach(t=>{const e=t.className.split(" ").filter(t=>this.isSwupClass(t));t.classList.remove(...e)})}isSwupClass(t){return this.swupClasses.some(e=>t.startsWith(e))}}function v(t){let{to:e,from:n=this.currentPageUrl,hash:i,el:r,event:o}=t;return{from:{url:n},to:{url:e,hash:i},containers:this.options.containers,animation:{animate:!0,wait:!1,name:void 0,scope:this.options.animationScope,selector:this.options.animationSelector},trigger:{el:r,event:o},cache:{read:this.options.cache,write:this.options.cache},history:{action:"push",popstate:!1,direction:void 0},scroll:{reset:!0,target:void 0}}}const g="undefined"!=typeof Symbol?Symbol.iterator||(Symbol.iterator=Symbol("Symbol.iterator")):"@@iterator";function w(t,e,n){if(!t.s){if(n instanceof y){if(!n.s)return void(n.o=w.bind(null,t,e));1&e&&(e=n.s),n=n.v}if(n&&n.then)return void n.then(w.bind(null,t,e),w.bind(null,t,2));t.s=e,t.v=n;const i=t.o;i&&i(t)}}const y=/*#__PURE__*/function(){function t(){}return t.prototype.then=function(e,n){const i=new t,r=this.s;if(r){const t=1&r?e:n;if(t){try{w(i,1,t(this.v))}catch(t){w(i,2,t)}return i}return this}return this.o=function(t){try{const r=t.v;1&t.s?w(i,1,e?e(r):r):n?w(i,1,n(r)):w(i,2,r)}catch(t){w(i,2,t)}},i},t}();function P(t){return t instanceof y&&1&t.s}class k{constructor(t){this.swup=void 0,this.registry=new Map,this.hooks=["animation:out:start","animation:out:await","animation:out:end","animation:in:start","animation:in:await","animation:in:end","animation:skip","cache:clear","cache:set","content:replace","content:scroll","enable","disable","fetch:request","fetch:error","history:popstate","link:click","link:self","link:anchor","link:newtab","page:load","page:view","scroll:top","scroll:anchor","visit:start","visit:end"],this.swup=t,this.init()}init(){this.hooks.forEach(t=>this.create(t))}create(t){this.registry.has(t)||this.registry.set(t,new Map)}exists(t){return this.registry.has(t)}get(t){const e=this.registry.get(t);if(e)return e;console.error(`Unknown hook '${t}'`)}clear(){this.registry.forEach(t=>t.clear())}on(t,e,n){void 0===n&&(n={});const i=this.get(t);if(!i)return console.warn(`Hook '${t}' not found.`),()=>{};const r=i.size+1,o={...n,id:r,hook:t,handler:e};return i.set(e,o),()=>this.off(t,e)}before(t,e,n){return void 0===n&&(n={}),this.on(t,e,{...n,before:!0})}replace(t,e,n){return void 0===n&&(n={}),this.on(t,e,{...n,replace:!0})}once(t,e,n){return void 0===n&&(n={}),this.on(t,e,{...n,once:!0})}off(t,e){const n=this.get(t);n&&e?n.delete(e)||console.warn(`Handler for hook '${t}' not found.`):n&&n.clear()}call(t,e,n){try{const i=this,{before:r,handler:o,after:s}=i.getHandlers(t,n);return Promise.resolve(i.run(r,e)).then(function(){return Promise.resolve(i.run(o,e)).then(function(n){let[r]=n;return Promise.resolve(i.run(s,e)).then(function(){return i.dispatchDomEvent(t,e),r})})})}catch(t){return Promise.reject(t)}}callSync(t,e,n){const{before:i,handler:r,after:o}=this.getHandlers(t,n);this.runSync(i,e);const[s]=this.runSync(r,e);return this.runSync(o,e),this.dispatchDomEvent(t,e),s}run(t,e){try{const n=this,i=[],r=function(t,e,n){if("function"==typeof t[g]){var i,r,o,s=t[g]();if(function t(n){try{for(;!(i=s.next()).done;)if((n=e(i.value))&&n.then){if(!P(n))return void n.then(t,o||(o=w.bind(null,r=new y,2)));n=n.v}r?w(r,1,n):r=n}catch(t){w(r||(r=new y),2,t)}}(),s.return){var a=function(t){try{i.done||s.return()}catch(t){}return t};if(r&&r.then)return r.then(a,function(t){throw a(t)});a()}return r}if(!("length"in t))throw new TypeError("Object is not iterable");for(var c=[],l=0;l<t.length;l++)c.push(t[l]);return function(t,e,n){var i,r,o=-1;return function n(s){try{for(;++o<t.length;)if((s=e(o))&&s.then){if(!P(s))return void s.then(n,r||(r=w.bind(null,i=new y,2)));s=s.v}i?w(i,1,s):i=s}catch(t){w(i||(i=new y),2,t)}}(),i}(c,function(t){return e(c[t])})}(t,function(t){let{hook:r,handler:o,defaultHandler:s,once:a}=t;return Promise.resolve(function(t,e){return void 0===e&&(e=[]),new Promise((n,i)=>{const r=t(...e);d(r)?r.then(n,i):n(r)})}(o,[n.swup.visit,e,s])).then(function(t){i.push(t),a&&n.off(r,o)})});return Promise.resolve(r&&r.then?r.then(function(){return i}):i)}catch(t){return Promise.reject(t)}}runSync(t,e){const n=[];for(const{hook:i,handler:r,defaultHandler:o,once:s}of t){const t=r(this.swup.visit,e,o);n.push(t),d(t)&&console.warn(`Promise returned from handler for synchronous hook '${i}'.Swup will not wait for it to resolve.`),s&&this.off(i,r)}return n}getHandlers(t,e){const n=this.get(t);if(!n)return{found:!1,before:[],handler:[],after:[],replaced:!1};const i=Array.from(n.values()),r=this.sortRegistrations,o=i.filter(t=>{let{before:e,replace:n}=t;return e&&!n}).sort(r),s=i.filter(t=>{let{replace:e}=t;return e}).filter(t=>!0).sort(r),a=i.filter(t=>{let{before:e,replace:n}=t;return!e&&!n}).sort(r),c=s.length>0;let l=[];if(e&&(l=[{id:0,hook:t,handler:e}],c)){const n=s.length-1,i=t=>{const n=s[t-1];return n?(e,r)=>n.handler(e,r,i(t-1)):e};l=[{id:0,hook:t,handler:s[n].handler,defaultHandler:i(n)}]}return{found:!0,before:o,handler:l,after:a,replaced:c}}sortRegistrations(t,e){return(t.priority??0)-(e.priority??0)||t.id-e.id||0}dispatchDomEvent(t,e){document.dispatchEvent(new CustomEvent(`swup:${t}`,{detail:{hook:t,args:e,visit:this.swup.visit}}))}}const S=t=>{if(t&&"#"===t.charAt(0)&&(t=t.substring(1)),!t)return null;const e=decodeURIComponent(t);let n=document.getElementById(t)||document.getElementById(e)||l(`a[name='${m(t)}']`)||l(`a[name='${m(e)}']`);return n||"top"!==t||(n=document.body),n},b=function(t){let{elements:e,selector:n}=t;try{if(!1===n&&!e)return Promise.resolve();let t=[];if(e)t=Array.from(e);else if(n&&(t=h(n,document.body),!t.length))return console.warn(`[swup] No elements found matching animationSelector \`${n}\``),Promise.resolve();const i=t.map(t=>function(t){const{type:e,timeout:n,propCount:i}=function(t,e){const n=window.getComputedStyle(t),i=C(n,`${E}Delay`),r=C(n,`${E}Duration`),o=$(i,r),s=C(n,`${U}Delay`),a=C(n,`${U}Duration`),c=$(s,a);let l=null,h=0,u=0;return e===E?o>0&&(l=E,h=o,u=r.length):e===U?c>0&&(l=U,h=c,u=a.length):(h=Math.max(o,c),l=h>0?o>c?E:U:null,u=l?l===E?r.length:a.length:0),{type:l,timeout:h,propCount:u}}(t);return!(!e||!n)&&new Promise(r=>{const o=`${e}end`,s=performance.now();let a=0;const c=()=>{t.removeEventListener(o,l),r()},l=e=>{if(e.target===t){if(!function(t){return[`${E}end`,`${U}end`].includes(t.type)}(e))throw new Error("Not a transition or animation event.");(performance.now()-s)/1e3<e.elapsedTime||++a>=i&&c()}};setTimeout(()=>{a<i&&c()},n+1),t.addEventListener(o,l)})}(t));return i.filter(Boolean).length>0?Promise.resolve(Promise.all(i)).then(function(){}):(n&&console.warn(`[swup] No CSS animation duration defined on elements matching \`${n}\``),Promise.resolve())}catch(t){return Promise.reject(t)}},E="transition",U="animation";function C(t,e){return(t[e]||"").split(", ")}function $(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map((e,n)=>f(e)+f(t[n])))}const x=function(t){void 0===t&&(t={});try{const r=this,{el:o}=r.visit.trigger;t.referrer=t.referrer||r.currentPageUrl,!1===t.animate&&(r.visit.animation.animate=!1),r.visit.animation.animate||r.classes.clear();const s=t.history||o?.getAttribute("data-swup-history")||void 0;s&&["push","replace"].includes(s)&&(r.visit.history.action=s);const a=t.animation||o?.getAttribute("data-swup-animation")||void 0;return a&&(r.visit.animation.name=a),"object"==typeof t.cache?(r.visit.cache.read=t.cache.read??r.visit.cache.read,r.visit.cache.write=t.cache.write??r.visit.cache.write):void 0!==t.cache&&(r.visit.cache={read:!!t.cache,write:!!t.cache}),delete t.cache,Promise.resolve(function(o,s){try{var a=Promise.resolve(r.hooks.call("visit:start",void 0)).then(function(){function o(){const t=r.animatePageOut();return Promise.resolve(Promise.all([s,t])).then(function(t){let[e]=t;return Promise.resolve(r.renderPage(r.visit.to.url,e)).then(function(){return Promise.resolve(r.animatePageIn()).then(function(){return Promise.resolve(r.hooks.call("visit:end",void 0,()=>r.classes.clear())).then(function(){})})})})}const s=r.hooks.call("page:load",{options:t},function(t,e){try{function n(t){return e.page=t,e.cache=!!i,e.page}let i;return r.visit.cache.read&&(i=r.cache.get(t.to.url)),Promise.resolve(i?n(i):Promise.resolve(r.fetchPage(t.to.url,e.options)).then(n))}catch(o){return Promise.reject(o)}});if(!r.visit.history.popstate){const t=r.visit.to.url+r.visit.to.hash;"replace"===r.visit.history.action||r.visit.to.url===r.currentPageUrl?i(t):n(t,{index:r.currentHistoryIndex+1})}r.currentPageUrl=e();const a=function(){if(r.visit.animation.wait)return Promise.resolve(s).then(function(t){let{html:e}=t;r.visit.to.html=e})}();return a&&a.then?a.then(o):o()})}catch(t){return s(t)}return a&&a.then?a.then(void 0,s):a}(0,function(t){t&&(console.error(t),r.options.skipPopStateHandling=()=>(window.location.href=r.visit.to.url+r.visit.to.hash,!0),window.history.go(-1))}))}catch(t){return Promise.reject(t)}};function A(t,e,n){if(void 0===e&&(e={}),void 0===n&&(n={}),"string"!=typeof t)throw new Error("swup.navigate() requires a URL parameter");if(this.shouldIgnoreVisit(t,{el:n.el,event:n.event}))return void(window.location.href=t);const{url:i,hash:r}=a.fromUrl(t);this.visit=this.createVisit({...n,to:i,hash:r}),this.performNavigation(e)}const j=function(t,e){void 0===e&&(e={});try{const n=this;t=a.fromUrl(t).url;const i={...n.options.requestHeaders,...e.headers};return e={...e,headers:i},Promise.resolve(n.hooks.call("fetch:request",{url:t,options:e},(t,e)=>{let{url:n,options:i}=e;return fetch(n,i)})).then(function(i){const{status:r,url:o}=i;return Promise.resolve(i.text()).then(function(s){if(500===r)throw n.hooks.call("fetch:error",{status:r,response:i,url:o}),new H(`Server error: ${o}`,{status:r,url:o});if(!s)throw new H(`Empty response: ${o}`,{status:r,url:o});const{url:c}=a.fromUrl(o),l={url:c,html:s};return!n.visit.cache.write||e.method&&"GET"!==e.method||t!==c||n.cache.set(l.url,l),l})})}catch(t){return Promise.reject(t)}};class H extends Error{constructor(t,e){super(t),this.url=void 0,this.status=void 0,this.name="FetchError",this.url=e.url,this.status=e.status}}const L=function(){try{let n;const i=this;function e(e){return n?e:Promise.resolve(i.hooks.call("animation:out:start",void 0,e=>{i.classes.add("is-changing","is-leaving","is-animating"),e.history.popstate&&i.classes.add("is-popstate"),e.animation.name&&i.classes.add(`to-${t(e.animation.name)}`)})).then(function(){return Promise.resolve(i.hooks.call("animation:out:await",{skip:!1},function(t,e){let{skip:n}=e;try{return n?Promise.resolve():Promise.resolve(i.awaitAnimations({selector:t.animation.selector})).then(function(){})}catch(t){return Promise.reject(t)}})).then(function(){return Promise.resolve(i.hooks.call("animation:out:end",void 0)).then(function(){})})})}const r=function(){if(!i.visit.animation.animate)return Promise.resolve(i.hooks.call("animation:skip",void 0)).then(function(){n=1})}();return Promise.resolve(r&&r.then?r.then(e):e(r))}catch(o){return Promise.reject(o)}},T=function(t,e){let{html:n}=t,{containers:i}=void 0===e?this.options:e;const r=(new DOMParser).parseFromString(n,"text/html"),o=r.querySelector("title")?.innerText||"";document.title=o;const s=h('[data-swup-persist]:not([data-swup-persist=""])'),a=i.map(t=>{const e=document.querySelector(t),n=r.querySelector(t);return e&&n?(e.replaceWith(n),!0):(e||console.warn(`[swup] Container missing in current document: ${t}`),n||console.warn(`[swup] Container missing in incoming document: ${t}`),!1)}).filter(Boolean);return s.forEach(t=>{const e=t.getAttribute("data-swup-persist"),n=l(`[data-swup-persist="${e}"]`);n&&n!==t&&n.replaceWith(t)}),a.length===i.length},q=function(){const t={behavior:"auto"},{target:e,reset:n}=this.visit.scroll,i=e??this.visit.to.hash;let r=!1;return i&&(r=this.hooks.callSync("scroll:anchor",{hash:i,options:t},(t,e)=>{let{hash:n,options:i}=e;const r=this.getAnchorElement(n);return r&&r.scrollIntoView(i),!!r})),n&&!r&&(r=this.hooks.callSync("scroll:top",{options:t},(t,e)=>{let{options:n}=e;return window.scrollTo({top:0,left:0,...n}),!0})),r},R=function(){try{const t=this;if(!t.visit.animation.animate)return Promise.resolve();const e=t.hooks.call("animation:in:await",{skip:!1},function(e,n){let{skip:i}=n;try{return i?Promise.resolve():Promise.resolve(t.awaitAnimations({selector:e.animation.selector})).then(function(){})}catch(t){return Promise.reject(t)}});return Promise.resolve(u()).then(function(){return Promise.resolve(t.hooks.call("animation:in:start",void 0,()=>{t.classes.remove("is-animating")})).then(function(){return Promise.resolve(e).then(function(){return Promise.resolve(t.hooks.call("animation:in:end",void 0)).then(function(){})})})})}catch(t){return Promise.reject(t)}},N=function(n,r){try{const o=this,{url:s,html:a}=r;return o.classes.remove("is-leaving"),o.isSameResolvedUrl(e(),n)?(o.isSameResolvedUrl(e(),s)||(i(s),o.currentPageUrl=e(),o.visit.to.url=o.currentPageUrl),o.visit.animation.animate&&o.classes.add("is-rendering"),o.visit.to.html=a,Promise.resolve(o.hooks.call("content:replace",{page:r},(e,n)=>{let{page:i}=n;if(!o.replaceContent(i,{containers:e.containers}))throw new Error("[swup] Container mismatch, aborting");e.animation.animate&&(o.classes.add("is-animating","is-changing","is-rendering"),e.animation.name&&o.classes.add(`to-${t(e.animation.name)}`))})).then(function(){return Promise.resolve(o.hooks.call("content:scroll",void 0,()=>o.scrollToContent())).then(function(){return Promise.resolve(o.hooks.call("page:view",{url:o.currentPageUrl,title:document.title})).then(function(){})})})):Promise.resolve()}catch(t){return Promise.reject(t)}},D=function(t){var e;if(e=t,Boolean(e?.isSwupPlugin)){if(t.swup=this,!t._checkRequirements||t._checkRequirements())return t._beforeMount&&t._beforeMount(),t.mount(),this.plugins.push(t),this.plugins}else console.error("Not a swup plugin instance",t)};function I(t){const e=this.findPlugin(t);if(e)return e.unmount(),e._afterUnmount&&e._afterUnmount(),this.plugins=this.plugins.filter(t=>t!==e),this.plugins;console.error("No such plugin",e)}function M(t){return this.plugins.find(e=>e===t||e.name===t||e.name===`Swup${String(t)}`)}function W(t){if("function"!=typeof this.options.resolveUrl)return console.warn("[swup] options.resolveUrl expects a callback function."),t;const e=this.options.resolveUrl(t);return e&&"string"==typeof e?e.startsWith("//")||e.startsWith("http")?(console.warn("[swup] options.resolveUrl needs to return a relative url"),t):e:(console.warn("[swup] options.resolveUrl needs to return a url"),t)}function V(t,e){return this.resolveUrl(t)===this.resolveUrl(e)}const B={animateHistoryBrowsing:!1,animationSelector:'[class*="transition-"]',animationScope:"html",cache:!0,containers:["#swup"],ignoreVisit:function(t,e){let{el:n}=void 0===e?{}:e;return!!n?.closest("[data-no-swup]")},linkSelector:"a[href]",linkToSelf:"scroll",plugins:[],resolveUrl:t=>t,requestHeaders:{"X-Requested-With":"swup",Accept:"text/html, application/xhtml+xml"},skipPopStateHandling:t=>"swup"!==t.state?.source};return class{constructor(t){void 0===t&&(t={}),this.version="4.3.1",this.options=void 0,this.defaults=B,this.plugins=[],this.visit=void 0,this.cache=void 0,this.hooks=void 0,this.classes=void 0,this.currentPageUrl=e(),this.currentHistoryIndex=1,this.clickDelegate=void 0,this.use=D,this.unuse=I,this.findPlugin=M,this.log=()=>{},this.navigate=A,this.performNavigation=x,this.createVisit=v,this.delegateEvent=s,this.fetchPage=j,this.awaitAnimations=b,this.renderPage=N,this.replaceContent=T,this.animatePageIn=R,this.animatePageOut=L,this.scrollToContent=q,this.getAnchorElement=S,this.getCurrentUrl=e,this.resolveUrl=W,this.isSameResolvedUrl=V,this.options={...this.defaults,...t},this.handleLinkClick=this.handleLinkClick.bind(this),this.handlePopState=this.handlePopState.bind(this),this.cache=new c(this),this.classes=new p(this),this.hooks=new k(this),this.visit=this.createVisit({to:""}),this.checkRequirements()&&this.enable()}checkRequirements(){return"undefined"!=typeof Promise||(console.warn("Promise is not supported"),!1)}enable(){try{const t=this,{linkSelector:e}=t.options;return t.clickDelegate=t.delegateEvent(e,"click",t.handleLinkClick),window.addEventListener("popstate",t.handlePopState),t.options.animateHistoryBrowsing&&(window.history.scrollRestoration="manual"),t.options.plugins.forEach(e=>t.use(e)),i(null,{index:1}),Promise.resolve(u()).then(function(){return Promise.resolve(t.hooks.call("enable",void 0,()=>{document.documentElement.classList.add("swup-enabled")})).then(function(){})})}catch(t){return Promise.reject(t)}}destroy(){try{const t=this;return t.clickDelegate.destroy(),window.removeEventListener("popstate",t.handlePopState),t.cache.clear(),t.options.plugins.forEach(e=>t.unuse(e)),Promise.resolve(t.hooks.call("disable",void 0,()=>{document.documentElement.classList.remove("swup-enabled")})).then(function(){t.hooks.clear()})}catch(t){return Promise.reject(t)}}shouldIgnoreVisit(t,e){let{el:n,event:i}=void 0===e?{}:e;const{origin:r,url:o,hash:s}=a.fromUrl(t);return r!==window.location.origin||!(!n||!this.triggerWillOpenNewWindow(n))||!!this.options.ignoreVisit(o+s,{el:n,event:i})}handleLinkClick(t){const e=t.delegateTarget,{href:n,url:r,hash:o}=a.fromElement(e);this.shouldIgnoreVisit(n,{el:e,event:t})||(this.visit=this.createVisit({to:r,hash:o,el:e,event:t}),t.metaKey||t.ctrlKey||t.shiftKey||t.altKey?this.hooks.call("link:newtab",{href:n}):0===t.button&&this.hooks.callSync("link:click",{el:e,event:t},()=>{const e=this.visit.from.url??"";t.preventDefault(),r&&r!==e?this.isSameResolvedUrl(r,e)||this.performNavigation():o?this.hooks.callSync("link:anchor",{hash:o},()=>{i(r+o),this.scrollToContent()}):this.hooks.callSync("link:self",void 0,()=>"navigate"===this.options.linkToSelf?this.performNavigation():this.scrollToContent())}))}handlePopState(t){const n=t.state?.url??location.href;if(this.options.skipPopStateHandling(t))return;if(this.isSameResolvedUrl(e(),this.currentPageUrl))return;const{url:i,hash:r}=a.fromUrl(n);this.visit=this.createVisit({to:i,hash:r,event:t}),this.visit.history.popstate=!0;const o=Number(t.state?.index);o&&(this.visit.history.direction=o-this.currentHistoryIndex>0?"forwards":"backwards"),this.visit.animation.animate=!1,this.visit.scroll.reset=!1,this.visit.scroll.target=!1,this.options.animateHistoryBrowsing&&(this.visit.animation.animate=!0,this.visit.scroll.reset=!0),this.hooks.callSync("history:popstate",{event:t},()=>{this.performNavigation()})}triggerWillOpenNewWindow(t){return!!t.matches('[download], [target="_blank"]')}}});
//# sourceMappingURL=Swup.min.js.map