{"version": 3, "file": "imageViewer.js", "names": ["imageViewer", "isBigImage", "scale", "isMouseDown", "dragged", "currentImgIndex", "lastMouseX", "lastMouseY", "translateX", "translateY", "maskDom", "document", "querySelector", "console", "warn", "targetImg", "showHandle", "isShow", "body", "style", "overflow", "classList", "add", "remove", "lastTime", "dragEndHandle", "event", "stopPropagation", "cursor", "addEventListener", "preventDefault", "rect", "getBoundingClientRect", "offsetX", "clientX", "left", "offsetY", "clientY", "top", "dx", "width", "dy", "height", "oldScale", "deltaY", "Math", "min", "max", "transform", "passive", "currentTime", "Date", "getTime", "deltaX", "imgDoms", "querySelectorAll", "escapeKeyListener", "key", "removeEventListener", "length", "for<PERSON>ach", "img", "index", "src", "handleArrowKeys", "currentImg", "newSrc", "hasAttribute", "getAttribute", "removeAttribute"], "sources": ["0"], "mappings": "eAAe,SAASA,cACtB,IAAIC,GAAa,EACbC,EAAQ,EACRC,GAAc,EACdC,GAAU,EACVC,EAAkB,EAClBC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EAEjB,MAAMC,EAAUC,SAASC,cAAc,2BACvC,IAAKF,EAIH,YAHAG,QAAQC,KACN,mEAKJ,MAAMC,EAAYL,EAAQE,cAAc,OACxC,IAAKG,EAIH,YAHAF,QAAQC,KACN,mFAKJ,MAAME,WAAcC,IAClBN,SAASO,KAAKC,MAAMC,SAAWH,EAAS,SAAW,OACnDA,EACIP,EAAQW,UAAUC,IAAI,UACtBZ,EAAQW,UAAUE,OAAO,SAAS,EAmCxC,IAAIC,EAAW,EACf,MAoBMC,cAAiBC,IACjBvB,GACFuB,EAAMC,kBAERxB,GAAc,EACdY,EAAUI,MAAMS,OAAS,MAAM,EAGjCb,EAAUc,iBAAiB,SA7DPH,IAClBA,EAAMI,iBACN,MAAMC,EAAOhB,EAAUiB,wBACjBC,EAAUP,EAAMQ,QAAUH,EAAKI,KAC/BC,EAAUV,EAAMW,QAAUN,EAAKO,IAC/BC,EAAKN,EAAUF,EAAKS,MAAQ,EAC5BC,EAAKL,EAAUL,EAAKW,OAAS,EAC7BC,EAAWzC,EACjBA,IAAyB,KAAhBwB,EAAMkB,OACf1C,EAAQ2C,KAAKC,IAAID,KAAKE,IAAI,GAAK7C,GAAQ,GAEnCyC,EAAWzC,GAEbM,GAAc+B,GAAMrC,EAAQyC,GAC5BlC,GAAcgC,GAAMvC,EAAQyC,KAG5BnC,EAAa,EACbC,EAAa,GAGfM,EAAUI,MAAM6B,UAAY,aAAaxC,QAAiBC,cAAuBP,IAAQ,GAwC3C,CAAE+C,SAAS,IAC3DlC,EAAUc,iBAAiB,aAtCFH,IACvBA,EAAMI,iBACN3B,GAAc,EACdG,EAAaoB,EAAMQ,QACnB3B,EAAamB,EAAMW,QACnBtB,EAAUI,MAAMS,OAAS,UAAU,GAiCoB,CAAEqB,SAAS,IACpElC,EAAUc,iBAAiB,aA5BPH,IAClB,GAAIvB,EAAa,CACf,MAAM+C,GAAc,IAAIC,MAAOC,UAC/B,GAAIF,EAAc1B,EALL,IAMX,OAEFA,EAAW0B,EACX,MAAMG,EAAS3B,EAAMQ,QAAU5B,EACzBsC,EAASlB,EAAMW,QAAU9B,EAC/BC,GAAc6C,EACd5C,GAAcmC,EACdtC,EAAaoB,EAAMQ,QACnB3B,EAAamB,EAAMW,QACnBtB,EAAUI,MAAM6B,UAAY,aAAaxC,QAAiBC,cAAuBP,KACjFE,GAAU,CACZ,IAakD,CAAE6C,SAAS,IAC/DlC,EAAUc,iBAAiB,UAAWJ,cAAe,CAAEwB,SAAS,IAChElC,EAAUc,iBAAiB,aAAcJ,cAAe,CAAEwB,SAAS,IAEnEvC,EAAQmB,iBAAiB,SAAUH,IAC5BtB,IACHH,GAAa,EACbe,WAAWf,GACXC,EAAQ,EACRM,EAAa,EACbC,EAAa,EACbM,EAAUI,MAAM6B,UAAY,aAAaxC,QAAiBC,cAAuBP,MAEnFE,GAAU,CAAK,IAGjB,MAAMkD,EAAU3C,SAAS4C,iBACvB,gEAGIC,kBAAqB9B,IACP,WAAdA,EAAM+B,KAAoBxD,IAC5BA,GAAa,EACbe,WAAWf,GACXC,EAAQ,EACRM,EAAa,EACbC,EAAa,EACbM,EAAUI,MAAM6B,UAAY,aAAaxC,QAAiBC,cAAuBP,KAEjFS,SAAS+C,oBAAoB,UAAWF,mBAC1C,EAGF,GAAIF,EAAQK,OAAS,EAAG,CACtBL,EAAQM,SAAQ,CAACC,EAAKC,KACpBD,EAAIhC,iBAAiB,SAAS,KAC5BxB,EAAkByD,EAClB7D,GAAa,EACbe,WAAWf,GACXc,EAAUgD,IAAMF,EAAIE,IACpBpD,SAASkB,iBAAiB,UAAW2B,kBAAkB,GACvD,IAGJ,MAAMQ,gBAAmBtC,IACvB,IAAKzB,EAAY,OAEjB,GAAkB,YAAdyB,EAAM+B,KAAmC,cAAd/B,EAAM+B,IACnCpD,GACGA,EAAkB,EAAIiD,EAAQK,QAAUL,EAAQK,WAC9C,IAAkB,cAAdjC,EAAM+B,KAAqC,eAAd/B,EAAM+B,IAG5C,OAFApD,GAAmBA,EAAkB,GAAKiD,EAAQK,MAGpD,CAEA,MAAMM,EAAaX,EAAQjD,GAC3B,IAAI6D,EAASD,EAAWF,IAEpBE,EAAWE,aAAa,cAC1BD,EAASD,EAAWG,aAAa,YACjCH,EAAWF,IAAMG,EACjBD,EAAWI,gBAAgB,aAG7BtD,EAAUgD,IAAMG,CAAM,EAGxBvD,SAASkB,iBAAiB,UAAWmC,gBACvC,CAGF", "ignoreList": []}