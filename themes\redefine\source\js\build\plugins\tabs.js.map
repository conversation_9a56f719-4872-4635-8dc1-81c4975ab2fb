{"version": 3, "file": "tabs.js", "names": ["setTabs", "tabs", "document", "querySelectorAll", "for<PERSON>ach", "tab", "link", "addEventListener", "e", "preventDefault", "stopPropagation", "parentTab", "target", "parentElement", "querySelector", "classList", "remove", "add", "className", "swup", "hooks", "on"], "sources": ["0"], "mappings": "AAAA,SAASA,UACP,IAAIC,EAAOC,SAASC,iBAAiB,mBAChCF,GAELA,EAAKG,SAASC,IACZA,EAAIF,iBAAiB,KAAKC,SAASE,IACjCA,EAAKC,iBAAiB,SAAUC,IAC9BA,EAAEC,iBACFD,EAAEE,kBAEF,MAAMC,EAAYH,EAAEI,OAAOC,cAAcA,cAAcA,cAQvD,OAPAF,EAAUG,cAAc,qBAAqBC,UAAUC,OAAO,UAC9DR,EAAEI,OAAOC,cAAcE,UAAUE,IAAI,UACrCN,EACGG,cAAc,wBACdC,UAAUC,OAAO,UACpBL,EAAUG,cAAcN,EAAEI,OAAOM,WAAWH,UAAUE,IAAI,WAEnD,CAAK,GACZ,GACF,GAEN,CAEA,IACEE,KAAKC,MAAMC,GAAG,YAAarB,QAC7B,CAAE,MAAOQ,GAAI,CAEbN,SAASK,iBAAiB,mBAAoBP", "ignoreList": []}