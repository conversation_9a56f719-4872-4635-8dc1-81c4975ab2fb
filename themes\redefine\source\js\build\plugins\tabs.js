function setTabs(){let e=document.querySelectorAll(".tabs .nav-tabs");e&&e.forEach((e=>{e.querySelectorAll("a").forEach((e=>{e.addEventListener("click",(e=>{e.preventDefault(),e.stopPropagation();const t=e.target.parentElement.parentElement.parentElement;return t.querySelector(".nav-tabs .active").classList.remove("active"),e.target.parentElement.classList.add("active"),t.querySelector(".tab-content .active").classList.remove("active"),t.querySelector(e.target.className).classList.add("active"),!1}))}))}))}try{swup.hooks.on("page:view",setTabs)}catch(e){}document.addEventListener("DOMContentLoaded",setTabs);
//# sourceMappingURL=tabs.js.map