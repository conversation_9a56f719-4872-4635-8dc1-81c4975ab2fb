name: Vercel Production Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
on:
  workflow_dispatch:
  push:
    branches:
    - main

jobs:
  production-deployment:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout theme repository
        uses: actions/checkout@v4
        with:
          path: 'theme' # Checkout the theme repository into a directory named 'theme'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x' # Specify your required Node.js version

      - name: Clone Hexo site repository
        run: git clone https://github.com/EvanNotFound/redefine-demo.git hexo-site

      - name: Move theme into Hexo site
        run: |
          mv theme hexo-site/themes/redefine

      - name: Install dependencies
        run: |
          cd hexo-site
          npm install

      - name: Build Hexo
        run: |
          cd hexo-site
          npm install
          npm uninstall hexo-theme-redefine
          npm run build

      - name: Change directory to hexo-site
        run: echo "HEXO_SITE_DIR=${{ github.workspace }}/hexo-site" >> $GITHUB_ENV

      - name: Install Vercel CLI
        run: npm install -g vercel@22.0.1

      - name: Deploy to Vercel Action
        uses: EvanNotFound/vercel-deployment-for-github-actions@v1.1.0
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          WORKING_DIRECTORY: ${{ env.HEXO_SITE_DIR }}
          VERCEL_SCOPE: ${{ secrets.VERCEL_SCOPE }}
          PRODUCTION: true
          DEPLOY_PR_FROM_FORK: true
          PR_PREVIEW_DOMAIN: "redefine-preview-pr-{PR}.vercel.app"
