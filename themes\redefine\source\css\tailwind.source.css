@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --spacing-unit: 38px;

  --margin-spacing-unit: 38px;

  --container-content: 1000px;
  --container-has-toc-content: 120%;

  --z-index-1: 1001;
  --z-index-2: 1002;
  --z-index-3: 1003;
  --z-index-4: 1004;
  --z-index-5: 1005;
  --z-index-6: 1006;
  --z-index-7: 1007;
  --z-index-8: 1008;
  --z-index-9: 1009;

  --radius-none: 0px;
  --radius-xsmall: 4px;
  --radius-small: 9px;
  --radius-medium: 14px;
  --radius-large: 18px;
  --radius-xlarge: 24px;
  --radius-xxlarge: 48px;

  --font-english: Geist Variable, sans-serif;
  --font-default: Geist Variable, system-ui, sans-serif;

  --text-default: 16px;

  --leading-default: 1.5;

  --color-primary: var(--primary-color);
  --color-nav-color-1: var(--nav-color-1);
  --color-nav-color-2: var(--nav-color-2);
  --color-nav-color-bg: var(--nav-color-bg);
  --color-background-color: var(--background-color);
  --color-background-color-transparent: var(--background-color-transparent);
  --color-background-color-transparent-15: var(
    --background-color-transparent-15
  );
  --color-background-color-transparent-40: var(
    --background-color-transparent-40
  );
  --color-background-color-transparent-80: var(
    --background-color-transparent-80
  );
  --color-second-background-color: var(--second-background-color);
  --color-third-background-color: var(--third-background-color);
  --color-third-background-color-transparent: var(
    --third-background-color-transparent
  );
  --color-first-text-color: var(--first-text-color);
  --color-second-text-color: var(--second-text-color);
  --color-third-text-color: var(--third-text-color);
  --color-fourth-text-color: var(--fourth-text-color);
  --color-default-text-color: var(--default-text-color);
  --color-invert-text-color: var(--invert-text-color);
  --color-border-color: var(--border-color);
  --color-selection-color: var(--selection-color);
  --color-shadow-color-1: var(--shadow-color-1);
  --color-shadow-color-2: var(--shadow-color-2);
  --color-shadow-hover-color: var(--shadow-hover-color);
  --color-scrollbar-color: var(--scrollbar-color);
  --color-scrollbar-color-hover: var(--scrollbar-color-hover);
  --color-scroll-bar-bg-color: var(--scroll-bar-bg-color);
  --color-link-color: var(--link-color);
  --color-copyright-info-color: var(--copyright-info-color);
  --color-avatar-background-color: var(--avatar-background-color);
  --color-pjax-progress-bar-color: var(--pjax-progress-bar-color);
  --color-archive-timeline-last-child-color: var(
    --archive-timeline-last-child-color
  );
  --color-note-blue-title-bg: var(--note-blue-title-bg);
  --color-note-red-title-bg: var(--note-red-title-bg);
  --color-note-cyan-title-bg: var(--note-cyan-title-bg);
  --color-note-green-title-bg: var(--note-green-title-bg);
  --color-note-yellow-title-bg: var(--note-yellow-title-bg);
  --color-note-gray-title-bg: var(--note-gray-title-bg);
  --color-note-type-title-bg: var(--note-type-title-bg);
  --color-note-black-title-bg: var(--note-black-title-bg);
  --color-note-purple-title-bg: var(--note-purple-title-bg);
  --color-home-banner-img: var(--home-banner-img);
  --color-home-banner-text-color: var(--home-banner-text-color);
  --color-home-banner-icons-container-border-color: var(
    --home-banner-icons-container-border-color
  );
  --color-home-banner-icons-container-background-color: var(
    --home-banner-icons-container-background-color
  );
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --shadow-redefine:
    0px 6px 24px 0px var(--shadow-color-2),
    0px 0px 0px 1px var(--shadow-color-1);
  --shadow-redefine-hover:
    0px 6px 24px 0px var(--shadow-color-2),
    0px 0px 0px 1px var(--shadow-color-1),
    0px 0px 0px 1px inset var(--shadow-color-1);
  --shadow-redefine-flat:
    0px 1px 4px 0px var(--shadow-color-2), 0px 0px 0px 1px var(--shadow-color-1);
  --shadow-redefine-flat-hover:
    0px 1px 4px 0px var(--shadow-color-2),
    0px 0px 0px 1px var(--shadow-color-1),
    0px 0px 0px 1px inset var(--shadow-color-1);
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;

        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;

        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;

        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;

        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;

        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;

        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;

        --radius: 0.5rem;

        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;

        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;

        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;

        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;

        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;

        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;

        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;

        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;

        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;     /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;            /* Chrome, Safari and Opera */
}