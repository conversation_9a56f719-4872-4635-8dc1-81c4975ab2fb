<footer class="footer mt-5 py-5 h-auto text-base text-third-text-color relative border-t-2 border-t-border-color">
    <div class="info-container py-3 text-center">
        <% if (theme.footer.hasOwnProperty('customize') && theme.footer.customize) { %>
            <div class="customize-info my-1"><%- theme.footer.customize %></div>
        <% } %>
        <div class="text-center">
            &copy;
            <% if (theme.footer.hasOwnProperty('start') && theme.footer.start) { %>
              <span><%= date(new Date(theme.footer.start), 'YYYY') %></span>
              -
            <% } %>
            <%= date(new Date(), 'YYYY') %>&nbsp;&nbsp;<%- theme.footer.icon || '<i class="fa-regular fa-computer-classic"></i>' %>&nbsp;&nbsp;<a href="<%= config.root %>"><%= theme.info.author || config.author %></a>
            <% if (theme.footer.hasOwnProperty('statistics') && theme.footer.statistics) {%>
                <%
                    const sitePostsCount = site.posts.length;
                    let siteWordCount = null;
                    try{
                        siteWordCount = totalcount(site);
                    } catch (e) {
                        siteWordCount = null;
                    }
                %>
                <p class="post-count space-x-0.5">
                    <span>
                        <%- __("site_posts", sitePostsCount) %>
                    </span>
                    <% if (siteWordCount) {%>
                        <span>
                            <%- __("site_wordcount", siteWordCount) %>
                        </span>
                    <% } %>
                </p>
            <% } %>
        </div>
        <% if (theme.global.website_counter.enable === true) { %>
            <script <%= theme.global.single_page === true && 'data-swup-reload-script' %> src="<%= theme.global.website_counter.url ? theme.global.website_counter.url : "https://vercount.one/js" %>"></script>
            <div class="relative text-center lg:absolute lg:right-[20px] lg:top-1/2 lg:-translate-y-1/2 lg:text-right">
                <% if (theme.global.website_counter.site_uv) { %>
                    <span id="busuanzi_container_site_uv" class="lg:!block">
                        <span class="text-sm"><%- __('site_uv') %></span>
                        <span id="busuanzi_value_site_uv"></span>
                    </span>
                <% } %>
                <% if (theme.global.website_counter.site_pv) { %>
                    <span id="busuanzi_container_site_pv" class="lg:!block">
                        <span class="text-sm"><%- __('site_pv') %></span>
                        <span id="busuanzi_value_site_pv"></span>
                    </span>
                <% } %>
            </div>
        <% } %>
        <div class="relative text-center lg:absolute lg:left-[20px] lg:top-1/2 lg:-translate-y-1/2 lg:text-left">
            <span class="lg:block text-sm"><%- __('powered_by', '<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg class="relative top-[2px] inline-block align-baseline" version="1.1" id="圖層_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="1rem" height="1rem" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path fill="#0E83CD" d="M256.4,25.8l-200,115.5L56,371.5l199.6,114.7l200-115.5l0.4-230.2L256.4,25.8z M349,354.6l-18.4,10.7l-18.6-11V275H200v79.6l-18.4,10.7l-18.6-11v-197l18.5-10.6l18.5,10.8V237h112v-79.6l18.5-10.6l18.5,10.8V354.6z"/></svg><a target="_blank" class="text-base" href="https://hexo.io">Hexo</a>') %></span>
            <%# 保留主题信息是对作者的尊重，感谢！ %><span class="text-sm lg:block"><%- __('theme') %>&nbsp;<a class="text-base" target="_blank" href="https://github.com/EvanNotFound/hexo-theme-redefine">Redefine v<%- getThemeVersion() %></a></span><%# 保留主题信息是对作者的尊重，感谢！ %>
        </div>
        <% if (theme.footer.icp.enable && theme.footer.icp.hasOwnProperty('number')) { %>
            <div class="icp-info my-1"><a target="_blank" rel="nofollow" href="
                <% if (theme.footer.icp.hasOwnProperty('url') && theme.footer.icp.url) { %>
                    <%= theme.footer.icp.url %>
                <% } else { %>
                https://beian.miit.gov.cn
                <% } %>
                "><%= theme.footer.icp.number %></a></div>
        <% } %>
        <% if (theme.footer.runtime === true) {%>
            <div>
                <%= __('runtime') %> <span class="odometer" id="runtime_days" ></span> <%= __('days') %> <span class="odometer" id="runtime_hours"></span> <%= __('hours') %> <span class="odometer" id="runtime_minutes"></span> <%= __('minutes') %> <span class="odometer" id="runtime_seconds"></span> <%= __('seconds') %>
            </div>
        <% } %>
        <% if (theme.footer.runtime === true) {%>
            <script <%= theme.global.single_page === true && 'data-swup-reload-script' %>>
                try {
                    function odometer_init() {
                    const elements = document.querySelectorAll('.odometer');
                    elements.forEach(el => {
                        new Odometer({
                            el,
                            format: '( ddd).dd',
                            duration: 200
                        });
                    });
                    }
                    odometer_init();
                } catch (error) {}
            </script>
        <% } %>
        <% if (theme.inject.enable === true) { %>
            <% for (let i in theme.inject.footer) { %>
                <% if (theme.inject.footer[i] !== null ){ %>

                    <% if (theme.global.single_page === true) { %>
                        <%- theme.inject.footer[i].replace("<script", "<script data-swup-reload-script") %>
                    <% } else { %>
                        <%- theme.inject.footer[i] %>
                <% } } %>
        <% } }%>
        
    </div>  
</footer>