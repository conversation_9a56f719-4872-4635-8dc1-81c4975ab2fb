const toggleStyle=(e,t,o,l)=>{e.style[t]=e.style[t]===o?l:o},setupCategoryList=()=>{const e=Array.from(document.querySelectorAll(".all-category-list-item")).filter((e=>e.parentElement.classList.contains("all-category-list")));e.forEach((t=>{const o=t.querySelectorAll(".all-category-list-child");o.forEach((e=>{e.style.maxHeight="0px",e.style.marginTop="0px"})),t.addEventListener("click",(()=>{const l=t.offsetTop;o.forEach((e=>{toggleStyle(e,"maxHeight","0px","1000px"),toggleStyle(e,"marginTop","0px","15px")})),e.forEach((e=>{if(e.offsetTop===l&&e!==t){e.querySelectorAll(".all-category-list-child").forEach((e=>{toggleStyle(e,"maxHeight","0px","1000px"),toggleStyle(e,"marginTop","0px","15px")}))}}))}))}))};try{swup.hooks.on("page:view",setupCategoryList)}catch(e){console.error(e)}document.addEventListener("DOMContentLoaded",setupCategoryList);
//# sourceMappingURL=categoryList.js.map