export const config={usrTypeSpeed:theme.home_banner.subtitle.typing_speed,usrBackSpeed:theme.home_banner.subtitle.backing_speed,usrBackDelay:theme.home_banner.subtitle.backing_delay,usrStartDelay:theme.home_banner.subtitle.starting_delay,usrLoop:theme.home_banner.subtitle.loop,usrSmartBackspace:theme.home_banner.subtitle.smart_backspace,usrHitokotoAPI:theme.home_banner.subtitle.hitokoto.api};export default function initTyped(e){const{usrTypeSpeed:t,usrBackSpeed:o,usrBackDelay:a,usrStartDelay:n,usrLoop:s,usrSmartBackspace:r,usrHitokotoAPI:i}=config;function typing(i){new Typed("#"+e,{strings:[i],typeSpeed:t||100,smartBackspace:r||!1,backSpeed:o||80,backDelay:a||1500,loop:s||!1,startDelay:n||500})}if(theme.home_banner.subtitle.hitokoto.enable)fetch(i).then((e=>e.json())).then((e=>{e.from_who&&theme.home_banner.subtitle.hitokoto.show_author?typing(e.hitokoto+"——"+e.from_who):typing(e.hitokoto)})).catch(console.error);else{const i=[...theme.home_banner.subtitle.text];if(document.getElementById(e)){new Typed("#"+e,{strings:i,typeSpeed:t||100,smartBackspace:r||!1,backSpeed:o||80,backDelay:a||1500,loop:s||!1,startDelay:n||500})}}}
//# sourceMappingURL=typed.js.map