<div align="right">
  <a title="en" href="README.md"><img src="https://img.shields.io/badge/-English-545759?style=for-the-badge" alt="english"></a>
  <a title="zh-CN" href="README_zh-CN.md">  <img src="https://img.shields.io/badge/-%E7%AE%80%E4%BD%93%E4%B8%AD%E6%96%87-545759?style=for-the-badge" alt="简体中文"></a>
  <img src="https://img.shields.io/badge/-%E7%B9%81%E9%AB%94%E4%B8%AD%E6%96%87-A31F34?style=for-the-badge" alt="language">
</div>

<a href="https://redefine.ohevan.com"><img align="center" src="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/f2ff10f6-a740-4120-ba04-1b2a518fb019"  alt="Redefine"></a>



# hexo-theme-redefine

"Redefine" 是一款簡潔、快速、純粹的 Hexo 主題，其簡約之美遙非平凡，讓人領略到設計的力量。本主題不僅包含諸多實用豐富的功能，而且賦予了精緻而出色的設計理念。

本主題源自 [hexo-theme-keep](https://github.com/XPoet/hexo-theme-keep) 的早期版本，進一步進行了精心打磨和擴展。在保留了原主題的精煉基礎上，我們優化了整體風格，引入了豐富的寫作模組，並增添了多種插件支持。同時，大幅提高了自定義的靈活度，旨在讓您能夠更加便捷、順暢地運用本主題，打造出獨具匠心的個人博客。
<p align="center">
    <a href="https://www.npmjs.com/package/hexo-theme-redefine">
        <img src="https://img.shields.io/npm/v/hexo-theme-redefine?color=F38181&amp;label=version&amp;logo=npm&amp;logoColor=F38181&amp;style=for-the-badge" referrerpolicy="no-referrer" alt="NPM version" />
    </a>
    <a href="https://www.npmjs.com/package/hexo-theme-redefine">
        <img src="https://img.shields.io/npm/dm/hexo-theme-redefine?color=FCE38A&amp;logo=npm&amp;logoColor=FCE38A&amp;style=for-the-badge" referrerpolicy="no-referrer" alt="npm downloads" />
    </a>
    <a href="https://www.npmjs.com/package/hexo-theme-redefine">
        <img src="https://img.shields.io/npm/dt/hexo-theme-redefine?color=95E1D3&amp;label=total&amp;logo=npm&amp;logoColor=95E1D3&amp;style=for-the-badge" referrerpolicy="no-referrer" alt="npm-total" />
    </a>
    <a href="https://hexo.io"><img src="https://img.shields.io/badge/hexo-%3E=5.0.0-8caaee?style=for-the-badge&amp;logo=hexo&amp;logoColor=8caaee" referrerpolicy="no-referrer" alt="Required Hexo version" /></a>
    <img src="https://img.shields.io/badge/node-%3E=12.0-a6d189?style=for-the-badge&amp;logo=node.js&amp;logoColor=a6d189" referrerpolicy="no-referrer" alt="NodeJS Version" />
</p>



<picture>
  <source media="(prefers-color-scheme: dark)" srcset="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/337c1801-7a59-45af-a02a-583508be69a5" />
  <source media="(prefers-color-scheme: light)" srcset="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/d88a5544-c86e-46ab-8e52-0582b437f989" />
  <img alt="Cover Image" src="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/d88a5544-c86e-46ab-8e52-0582b437f989" />
</picture>

<picture>
  <source media="(prefers-color-scheme: dark)" srcset="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/5d51b48d-7b08-4da0-a304-933424739203" />
  <source media="(prefers-color-scheme: light)" srcset="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/c6df4b81-557d-4e0b-8038-b056075d0fa4" />
  <img alt="Pages Image" src="https://github.com/EvanNotFound/hexo-theme-redefine/assets/68590232/c6df4b81-557d-4e0b-8038-b056075d0fa4" />
</picture>

## 🌐 在線演示站

- [EvanNotFound's Blog](https://ohevan.com)
- [Theme Redefine 演示站点](https://redefine.ohevan.com)
- [Redefine 用戶牆](https://redefine.ohevan.com/showcase)

如果你也在使用 Redefine，歡迎在前往 [Redefine 用戶牆](https://redefine.ohevan.com/showcase) 添加你的博客鏈接。

## ⛰️ 部分功能

- [筆記模塊](https://redefine-docs.ohevan.com/modules/notes)
- [友鏈樣式](https://redefine-docs.ohevan.com/page_templates/friends)
- [數學公式](https://redefine-docs.ohevan.com/plugins/mathjax)
- 代碼塊語言顯示
- Light/Dark 模式切換
- [Font Awesome 6.2.1 Pro](https://redefine-docs.ohevan.com/basic/fontawesome)（包含 Duotone/Regular/Thin 等不同樣式）
- [下拉菜單](https://redefine-docs.ohevan.com/dhome/navbar#%E9%93%BE%E6%8E%A5%E5%88%97%E8%A1%A8)
- [可自定義頁腳](https://redefine-docs.ohevan.com/footer)
- [網站運行時間顯示](https://redefine-docs.ohevan.com/footer#%E8%BF%90%E8%A1%8C%E6%97%B6%E9%97%B4)
- [文章頭圖](https://redefine-docs.ohevan.com/article_customize/banner)
- [Mermaid JS 支持](https://redefine-docs.ohevan.com/plugins/mermaid)
- SEO 友好
- [Aplayer 音樂播放器支持](https://redefine-docs.ohevan.com/plugins/aplayer)
- [說說模塊](https://redefine-docs.ohevan.com/shuoshuo)
- [自定義字體](https://redefine-docs.ohevan.com/basic/global#%E8%87%AA%E5%AE%9A%E4%B9%89%E5%AD%97%E4%BD%93)
- Tailwind CSS 支持

## ☁️ 安裝

如果你的 Hexo 版本在 `5.0` 及以上，推薦通過 `npm` 安裝

```sh
$ cd your-hexo-site
$ npm install hexo-theme-redefine@latest
```

或者使用 git 克隆

```sh
$ cd your-hexo-site
$ git clone https://github.com/EvanNotFound/hexo-theme-redefine.git themes/redefine
```

安裝完成後，在 Hexo 配置文件 `_config.yml` 中將 `theme` 設置為 `Redefine`。

```yaml
theme: redefine
```



## ⏫ 更新

Theme Redefine 經常發布新版本，你可以透過如下命令更新 Theme Redefine。

通過 `npm` 安裝最新版本：

```sh
$ npm install hexo-theme-redefine@latest
```

通過 `git` 更新到最新的 `main` 分支：

```sh
$ cd themes/redefine
$ git pull
```



## 📄 文檔

請閱讀 [Redefine 主題官方文檔](https://redefine-docs.ohevan.com/) 進行主題配置與安裝，非常簡單易懂。

## ☕ 支持

歡迎 **pull request** 或者 提交 **issues**.

如有問題，請發郵件到 [<EMAIL>](mailto:<EMAIL>). 我會及時回复

如果你覺得主題還不錯的話，歡迎給我 Github 點個 Star，謝謝

如果你在使用 [Typora](https://typora.io/) 編輯器寫文章，歡迎查看我寫的 [Typora Redefine 主題](https://github.com/EvanNotFound/typora-theme-redefine)，按照本 Hexo 主題樣式編寫，讓你可以直接預覽文章效果，更好排版。

## 💗 贊助

非常感謝所有贊助者的支持，你們的支持是我維護這個項目的動力。

如果你覺得這個項目還不錯，歡迎給我買杯咖啡，給 CDN 續命續久一點，感謝

所有贊助者名單：[贊助者名單](https://github.com/EvanNotFound/hexo-theme-redefine/blob/dev/DONATION.md)

🎉 **想体验 OpenAI 的 ChatGPT Plus 会员？官网 20 美元每月太贵了？不用担心！**

🚀 立即加入我的 [GPT Plus Share](https://gpt.oknice.ca) GPT Plus 共享站，ChatGPT Plus 随心用。

- ✅ 支持 OpenAI 最新所有模型，包括 `GPT-4o`, `o3` 系列模型，支持高级图片生成
- ✅ 后台大量官方正版账号号池，随心使用
- ✅ 每月仅需低至 23.99 人民币，支持一天免费试用
- ✅ 无需梯子，国内免翻访问！
- ✅ 由 Evan 本人运营，售后保证，已稳定运行超过一年

[![gpt-billboard](https://github.com/user-attachments/assets/f049fb37-0587-4af9-b59d-b1910d310549)](https://www.gptplus.ca/home)


## 🌟 Star 記錄

<a href="https://star-history.com/#EvanNotFound/hexo-theme-redefine&Date">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=EvanNotFound/hexo-theme-redefine&type=Date&theme=dark" />
    <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=EvanNotFound/hexo-theme-redefine&type=Date" />
    <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=EvanNotFound/hexo-theme-redefine&type=Date" />
  </picture>
</a>

## 💻 主题开发

如果你想參與主題開發，請 `clone` 本專案的 `dev` 分支，然後在 `dev` 分支上進行開發。

同時，請查看 [Redefine 主題開發文件](https://redefine-docs.ohevan.com/developer)。
