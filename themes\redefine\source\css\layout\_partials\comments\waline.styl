:root
    --waline-font-size 1rem
    --waline-white #fff
    --waline-light-grey #999
    --waline-dark-grey #666
    --waline-theme-color var(--primary-color)
    --waline-active-color lighten($primary-color, 10%)
    --waline-color #444
    --waline-bgcolor var(--background-color)
    --waline-bgcolor-light var(--second-background-color)
    --waline-bgcolor-hover var(--second-background-color)
    --waline-border-color #ddd
    --waline-disable-bgcolor #f8f8f8
    --waline-disable-color #000
    --waline-code-bgcolor #282c34
    --waline-bq-color var(--background-color)
    --waline-avatar-size 3.25rem
    --waline-m-avatar-size calc((var(--waline-avatar-size) * 9 / 13))
    --waline-badge-color #3498db
    --waline-badge-font-size 0.75em
    --waline-info-bgcolor #f8f8f8
    --waline-info-color #999
    --waline-info-font-size 0.625em
    --waline-border none
    --waline-avatar-radius 28%
    // --redefine-box-shadow var(--redefine-box-shadow-flat) !important

[data-waline]
    font-size var(--waline-font-size)
    text-align start

[dir=rtl] [data-waline]
    direction rtl

[data-waline] *
    box-sizing content-box
    line-height 1.75

[data-waline] p
    color var(--waline-color)

[data-waline] a
    position relative
    display inline-block
    color var(--waline-theme-color)
    text-decoration none
    word-break break-word
    cursor pointer

[data-waline] a:hover
    color var(--waline-active-color)

[data-waline] img
    max-width 100%
    max-height 400px
    border none

[data-waline] hr
    margin 0.825em 0
    border-style dashed
    border-color var(--second-background-color)

[data-waline] code, [data-waline] pre
    margin 0
    padding 0.2em 0.4em
    border-radius 3px
    background var(--second-background-color)
    font-size 85%

[data-waline] pre
    overflow auto
    padding 10px
    line-height 1.45

[data-waline] pre::-webkit-scrollbar
    width 6px
    height 6px

[data-waline] pre::-webkit-scrollbar-track-piece:horizontal
    -webkit-border-radius 6px
    border-radius 6px
    background rgba(0, 0, 0, 0.1)

[data-waline] pre::-webkit-scrollbar-thumb:horizontal
    width 6px
    -webkit-border-radius 6px
    border-radius 6px
    background var(--waline-theme-color)

[data-waline] pre code
    padding 0
    background rgba(0, 0, 0, 0)
    color var(--waline-color)
    white-space pre-wrap
    word-break keep-all

[data-waline] blockquote
    margin 0.5em 0
    padding 0.5em 0 0.5em 1em
    border-inline-start 8px solid var(--waline-bq-color)
    color var(--waline-dark-grey)

[data-waline] blockquote>p
    margin 0

[data-waline] ol, [data-waline] ul
    margin-inline-start 1.25em
    padding 0

[data-waline] input[type=checkbox], [data-waline] input[type=radio]
    display inline-block
    vertical-align middle
    margin-top -2px

.wl-btn
    display inline-block
    vertical-align middle
    min-width 2.5em
    margin-bottom 0
    padding 0.5em 1em
    border 1px solid var(--waline-border-color)
    border-radius 0.5em
    background rgba(0, 0, 0, 0)
    color var(--waline-color)
    font-weight 400
    font-size 0.75em
    line-height 1.5
    text-align center
    white-space nowrap
    cursor pointer
    user-select none
    transition-duration 0.4s
    touch-action manipulation

.wl-btn:hover, .wl-btn:active
    border-color var(--waline-theme-color)
    color var(--waline-theme-color)

.wl-btn:disabled
    border-color var(--waline-border-color)
    background var(--waline-disable-bgcolor)
    color var(--waline-disable-color)
    cursor not-allowed

.wl-btn.primary
    border-color var(--waline-theme-color)
    background var(--waline-theme-color)
    color var(--waline-white)

.wl-btn.primary:hover, .wl-btn.primary:active
    border-color var(--waline-active-color)
    background var(--waline-active-color)
    color var(--waline-white)

.wl-btn.primary:disabled
    border-color var(--waline-border-color)
    background var(--waline-disable-bgcolor)
    color var(--waline-disable-color)
    cursor not-allowed

.wl-loading
    text-align center

.wl-loading svg
    margin 0 auto

.wl-comment
    position relative
    display flex
    margin-bottom 0.75em

.wl-close
    position absolute
    top -4px
    inset-inline-end -4px
    padding 0
    border none
    background rgba(0, 0, 0, 0)
    line-height 1
    cursor pointer

.wl-login-info
    max-width 80px
    margin-top 0.75em
    text-align center

.wl-logout-btn
    position absolute
    top -10px
    inset-inline-end -10px
    padding 3px
    border none
    background rgba(0, 0, 0, 0)
    line-height 0
    cursor pointer

.wl-avatar
    position relative
    width var(--waline-avatar-size)
    height var(--waline-avatar-size)
    margin 0 auto
    border var(--waline-border)
    border-radius var(--waline-avatar-radius)

@media (max-width 720px)
    .wl-avatar
        width var(--waline-m-avatar-size)
        height var(--waline-m-avatar-size)

.wl-avatar img
    width 100%
    height 100%
    border-radius var(--waline-avatar-radius)

.wl-login-nick
    display block
    color var(--waline-theme-color)
    font-size 0.75em
    word-break break-all

.wl-panel
    position relative
    flex-shrink 1
    width 100%
    margin 0.5em
    border var(--waline-border)
    border-radius 0.75em
    background var(--background-color)
    box-shadow var(--redefine-box-shadow-flat)

    &:hover
        box-shadow var(--redefine-box-shadow-flat-hover)

.wl-header
    display flex
    overflow hidden
    padding 0 4px
    border-bottom 2px dashed var(--waline-border-color)
    border-top-left-radius 0.75em
    border-top-right-radius 0.75em

@media (max-width 580px)
    .wl-header
        display block

.wl-header label
    min-width 40px
    padding 0.75em 0.5em
    color var(--waline-color)
    font-size 0.75em
    text-align center

.wl-header input
    flex 1
    width 0
    padding 0.5em
    background rgba(0, 0, 0, 0)
    font-size 0.625em
    resize none

.wl-header-item
    display flex
    flex 1

@media (max-width 580px)
    .wl-header-item:not(:last-child)
        border-bottom 2px dashed var(--waline-border-color)

.wl-header-1 .wl-header-item
    width 100%

.wl-header-2 .wl-header-item
    width 50%

@media (max-width 580px)
    .wl-header-2 .wl-header-item
        flex 0
        width 100%

.wl-header-3 .wl-header-item
    width 33.33%

@media (max-width 580px)
    .wl-header-3 .wl-header-item
        width 100%

.wl-editor
    position relative
    width calc(100% - 1em)
    min-height 8.75em
    margin 0.75em 0.5em
    border-radius 0.5em
    background rgba(0, 0, 0, 0)
    font-size 0.875em
    resize vertical

.wl-editor, .wl-input
    max-width 100%
    border none
    color var(--waline-color)
    outline none
    transition all 0.25s ease

.wl-editor:focus, .wl-input:focus
    background var(--second-background-color)

.wl-preview
    padding 0 0.5em 0.5em

.wl-preview h4
    margin 0.25em
    font-weight bold
    font-size 0.9375em

.wl-preview .wl-content
    min-height 1.25em
    padding 0.25em
    word-break break-word
    hyphens auto

.wl-preview .wl-content>*:first-child
    margin-top 0

.wl-preview .wl-content>*:last-child
    margin-bottom 0

.wl-footer
    position relative
    display flex
    flex-wrap wrap
    margin 0.5em 0.75em

.wl-actions
    display flex
    flex 2
    align-items center

.wl-action
    display inline-flex
    align-items center
    justify-content center
    width 1.5em
    height 1.5em
    margin 2px
    padding 0
    border none
    background rgba(0, 0, 0, 0)
    color var(--waline-color)
    font-size 16px
    cursor pointer

.wl-action:hover
    color var(--waline-theme-color)

.wl-action.active
    color var(--waline-active-color)

#wl-image-upload
    display none

#wl-image-upload:focus+label
    color var(--waline-color)

#wl-image-upload:focus-visible+label
    outline -webkit-focus-ring-color auto 1px

.wl-info
    display flex
    flex 3
    align-items center
    justify-content flex-end

.wl-info .wl-text-number
    color var(--waline-info-color)
    font-size 0.75em

.wl-info .wl-text-number .illegal
    color red

.wl-info button
    margin-inline-start 0.75em

.wl-info button svg
    display block
    margin 0 auto
    line-height 18px

.wl-emoji-popup
    position absolute
    top 100%
    inset-inline-start 1.25em
    z-index 10
    max-width 526px
    border var(--waline-border)
    border-radius 6px
    background var(--background-color)
    box-shadow var(--redefine-box-shadow-flat)
    opacity 0
    visibility hidden
    transition transform 0.2s ease-out, opacity 0.2s ease-out
    transform scale(0.9, 0.9)
    transform-origin 0 0

.wl-emoji-popup.display
    opacity 1
    visibility visible
    transform none

.wl-emoji-popup button
    display inline-block
    vertical-align middle
    width 2em
    margin 0.125em
    padding 0
    border-width 0
    background rgba(0, 0, 0, 0)
    font-size inherit
    line-height 2
    text-align center
    cursor pointer

.wl-emoji-popup button:hover
    background var(--waline-bgcolor-hover)

.wl-emoji-popup .wl-emoji
    display inline-block
    vertical-align middle
    max-width 1.5em
    max-height 1.5em

.wl-emoji-popup .wl-tab-wrapper
    overflow-y auto
    max-height 145px
    padding 0.5em

.wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar
    width 6px
    height 6px

.wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar-track-piece:vertical
    -webkit-border-radius 6px
    border-radius 6px
    background rgba(0, 0, 0, 0.1)

.wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar-thumb:vertical
    width 6px
    -webkit-border-radius 6px
    border-radius 6px
    background var(--waline-theme-color)

.wl-emoji-popup .wl-tabs
    position relative
    overflow-x auto
    padding 0 6px
    white-space nowrap

.wl-emoji-popup .wl-tabs::before
    content ' '
    position absolute
    top 0
    right 0
    left 0
    z-index 2
    height 1px
    background var(--waline-border-color)

.wl-emoji-popup .wl-tabs::-webkit-scrollbar
    width 6px
    height 6px

.wl-emoji-popup .wl-tabs::-webkit-scrollbar-track-piece:horizontal
    -webkit-border-radius 6px
    border-radius 6px
    background rgba(0, 0, 0, 0.1)

.wl-emoji-popup .wl-tabs::-webkit-scrollbar-thumb:horizontal
    height 6px
    -webkit-border-radius 6px
    border-radius 6px
    background var(--waline-theme-color)

.wl-emoji-popup .wl-tab
    position relative
    margin 0
    padding 0 0.5em

.wl-emoji-popup .wl-tab.active
    z-index 3
    border 1px solid var(--waline-border-color)
    border-top-width 0
    border-bottom-right-radius 6px
    border-bottom-left-radius 6px
    background var(--background-color)

.wl-gif-popup
    position absolute
    top 100%
    inset-inline-start 1.25em
    z-index 10
    width calc(100% - 3em)
    padding 0.75em 0.75em 0.25em
    border var(--waline-border)
    border-radius 6px
    background var(--background-color)
    box-shadow var(--redefine-box-shadow-flat)
    opacity 0
    visibility hidden
    transition transform 0.2s ease-out, opacity 0.2s ease-out
    transform scale(0.9, 0.9)
    transform-origin 0 0

.wl-gif-popup.display
    opacity 1
    visibility visible
    transform none

.wl-gif-popup input
    box-sizing border-box
    width 100%
    margin-bottom 10px
    padding 3px 5px
    border var(--waline-border)

.wl-gif-popup img
    display block
    box-sizing border-box
    width 100%
    border-width 2px
    border-style solid
    border-color #fff
    cursor pointer

.wl-gif-popup img:hover
    border-color var(--waline-theme-color)
    border-radius 2px

.wl-gallery
    display flex
    overflow-y auto
    max-height 80vh

.wl-gallery-column
    display flex
    flex 1
    flex-direction column
    height -webkit-max-content
    height -moz-max-content
    height max-content

.wl-cards .wl-user
    --avatar-size var(--waline-avatar-size)
    position relative
    margin-inline-end 0.75em

@media (max-width 720px)
    .wl-cards .wl-user
        --avatar-size var(--waline-m-avatar-size)

.wl-cards .wl-user img
    width var(--avatar-size)
    height var(--avatar-size)
    border-radius var(--waline-avatar-radius)
    box-shadow var(--redefine-box-shadow-flat)

.wl-cards .wl-user .verified-icon
    position absolute
    top calc((var(--avatar-size) * 3 / 4))
    inset-inline-start calc((var(--avatar-size) * 3 / 4))
    border-radius 50%
    background var(--background-color)
    box-shadow var(--redefine-box-shadow-flat)

.wl-card-item
    position relative
    display flex
    padding 0.5em

.wl-card-item .wl-card-item
    padding-inline-end 0

.wl-card
    flex 1
    width 0
    padding-bottom 0.5em
    border-bottom 1px dashed var(--waline-border-color)

.wl-card:first-child
    margin-inline-start 1em

.wl-card-item:last-child .wl-card
    border-bottom none

.wl-card .wl-head
    overflow hidden
    line-height 1.5

.wl-card .wl-nick
    position relative
    display inline-block
    margin-inline-end 0.5em
    font-weight bold
    font-size 0.875em
    line-height 1
    text-decoration none

.wl-card .wl-nick svg
    position relative
    bottom -0.125em
    line-height 1

.wl-card span.wl-nick
    color var(--waline-dark-grey)

.wl-card .wl-badge
    display inline-block
    margin-inline-end 1em
    padding 0 0.3em
    border 1px solid var(--waline-badge-color)
    border-radius 4px
    color var(--waline-badge-color)
    font-size var(--waline-badge-font-size)

.wl-card .wl-time
    margin-inline-end 0.875em
    color var(--waline-info-color)
    font-size 0.75em

.wl-card .wl-meta
    display flex
    flex-direction row
    position relative
    line-height 1

.wl-card .wl-meta>span
    display flex
    align-items center
    flex-direction row
    margin-inline-end 0.25em
    padding 2px 4px
    border-radius 0.2em
    background var(--second-background-color)
    color var(--waline-info-color)
    font-size var(--waline-info-font-size)
    line-height 1.5

.wl-card .wl-meta>span:empty
    display none

.wl-card .wl-comment-actions
    float right
    line-height 1

[dir=rtl] .wl-card .wl-comment-actions
    float left

.wl-card .wl-delete, .wl-card .wl-like, .wl-card .wl-reply, .wl-card .wl-edit
    display inline-flex
    align-items center
    border none
    background rgba(0, 0, 0, 0)
    color var(--waline-color)
    line-height 1
    cursor pointer
    transition color 0.2s ease

.wl-card .wl-delete:hover, .wl-card .wl-like:hover, .wl-card .wl-reply:hover, .wl-card .wl-edit:hover
    color var(--waline-theme-color)

.wl-card .wl-delete.active, .wl-card .wl-like.active, .wl-card .wl-reply.active, .wl-card .wl-edit.active
    color var(--waline-active-color)

.wl-card .wl-content
    position relative
    margin-bottom 0.75em
    padding-top 0.625em
    font-size 0.875em
    line-height 2
    word-wrap break-word

.wl-card .wl-content.expand
    overflow hidden
    max-height 8em
    cursor pointer

.wl-card .wl-content.expand::before
    content ''
    position absolute
    top 0
    bottom 3.15em
    inset-inline-start 0
    z-index 999
    display block
    width 100%
    background linear-gradient(180deg, #000, rgba(255, 255, 255, 0.9))

.wl-card .wl-content.expand::after
    content attr(data-expand)
    position absolute
    bottom 0
    inset-inline-start 0
    z-index 999
    display block
    width 100%
    height 3.15em
    background rgba(255, 255, 255, 0.9)
    color #828586
    line-height 3.15em
    text-align center

.wl-card .wl-content>*:first-child
    margin-top 0

.wl-card .wl-content>*:last-child
    margin-bottom 0

.wl-card .wl-admin-actions
    margin 8px 0
    font-size 12px
    text-align right

.wl-card .wl-comment-status
    margin 0 8px

.wl-card .wl-comment-status .wl-btn
    border-radius 0

.wl-card .wl-comment-status .wl-btn:first-child
    border-inline-end 0
    border-radius 0.5em 0 0 0.5em

.wl-card .wl-comment-status .wl-btn:last-child
    border-inline-start 0
    border-radius 0 0.5em 0.5em 0

.wl-card .wl-quote
    border-inline-start 1px dashed rgba(237, 237, 237, 0.5)

.wl-card .wl-quote .wl-user
    --avatar-size var(--waline-m-avatar-size)

.wl-close-icon
    color var(--waline-border-color)

.wl-content .vemoji, .wl-content .wl-emoji
    display inline-block
    vertical-align baseline
    height 1.25em
    margin -0.125em 0.25em

.wl-content .wl-tex
    background var(--waline-info-bgcolor)
    color var(--waline-info-color)

.wl-content span.wl-tex
    display inline-block
    margin-inline-end 0.25em
    padding 2px 4px
    border-radius 0.2em
    font-size var(--waline-info-font-size)
    line-height 1.5

.wl-content p.wl-tex
    text-align center

.wl-content .katex-display
    overflow auto hidden
    -webkit-overflow-scrolling touch
    padding-top 0.2em
    padding-bottom 0.2em

.wl-content .katex-display::-webkit-scrollbar
    height 3px

.wl-content .katex-error
    color red

.wl-count
    flex 1
    font-weight bold
    font-size 1.25em

.wl-empty
    overflow auto
    padding 1.25em
    color var(--waline-color)
    text-align center

.wl-operation
    text-align center

.wl-operation button
    margin 1em 0

.wl-power
    padding 0.5em 0
    color var(--waline-light-grey)
    font-size var(--waline-info-font-size)
    text-align end

.wl-meta-head
    display flex
    flex-direction row
    align-items center
    padding 0.375em

.wl-sort
    margin 0
    list-style-type none

.wl-sort li
    display inline-block
    color var(--waline-info-color)
    font-size 0.75em
    cursor pointer

.wl-sort li.active
    color var(--waline-theme-color)

.wl-sort li+li
    margin-inline-start 1em

.wl-reaction
    overflow auto hidden
    margin-bottom 1.75em
    text-align center

.wl-reaction img
    width 100%
    height 100%
    transition all 250ms ease-in-out

.wl-reaction-title
    margin 16px auto
    font-weight bold
    font-size 18px

.wl-reaction-list
    display flex
    flex-direction row
    gap 16px
    justify-content center
    margin 0
    padding 8px
    list-style-type none

@media (max-width 580px)
    .wl-reaction-list
        gap 12px

[data-waline] .wl-reaction-list
    margin-inline-start 0

.wl-reaction-item
    display flex
    flex-direction column
    align-items center
    cursor pointer

.wl-reaction-item:hover img, .wl-reaction-item.active img
    transform scale(1.15)

.wl-reaction-img
    position relative
    width 42px
    height 42px

@media (max-width 580px)
    .wl-reaction-img
        width 32px
        height 32px

.wl-reaction-loading
    position absolute
    top -4px
    inset-inline-end -5px
    width 18px
    height 18px
    color var(--waline-theme-color)

.wl-reaction-votes
    position absolute
    top -9px
    inset-inline-end -9px
    min-width 1em
    padding 2px
    border 1px solid var(--waline-theme-color)
    border-radius 1em
    background var(--background-color)
    color var(--waline-theme-color)
    font-weight 700
    font-size 0.75em
    line-height 1

.wl-reaction-item.active .wl-reaction-votes
    background var(--waline-theme-color)
    color var(--background-color)

.wl-reaction-text
    font-size 0.875em

.wl-reaction-item.active .wl-reaction-text
    color var(--waline-theme-color)

.wl-content pre, .wl-content pre[class*=language-]
    overflow auto
    margin 0.75rem 0
    padding 1rem 1.25rem
    border-radius 6px
    background var(--waline-code-bgcolor)
    line-height 1.4

.wl-content pre code, .wl-content pre[class*=language-] code
    padding 0
    border-radius 0
    background rgba(0, 0, 0, 0) !important
    color #bbb
    direction ltr

.wl-content code[class*=language-], .wl-content pre[class*=language-]
    background none
    color #ccc
    font-size 1em
    font-family Consolas, Monaco, 'Andale Mono', 'Geist Mono', monospace
    text-align left
    white-space pre
    word-spacing normal
    word-wrap normal
    word-break normal
    tab-size 4
    hyphens none

.wl-content pre[class*=language-]
    overflow auto

.wl-content :not(pre)>code[class*=language-], .wl-content pre[class*=language-]
    background #2d2d2d

.wl-content :not(pre)>code[class*=language-]
    padding 0.1em
    border-radius 0.3em
    white-space normal

.wl-content .token.comment, .wl-content .token.block-comment, .wl-content .token.prolog, .wl-content .token.doctype, .wl-content .token.cdata
    color #999

.wl-content .token.punctuation
    color #ccc

.wl-content .token.tag, .wl-content .token.attr-name, .wl-content .token.namespace, .wl-content .token.deleted
    color #e2777a

.wl-content .token.function-name
    color #6196cc

.wl-content .token.boolean, .wl-content .token.number, .wl-content .token.function
    color #f08d49

.wl-content .token.property, .wl-content .token.class-name, .wl-content .token.constant, .wl-content .token.symbol
    color #f8c555

.wl-content .token.selector, .wl-content .token.important, .wl-content .token.atrule, .wl-content .token.keyword, .wl-content .token.builtin
    color #cc99cd

.wl-content .token.string, .wl-content .token.char, .wl-content .token.attr-value, .wl-content .token.regex, .wl-content .token.variable
    color #7ec699

.wl-content .token.operator, .wl-content .token.entity, .wl-content .token.url
    color #67cdcc

.wl-content .token.important, .wl-content .token.bold
    font-weight bold

.wl-content .token.italic
    font-style italic

.wl-content .token.entity
    cursor help

.wl-content .token.inserted
    color green

.wl-recent-item p
    display inline

.wl-user-list
    padding 0
    list-style none

.wl-user-list a, .wl-user-list a:hover, .wl-user-list a:visited
    color var(--waline-color)
    text-decoration none

.wl-user-list .wl-user-avatar
    position relative
    display inline-block
    overflow hidden
    margin-inline-end 10px
    border-radius 4px
    line-height 0

.wl-user-list .wl-user-avatar>img
    width var(--waline-user-avatar-size, 48px)
    height var(--waline-user-avatar-size, 48px)

.wl-user-list .wl-user-badge
    position absolute
    bottom 0
    inset-inline-end 0
    min-width 0.7em
    height 1.5em
    padding 0 0.4em
    border-radius 4px
    background var(--waline-info-bgcolor)
    color var(--waline-info-color)
    font-weight bold
    font-size 10px
    line-height 1.5em
    text-align center

.wl-user-list .wl-user-item
    margin 10px 0

.wl-user-list .wl-user-item:nth-child(1) .wl-user-badge
    background var(--waline-rank-gold-bgcolor, #fa3939)
    color var(--waline-white)
    font-weight bold

.wl-user-list .wl-user-item:nth-child(2) .wl-user-badge
    background var(--waline-rank-silver-bgcolor, #fb811c)
    color var(--waline-white)
    font-weight bold

.wl-user-list .wl-user-item:nth-child(3) .wl-user-badge
    background var(--waline-rank-copper-bgcolor, #feb207)
    color var(--waline-white)

.wl-user-list .wl-user-meta
    display inline-block
    vertical-align top

.wl-user-list .wl-badge
    display inline-block
    vertical-align text-top
    margin-inline-start 0.5em
    padding 0 0.3em
    border 1px solid var(--waline-badge-color)
    border-radius 4px
    color var(--waline-badge-color)
    font-size var(--waline-badge-font-size)

.wl-user-wall
    padding 0
    list-style none

.wl-user-wall .wl-user-badge, .wl-user-wall .wl-user-meta
    display none

.wl-user-wall .wl-user-item
    position relative
    display inline-block
    transition transform ease-in-out 0.2s

.wl-user-wall .wl-user-item::before, .wl-user-wall .wl-user-item::after
    position absolute
    bottom 100%
    left 50%
    z-index 10
    opacity 0
    pointer-events none
    transition all 0.18s ease-out 0.18s
    transform translate(-50%, 4px)
    transform-origin top

.wl-user-wall .wl-user-item::before
    content ''
    width 0
    height 0
    border 5px solid rgba(0, 0, 0, 0)
    border-top-color rgba(16, 16, 16, 0.95)

.wl-user-wall .wl-user-item::after
    content attr(aria-label)
    margin-bottom 10px
    padding 0.5em 1em
    border-radius 2px
    background rgba(16, 16, 16, 0.95)
    color #fff
    font-size 12px
    white-space nowrap

.wl-user-wall .wl-user-item:hover
    transform scale(1.1)

.wl-user-wall .wl-user-item:hover::before, .wl-user-wall .wl-user-item:hover::after
    opacity 1
    pointer-events none
    transform translate(-50%, 0)

.wl-user-wall .wl-user-item img
    width var(--waline-user-avatar-size, 48px)
    height var(--waline-user-avatar-size, 48px)


/* # sourceMappingURL=waline.css.map */
/* Waline meta */
.wl-meta > span::before
    content ''
    display inline-block
    width 1em
    height 1em
    margin-right 2px
    background-position center center
    background-repeat no-repeat

.wl-meta > .wl-addr::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='M444.52 3.52 28.74 195.42c-47.97 22.39-31.98 92.75 19.19 92.75h175.91v175.91c0 51.17 70.36 67.17 92.75 19.19l191.9-415.78c15.99-38.39-25.59-79.97-63.97-63.97z'/%3E%3C/svg%3E")

.wl-meta > .wl-os::before, .wl-meta > .wl-browser::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='M464 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h416c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm0 394c0 3.3-2.7 6-6 6H54c-3.3 0-6-2.7-6-6V192h416v234z'/%3E%3C/svg%3E")

.wl-meta > .wl-os[data-value^='windows'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' fill='%23999'%3E%3Cpath d='m0 93.7 183.6-25.3v177.4H0V93.7zm0 324.6 183.6 25.3V268.4H0v149.9zm203.8 28L448 480V268.4H203.8v177.9zm0-380.6v180.1H448V32L203.8 65.7z'/%3E%3C/svg%3E")

.wl-meta > .wl-os[data-value^='mac'i]::before, .wl-meta > .wl-os[data-value^='ios'i]::before, .wl-meta > .wl-os[data-value^='iphone'i]::before, .wl-meta > .wl-os[data-value^='ipad'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512' fill='%23999'%3E%3Cpath d='M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z'/%3E%3C/svg%3E")

.wl-meta > .wl-os[data-value^='linux'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' fill='%23999'%3E%3Cpath d='M220.8 123.3c1 .5 1.8 1.7 3 1.7 1.1 0 2.8-.4 2.9-1.5.2-1.4-1.9-2.3-3.2-2.9-1.7-.7-3.9-1-5.5-.1-.4.2-.8.7-.6 1.1.3 1.3 2.3 1.1 3.4 1.7zm-21.9 1.7c1.2 0 2-1.2 3-1.7 1.1-.6 3.1-.4 3.5-1.6.2-.4-.2-.9-.6-1.1-1.6-.9-3.8-.6-5.5.1-1.3.6-3.4 1.5-3.2 2.9.1 1 1.8 1.5 2.8 1.4zM420 403.8c-3.6-4-5.3-11.6-7.2-19.7-1.8-8.1-3.9-16.8-10.5-22.4-1.3-1.1-2.6-2.1-4-2.9-1.3-.8-2.7-1.5-4.1-2 9.2-27.3 5.6-54.5-3.7-79.1-11.4-30.1-31.3-56.4-46.5-74.4-17.1-21.5-33.7-41.9-33.4-72C311.1 85.4 315.7.1 234.8 0 132.4-.2 158 103.4 156.9 135.2c-1.7 23.4-6.4 41.8-22.5 64.7-18.9 22.5-45.5 58.8-58.1 96.7-6 17.9-8.8 36.1-6.2 53.3-6.5 5.8-11.4 14.7-16.6 20.2-4.2 4.3-10.3 5.9-17 8.3s-14 6-18.5 14.5c-2.1 3.9-2.8 8.1-2.8 12.4 0 3.9.6 7.9 1.2 11.8 1.2 8.1 2.5 15.7.8 20.8-5.2 14.4-5.9 24.4-2.2 31.7 3.8 7.3 11.4 10.5 20.1 12.3 17.3 3.6 40.8 2.7 59.3 12.5 19.8 10.4 39.9 14.1 55.9 10.4 11.6-2.6 21.1-9.6 25.9-20.2 12.5-.1 26.3-5.4 48.3-6.6 14.9-1.2 33.6 5.3 55.1 4.1.6 2.3 1.4 4.6 2.5 6.7v.1c8.3 16.7 23.8 24.3 40.3 23 16.6-1.3 34.1-11 48.3-27.9 13.6-16.4 36-23.2 50.9-32.2 7.4-4.5 13.4-10.1 13.9-18.3.4-8.2-4.4-17.3-15.5-29.7zM223.7 87.3c9.8-22.2 34.2-21.8 44-.4 6.5 14.2 3.6 30.9-4.3 40.4-1.6-.8-5.9-2.6-12.6-4.9 1.1-1.2 3.1-2.7 3.9-4.6 4.8-11.8-.2-27-9.1-27.3-7.3-.5-13.9 10.8-11.8 23-4.1-2-9.4-3.5-13-4.4-1-6.9-.3-14.6 2.9-21.8zM183 75.8c10.1 0 20.8 14.2 19.1 33.5-3.5 1-7.1 2.5-10.2 4.6 1.2-8.9-3.3-20.1-9.6-19.6-8.4.7-9.8 21.2-1.8 28.1 1 .8 1.9-.2-5.9 5.5-15.6-14.6-10.5-52.1 8.4-52.1zm-13.6 60.7c6.2-4.6 13.6-10 14.1-10.5 4.7-4.4 13.5-14.2 27.9-14.2 7.1 0 15.6 2.3 25.9 8.9 6.3 4.1 11.3 4.4 22.6 9.3 8.4 3.5 13.7 9.7 10.5 18.2-2.6 7.1-11 14.4-22.7 18.1-11.1 3.6-19.8 16-38.2 14.9-3.9-.2-7-1-9.6-2.1-8-3.5-12.2-10.4-20-15-8.6-4.8-13.2-10.4-14.7-15.3-1.4-4.9 0-9 4.2-12.3zm3.3 334c-2.7 35.1-43.9 34.4-75.3 18-29.9-15.8-68.6-6.5-76.5-21.9-2.4-4.7-2.4-12.7 2.6-26.4v-.2c2.4-7.6.6-16-.6-23.9-1.2-7.8-1.8-15 .9-20 3.5-6.7 8.5-9.1 14.8-11.3 10.3-3.7 11.8-3.4 19.6-9.9 5.5-5.7 9.5-12.9 14.3-18 5.1-5.5 10-8.1 17.7-6.9 8.1 1.2 15.1 6.8 21.9 16l19.6 35.6c9.5 19.9 43.1 48.4 41 68.9zm-1.4-25.9c-4.1-6.6-9.6-13.6-14.4-19.6 7.1 0 14.2-2.2 16.7-8.9 2.3-6.2 0-14.9-7.4-24.9-13.5-18.2-38.3-32.5-38.3-32.5-13.5-8.4-21.1-18.7-24.6-29.9s-3-23.3-.3-35.2c5.2-22.9 18.6-45.2 27.2-59.2 2.3-1.7.8 3.2-8.7 20.8-8.5 16.1-24.4 53.3-2.6 82.4.6-20.7 5.5-41.8 13.8-61.5 12-27.4 37.3-74.9 39.3-112.7 1.1.8 4.6 3.2 6.2 4.1 4.6 2.7 8.1 6.7 12.6 10.3 12.4 10 28.5 9.2 42.4 1.2 6.2-3.5 11.2-7.5 15.9-9 9.9-3.1 17.8-8.6 22.3-15 7.7 30.4 25.7 74.3 37.2 95.7 6.1 11.4 18.3 35.5 23.6 64.6 3.3-.1 7 .4 10.9 1.4 13.8-35.7-11.7-74.2-23.3-84.9-4.7-4.6-4.9-6.6-2.6-6.5 12.6 11.2 29.2 33.7 35.2 59 2.8 11.6 3.3 23.7.4 35.7 16.4 6.8 35.9 17.9 30.7 34.8-2.2-.1-3.2 0-4.2 0 3.2-10.1-3.9-17.6-22.8-26.1-19.6-8.6-36-8.6-38.3 12.5-12.1 4.2-18.3 14.7-21.4 27.3-2.8 11.2-3.6 24.7-4.4 39.9-.5 7.7-3.6 18-6.8 29-32.1 22.9-76.7 32.9-114.3 7.2zm257.4-11.5c-.9 16.8-41.2 19.9-63.2 46.5-13.2 15.7-29.4 24.4-43.6 25.5s-26.5-4.8-33.7-19.3c-4.7-11.1-2.4-23.1 1.1-36.3 3.7-14.2 9.2-28.8 9.9-40.6.8-15.2 1.7-28.5 4.2-38.7 2.6-10.3 6.6-17.2 13.7-21.1.3-.2.7-.3 1-.5.8 13.2 7.3 26.6 18.8 29.5 12.6 3.3 30.7-7.5 38.4-16.3 9-.3 15.7-.9 22.6 5.1 9.9 8.5 7.1 30.3 17.1 41.6 10.6 11.6 14 19.5 13.7 24.6zM173.3 148.7c2 1.9 4.7 4.5 8 7.1 6.6 5.2 15.8 10.6 27.3 10.6 11.6 0 22.5-5.9 31.8-10.8 4.9-2.6 10.9-7 14.8-10.4s5.9-6.3 3.1-6.6-2.6 2.6-6 5.1c-4.4 3.2-9.7 7.4-13.9 9.8-7.4 4.2-19.5 10.2-29.9 10.2s-18.7-4.8-24.9-9.7c-3.1-2.5-5.7-5-7.7-6.9-1.5-1.4-1.9-4.6-4.3-4.9-1.4-.1-1.8 3.7 1.7 6.5z'/%3E%3C/svg%3E")

.wl-meta > .wl-os[data-value^='ubuntu'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 496 512' fill='%23999'%3E%3Cpath d='M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm52.7 93c8.8-15.2 28.3-20.5 43.5-11.7 15.3 8.8 20.5 28.3 11.7 43.6-8.8 15.2-28.3 20.5-43.5 11.7-15.3-8.9-20.5-28.4-11.7-43.6zM87.4 287.9c-17.6 0-31.9-14.3-31.9-31.9 0-17.6 14.3-31.9 31.9-31.9 17.6 0 31.9 14.3 31.9 31.9 0 17.6-14.3 31.9-31.9 31.9zm28.1 3.1c22.3-17.9 22.4-51.9 0-69.9 8.6-32.8 29.1-60.7 56.5-79.1l23.7 39.6c-51.5 36.3-51.5 112.5 0 148.8L172 370c-27.4-18.3-47.8-46.3-56.5-79zm228.7 131.7c-15.3 8.8-34.7 3.6-43.5-11.7-8.8-15.3-3.6-34.8 11.7-43.6 15.2-8.8 34.7-3.6 43.5 11.7 8.8 15.3 3.6 34.8-11.7 43.6zm.3-69.5c-26.7-10.3-56.1 6.6-60.5 35-5.2 1.4-48.9 14.3-96.7-9.4l22.5-40.3c57 26.5 123.4-11.7 128.9-74.4l46.1.7c-2.3 34.5-17.3 65.5-40.3 88.4zm-5.9-105.3c-5.4-62-71.3-101.2-128.9-74.4l-22.5-40.3c47.9-23.7 91.5-10.8 96.7-9.4 4.4 28.3 33.8 45.3 60.5 35 23.1 22.9 38 53.9 40.2 88.5l-46 .6z'/%3E%3C/svg%3E")

.wl-meta > .wl-os[data-value^='android'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 576 512' fill='%23999'%3E%3Cpath d='M420.55 301.93a24 24 0 1 1 24-24 24 24 0 0 1-24 24m-265.1 0a24 24 0 1 1 24-24 24 24 0 0 1-24 24m273.7-144.48 47.94-83a10 10 0 1 0-17.27-10l-48.54 84.07a301.25 301.25 0 0 0-246.56 0l-48.54-84.07a10 10 0 1 0-17.27 10l47.94 83C64.53 202.22 8.24 285.55 0 384h576c-8.24-98.45-64.54-181.78-146.85-226.55'/%3E%3C/svg%3E")

.wl-meta > .wl-browser[data-value^='chrome'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 496 512' fill='%23999'%3E%3Cpath d='M131.5 217.5 55.1 100.1c47.6-59.2 119-91.8 192-92.1 42.3-.3 85.5 10.5 124.8 33.2 43.4 25.2 76.4 61.4 97.4 103L264 133.4c-58.1-3.4-113.4 29.3-132.5 84.1zm32.9 38.5c0 46.2 37.4 83.6 83.6 83.6s83.6-37.4 83.6-83.6-37.4-83.6-83.6-83.6-83.6 37.3-83.6 83.6zm314.9-89.2L339.6 174c37.9 44.3 38.5 108.2 6.6 157.2L234.1 503.6c46.5 2.5 94.4-7.7 137.8-32.9 107.4-62 150.9-192 107.4-303.9zM133.7 303.6 40.4 120.1C14.9 159.1 0 205.9 0 256c0 124 90.8 226.7 209.5 244.9l63.7-124.8c-57.6 10.8-113.2-20.8-139.5-72.5z'/%3E%3C/svg%3E")

.wl-meta > .wl-browser[data-value^='edge'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='M481.92 134.48C440.87 54.18 352.26 8 255.91 8 137.05 8 37.51 91.68 13.47 203.66c26-46.49 86.22-79.14 149.46-79.14 79.27 0 121.09 48.93 122.25 50.18 22 23.8 33 50.39 33 83.1 0 10.4-5.31 25.82-15.11 38.57-1.57 2-6.39 4.84-6.39 11 0 5.06 3.29 9.92 9.14 14 27.86 19.37 80.37 16.81 80.51 16.81A115.39 115.39 0 0 0 444.94 322a118.92 118.92 0 0 0 58.95-102.44c.5-43.43-15.5-72.3-21.97-85.08ZM212.77 475.67a154.88 154.88 0 0 1-46.64-45c-32.94-47.42-34.24-95.6-20.1-136A155.5 155.5 0 0 1 203 215.75c59-45.2 94.84-5.65 99.06-1a80 80 0 0 0-4.89-10.14c-9.24-15.93-24-36.41-56.56-53.51-33.72-17.69-70.59-18.59-77.64-18.59-38.71 0-77.9 13-107.53 35.69C35.68 183.3 12.77 208.72 8.6 243c-1.08 12.31-2.75 62.8 23 118.27a248 248 0 0 0 248.3 141.61c-38.12-6.62-65.85-26.64-67.13-27.21Zm250.72-98.33a7.76 7.76 0 0 0-7.92-.23 181.66 181.66 0 0 1-20.41 9.12 197.54 197.54 0 0 1-69.55 12.52c-91.67 0-171.52-63.06-171.52-144a61.12 61.12 0 0 1 6.52-26.75 168.72 168.72 0 0 0-38.76 50c-14.92 29.37-33 88.13 13.33 151.66 6.51 8.91 23 30 56 47.67 23.57 12.65 49 19.61 71.7 19.61 35.14 0 115.43-33.44 163-108.87a7.75 7.75 0 0 0-2.39-10.73Z'/%3E%3C/svg%3E")

.wl-meta > .wl-browser[data-value^='firefox'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='M189.37 152.86Zm-58.74-29.37c.16.01.08.01 0 0Zm351.42 45.35c-10.61-25.5-32.08-53-48.94-61.73 13.72 26.89 21.67 53.88 24.7 74 0 0 0 .14.05.41-27.58-68.75-74.35-96.47-112.55-156.83-1.93-3.05-3.86-6.11-5.74-9.33-1-1.65-1.86-3.34-2.69-5.05a44.88 44.88 0 0 1-3.64-9.62.63.63 0 0 0-.55-.66.9.9 0 0 0-.46 0l-.12.07-.18.1.1-.14c-54.23 31.77-76.72 87.38-82.5 122.78a130 130 0 0 0-48.33 12.33 6.25 6.25 0 0 0-3.09 7.75 6.13 6.13 0 0 0 7.79 3.79l.52-.21a117.84 117.84 0 0 1 42.11-11l1.42-.1c2-.12 4-.2 6-.22A122.61 122.61 0 0 1 291 140c.67.2 1.32.42 2 .63 1.89.57 3.76 1.2 5.62 1.87 1.36.5 2.71 1 4.05 1.58 1.09.44 2.18.88 3.25 1.35q2.52 1.13 5 2.35c.75.37 1.5.74 2.25 1.13q2.4 1.26 4.74 2.63 1.51.87 3 1.8a124.89 124.89 0 0 1 42.66 44.13c-13-9.15-36.35-18.19-58.82-14.28 87.74 43.86 64.18 194.9-57.39 189.2a108.43 108.43 0 0 1-31.74-6.12 139.5 139.5 0 0 1-7.16-2.93c-1.38-.63-2.76-1.27-4.12-2-29.84-15.34-54.44-44.42-57.51-79.75 0 0 11.25-41.95 80.62-41.95 7.5 0 28.93-20.92 29.33-27-.09-2-42.54-18.87-59.09-35.18-8.85-8.71-13.05-12.91-16.77-16.06a69.58 69.58 0 0 0-6.31-4.77 113.05 113.05 0 0 1-.69-59.63c-25.06 11.41-44.55 29.45-58.71 45.37h-.12c-9.67-12.25-9-52.65-8.43-61.08-.12-.53-7.22 3.68-8.15 4.31a178.54 178.54 0 0 0-23.84 20.43 214 214 0 0 0-22.77 27.33 205.84 205.84 0 0 0-32.73 73.9c-.06.27-2.33 10.21-4 22.48q-.42 2.87-.78 5.74c-.57 3.69-1 7.71-1.44 14 0 .24 0 .48-.05.72-.18 2.71-.34 5.41-.49 8.12v1.24c0 134.7 109.21 243.89 243.92 243.89 120.64 0 220.82-87.58 240.43-202.62.41-3.12.74-6.26 1.11-9.41 4.85-41.83-.54-85.79-15.82-122.55Z'/%3E%3C/svg%3E")

.wl-meta > .wl-browser[data-value^='safari'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='m274.69 274.69-37.38-37.38L166 346ZM256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8Zm155.85 174.79 14.78-6.13a8 8 0 0 1 10.45 4.34 8 8 0 0 1-4.33 10.46L418 197.57a8 8 0 0 1-10.45-4.33 8 8 0 0 1 4.3-10.45ZM314.43 94l6.12-14.78a8 8 0 0 1 10.45-4.3 8 8 0 0 1 4.33 10.45l-6.13 14.78a8 8 0 0 1-10.45 4.33A8 8 0 0 1 314.43 94ZM256 60a8 8 0 0 1 8 8v16a8 8 0 0 1-8 8 8 8 0 0 1-8-8V68a8 8 0 0 1 8-8Zm-75 14.92a8 8 0 0 1 10.46 4.33L197.57 94a8 8 0 1 1-14.78 6.12l-6.13-14.78A8 8 0 0 1 181 74.92Zm-63.58 42.49a8 8 0 0 1 11.31 0L140 128.72a8 8 0 0 1 0 11.28 8 8 0 0 1-11.31 0l-11.31-11.31a8 8 0 0 1 .03-11.28ZM60 256a8 8 0 0 1 8-8h16a8 8 0 0 1 8 8 8 8 0 0 1-8 8H68a8 8 0 0 1-8-8Zm40.15 73.21-14.78 6.13A8 8 0 0 1 74.92 331a8 8 0 0 1 4.33-10.46L94 314.43a8 8 0 0 1 10.45 4.33 8 8 0 0 1-4.3 10.45Zm4.33-136A8 8 0 0 1 94 197.57l-14.78-6.12a8 8 0 0 1-4.3-10.45 8 8 0 0 1 10.45-4.33l14.78 6.13a8 8 0 0 1 4.33 10.44ZM197.57 418l-6.12 14.78a8 8 0 0 1-14.79-6.12l6.13-14.78a8 8 0 1 1 14.78 6.12ZM264 444a8 8 0 0 1-8 8 8 8 0 0 1-8-8v-16a8 8 0 0 1 8-8 8 8 0 0 1 8 8Zm67-6.92a8 8 0 0 1-10.46-4.33L314.43 418a8 8 0 0 1 4.33-10.45 8 8 0 0 1 10.45 4.33l6.13 14.78a8 8 0 0 1-4.34 10.42Zm63.58-42.49a8 8 0 0 1-11.31 0L372 383.28a8 8 0 0 1 0-11.28 8 8 0 0 1 11.31 0l11.31 11.31a8 8 0 0 1-.03 11.28ZM286.25 286.25 110.34 401.66l115.41-175.91 175.91-115.41ZM437.08 331a8 8 0 0 1-10.45 4.33l-14.78-6.13a8 8 0 0 1-4.33-10.45 8 8 0 0 1 10.48-4.32l14.78 6.12a8 8 0 0 1 4.3 10.45Zm6.92-67h-16a8 8 0 0 1-8-8 8 8 0 0 1 8-8h16a8 8 0 0 1 8 8 8 8 0 0 1-8 8Z'/%3E%3C/svg%3E")

.wl-meta > .wl-browser[data-value^='ie'i]::before, .wl-meta > .wl-browser[data-value^='explorer'i]::before
    background-image url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512' fill='%23999'%3E%3Cpath d='M483.049 159.706c10.855-24.575 21.424-60.438 21.424-87.871 0-72.722-79.641-98.371-209.673-38.577-107.632-7.181-211.221 73.67-237.098 186.457 30.852-34.862 78.271-82.298 121.977-101.158C125.404 166.85 79.128 228.002 43.992 291.725 23.246 329.651 0 390.94 0 436.747c0 98.575 92.854 86.5 180.251 42.006 31.423 15.43 66.559 15.573 101.695 15.573 97.124 0 184.249-54.294 216.814-146.022H377.927c-52.509 88.593-196.819 52.996-196.819-47.436H509.9c6.407-43.581-1.655-95.715-26.851-141.162zM64.559 346.877c17.711 51.15 53.703 95.871 100.266 123.304-88.741 48.94-173.267 29.096-100.266-123.304zm115.977-108.873c2-55.151 50.276-94.871 103.98-94.871 53.418 0 101.981 39.72 103.981 94.871H180.536zm184.536-187.6c21.425-10.287 48.563-22.003 72.558-22.003 31.422 0 54.274 21.717 54.274 53.722 0 20.003-7.427 49.007-14.569 67.867-26.28-42.292-65.986-81.584-112.263-99.586z'/%3E%3C/svg%3E")

/* # sourceMappingURL=waline-meta.css.map */
