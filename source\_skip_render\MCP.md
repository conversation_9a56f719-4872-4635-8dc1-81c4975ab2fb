---
title: MCP - image test
date: 2025-05-08 20:55:38
tags:
---



### introduction

由claude的母公司Anthropic于2024年11月25日推出，全称**模型上下文协议(Model Context Protocol)**
- 这是一个开放标准, 在该网站可以看到官方文档 https://modelcontextprotocol.io/
  - 中文文档：https://mcp-docs.cn/introduction
- 旨在将**人工智能应用**与**不同工具**和数据源进行连接
  - 提供一种标准化的路径来沟通 模型 与 工具
  - 属于*中间协议层*， 就像USB-C转接器一样，统一外部server接入host， 如下图所示， 通过MCP client作为转接器，不同的Server最终以统一的接口形式暴露给host，让模型得知。

  ![MCP示意图](./MCP/MCP示意图.png)
- > MCP is an open protocol that standardizes how applications provide context to LLMs. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.



本质上是在模型和外部工具间添加一个 **中介程序**， 对接口进行**封装**

- 原先的外部工具套上一层壳(规范接口)就成了一个个*server*
- 中介程序中由*client* 和 *server* 打交道
  - 通信基于MCP协议
- 而容纳*client*的中介程序，就是*MCP Host* —— 例如vscode的cline插件

具体架构见图 [架构](#一般架构)





### 现实意义

能够帮助我们在抽象的顶层设计设计agent或Workflow —— 相当于将模型与工具封装隔离，设计者不需要过多考虑交互的细节与融合。
而现在的LLMs经常需要同外部数据或者工具进行融合，以此来增强自己的能力



举个例子，假如没有MCP，那么不同如 Claude Desktop、IDE 或 AI 工具等AI应用，想要增加外部工具的调用，则需要彼此按照自己的村规对每个不同的工具做适配(比如function calling)

而引入MCP协议作为中间层之后，一切变得井然有序, 如右图(目前的MCPserver数已经上万，如果没有中间层，这是不可想象的)

在此之后，甚至可能可以基于MCP协议对模型进行专门的训练，使之对工具使用的接口更加熟悉，调用更加稳定。(因为本质上要求模型按规定格式输出)

![MCP现实意义](./MCP/MCP现实意义.png)



### 一般架构

- **MCP Hosts**: 如 Claude Desktop、IDE 或 AI 工具，希望通过 MCP 访问数据的程序
- **MCP Clients**: 维护与服务器一对一连接的协议客户端
- **MCP Servers**: 轻量级程序，通过标准的 Model Context Protocol 提供特定能力
- **本地数据源**: MCP 服务器可安全访问的计算机文件、数据库和服务
- **远程服务**: MCP 服务器可连接的互联网上的外部系统（如通过 APIs）



- **MCP Hosts**: Programs like Claude Desktop, IDEs, or AI tools that want to access data through MCP
- **MCP Clients**: Protocol clients that maintain 1:1 connections with servers
- **MCP Servers**: Lightweight programs that each expose specific capabilities through the standardized Model Context Protocol
- **Local Data Sources**: Your computer’s files, databases, and services that MCP servers can securely access
- **Remote Services**: External systems available over the internet (e.g., through APIs) that MCP servers can connect to



![MCP架构](./MCP/MCP架构.png)

其中MCPserver虽然名字里带个server
但它实际上完全可以是本地的一个程序，只是在逻辑关系上就像提供服务的server一样——因为本质上是外部工具的封装

外部工具封装成MCP Server， 根据MCP协议，在传输层与MCP Client进行交互， client再将信息整合与Host交互

![架构2](./MCP/架构2.png)



#### 传输层实现细节

文档：https://modelcontextprotocol.io/docs/concepts/transports#standard-input%2Foutput-stdio

有两种传输方式

1. stdio(适用于本地进程，两者都在同一台计算机上)
   - 在这种模式下，server进程被client进程创建出来作为子进程，然后通过标准输入输出进行交互
     - client把消息写到server的stdin， server把response写到它的stdout	
2. 基于HTTP的SSE传输(SSE: Server-Sent Events) 
   - 使用 Server-Sent Events技术实现client到server的通信
     - client使用HTTP GET和HTTP POST与server进行交互
   - 通信的协议内容基于JSON-RPC2.0

- 其实也允许自定义传输方式，但那样就偏离了统一接口的目的，因为又引入了未定义的行为

### 核心概念



- **Resources**

  - 它允许server暴露可以被 clients 读取并用作 LLM 交互上下文的数据和内容

    - server通过`resources/list` endpoint暴露可用资源，resource结构
      ```json
      {
        uri: string;           // Unique identifier for the resource
        name: string;          // Human-readable name
        description?: string;  // Optional description
        mimeType?: string;     // Optional MIME type
      }
      ```

      或者
      ```json
      {
        uriTemplate: string;   // URI template following RFC 6570
        name: string;          // Human-readable name for this type
        description?: string;  // Optional description
        mimeType?: string;     // Optional MIME type for all matching resources
      }
      ```

    - client可以通过`resources/read` 请求读取resources

- **Prompts**

  - 允许 servers 定义可复用的提示模板和工作流，可以被Client获取

    - prompt结构
      ```json
      {
        name: string;              // Unique identifier for the prompt
        description?: string;      // Human-readable description
        arguments?: [              // Optional list of arguments
          {
            name: string;          // Argument identifier
            description?: string;  // Argument description
            required?: boolean;    // Whether argument is required
          }
        ]
      }
      ```

    - client可以通过`prompts/list`发现可用的prompts模版，然后嵌入必要的参数，通过`prompts/get` 进行使用

- **Tools**

  - 使 servers 能够向 clients 暴露可执行功能

    - Tool结构
      ```json
      {
        name: string;          // Unique identifier for the tool
        description?: string;  // Human-readable description
        inputSchema: {         // JSON Schema for the tool's parameters
          type: "object",
          properties: { ... }  // Tool-specific parameters
        },
        annotations?: {        // Optional hints about tool behavior
          title?: string;      // Human-readable title for the tool
          readOnlyHint?: boolean;    // If true, the tool does not modify its environment
          destructiveHint?: boolean; // If true, the tool may perform destructive updates
          idempotentHint?: boolean;  // If true, repeated calls with same args have no additional effect
          openWorldHint?: boolean;   // If true, tool interacts with external entities
        }
      }
      ```

    - Clients 可以通过 `tools/list` endpoint 列出可用的 tools

    - Client 可以使用 `tools/call` endpoint 调用tool，servers 执行请求的操作并返回结果

- **Sampling**

  - 它允许 servers 通过 client 请求 LLM 补全，从而实现复杂的 agentic 行为，同时保持安全性和隐私性

    - 这里产生了对LLM的请求，需要借用Host完成，而后文会提到这并不在MCP明确规定的范围内，所以未必所有的Host都支持该功能

    - 如*Claude Desktop*

      > This feature of MCP is not yet supported in the Claude Desktop client.

- **Roots**

  - 它定义了 servers 可以操作的边界
    - 告知 servers 相关 resources 及其位置信息
    - 明确哪些 resources 是你的 workspace 的一部分



注意上述的这些核心概念，一般只有Tool部分是都会支持的，其它的具体要看client和host的实现

![支持矩阵](./MCP/支持矩阵.png)





### 工作过程



##### Client 和 Server之间的通信



![连接工作流程](./MCP/连接工作流程.png)

1.  Clinet向Server发生初始化请求
2.  Server会告诉Client自己拥有哪些工具以及协议版本(> protocol version and capabilities)
3. Client向Server发送信息，确认初始化成功
4. 开始正常信息交流
   - Request-Response: Client或Server发生请求，另一方响应
   - Notifications: 一方单方面发送消息，不要求响应

当出现以下三种情况时终止

- 使用`close()`干净关闭
- 传输断开
- 出现错误



##### Client 和 模型之间的通信

- MCP协议主要规定的是**client和server之间的通信**，**Client和模型之间的通信会受到host具体实现的影响**，但这一部分仍然有助于我们理解一个MCP应用的工作流程
  - **Anthropic的官网上似乎并没有规定Client和AI模型之间的通信 —— 只是规定了client和server之间的通信**

以cline为例，有人通过数据抓包发现，在实际工作的时候，Cline插件并没有使用*function calling*技术，而是——直接把所有外部工具的详细信息直接写在了提示词中(json格式，系统提示词，"role":"system")，甚至还有Cline内置的工具，甚至还有详细的使用案例教学，长达上万字符。其中用户的信息也同样在这段提示词中，通过另一项json来进行指定(用户提示词，"role": "user")。

所以发生的事情是这样的：

1. Host(如Cline)， 将用户的require附加上关于MCP工具的所有使用说明提供给模型
2. 模型按照提示词指定的格式调用工具，如使用<use_mcp_tool>标签进行标记
   - 如果模型请求了工具调用，那么工具执行结果很可能会和原先的系统提示词、用户提示词一起发给模型，让它进行**重新生成**，直到没有工具调用请求。

> 引自*BV1sWoMYUEi9*， https://zhuanlan.zhihu.com/p/29001189476






---



### 工程实践



以下是一些MCP server汇总网站

- https://smithery.ai/
- https://mcp.so/
- https://www.mcp-home.com/
- https://mcpmarket.com/
- https://mcpmarket.cn/
- https://mcpservers.org/





#### 使用示例

找到一个心仪的MCP server(或者自己做一个)

- 这里使用的MCP Server：https://mcp.so/server/fetch/modelcontextprotocol

一般来说，他会是类似的界面，有一些说明，但最重要的是右侧红框标记的**Server Config**， 将其复制到MCP Host的MCP Server配置文件即可

![fetch](./MCP/fetch.png)



以cline作为客户端为例：

找到一个配置选项，点击就会打开一个json文件

![cline添加MCPServer](./MCP/Cline添加MCPServer.png)

在这个json文件复制进入从server提供者那里得到的server config即可——如果有多个的话，用逗号隔开。

```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    }
  }
}
```
以类似上述json格式去定义MCPserver
其中fetch为一个MCPserver的名字，运行的程序为uvx，参数为mcp-server-fetch

- 所以可知，需要首先下载uvx程序
  - MCPserver大多使用python或者node进行编写，对应启动程序一般是(在MCP server供应处复制的时候看看command是什么就知道了)
    - python - uvx(uv tool run的别名)
    - node - npx
  - 关于uv：
    - > An extremely fast Python package and project manager, written in Rust.
      - python包管理器
    - 文档：https://docs.astral.sh/uv/
    - mac+linux安装： `curl -LsSf https://astral.sh/uv/install.sh | sh`
    - windows安装： `powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"`
  - 关于npx, 其为node的一部分，所以已经安装过Node.js的不需要做额外安装
  - uvx/npx之外的command同理(原理是通的，都是运行命令做一些事情)

在客户端配置MCPserver的时候很多时候会运行命令、进行注册，所以要保证安装成功才能正常注册。
- 注：在客户端配置完MCPserver可能会出现以下情况
  - 尝试运行命令->发现对应工具没有安装->开始下载安装->安装太久，超时->配置失败
    - 则解决方法为，现在自己的终端运行一遍，保证下载完成，例如`uvx mcp-server-fetch`
      - 下载完成可能需要手动退出，因为对应程序可能会被运行，然后开始等待输入

在网站找到提供的MCPserver时，提供方提供的json可能是上面那个示例的格式
但在实际配置的时候，可能会出现多两个参数
disabled和timeout
分别表示是否启用该工具以及超时时间

还有一个*transportType*，这也是非常重要的，有两种选择，就是先前于[传输层实现细节](#传输层实现细节)提到的，但是网上具体的MCP Server提供的Server Config模版很多时候不自带这个选项，可以自行指定

- stdio
- sse

```json
{
  "mcpServers": {
    "fetch": {
      "disabled": false,
      "timeout": 60,
      "command": "uvx",
      "args": [
        "mcp-server-fetch"
      ],
      "transportType": "stdio"
    }
  }
}
```

然后就可以使用了





---



同理，在Claude Desktop中

找到对应的位置，打开config.json （claude_desktio_config.json）

![claude-desktop配置MCP](./MCP/claude-desktop配置MCP.png)

复制写入必要内容，然后重启Claude Desktop即可使用。

```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": [
        "mcp-server-fetch"
      ],
      "transportType": "stdio"
    }
  }
}
```

