if (hexo-config('page_templates.tags_style') == 'cloud')
  +redefine-mobile()
    padding 1.2rem 0.2rem
    box-shadow none

    &:hover
      box-shadow none

  .tagcloud-header
    margin 6px 0 12px 0
    font-size 3.2rem
    padding 0
    line-height 1
    font-weight bold
    color var(--second-text-color)

  .tagcloud-content
    text-align justify

    a
      display inline-block
      box-sizing border-box
      padding 0.7rem 0.5rem
      line-height 0.7
else
  +redefine-mobile()
    padding 1.2rem 0.2rem
    box-shadow none

    &:hover
      box-shadow none

  .tagcloud-header
    margin 6px 0 12px 0
    font-size 3.2rem
    padding 0
    line-height 1
    font-weight bold
    color var(--second-text-color)

  .tagcloud-content
    text-align justify

    &:hover
      .tag-list
        a
          filter blur(0px) !important
          transition filter 0.3s ease

    // 定义数量是否显示
    .tag-list[data-show-value] a::after
      content attr(data-weight)
      font-size 1.5rem
      background-color var(--third-background-color)
      padding 4px 8px
      min-width 35px
      margin-left 10px
      border-radius $redefine-border-radius-small
      color var(--third-text-color)
      transition background-color 0.2s ease

    .tags-punctuation, .tag-list
      list-style none
      padding-left 0
      display flex
      flex-wrap wrap
      align-items center
      justify-content center
      line-height 2.5rem

      li
        /*
        &:nth-child(1n+1) a { --color: #0cabeb; --opacity: 1  }
        &:nth-child(2n+1) a { --color: #181; --opacity: .9 }
        &:nth-child(3n+1) a { --color: #33a; --opacity: .7 }
        &:nth-child(4n+1) a { --color: #c38; --opacity: .4 }
        &:nth-child(5n+1) a { --color: rgb(184, 174, 62); --opacity: .8 }
          */
        a
          // 根据自定义data设置样式
          &[data-weight]
            --blur 0

          &[data-weight='1']
            --blur 3px

          &[data-weight='2']
            --blur 1.8px

          &[data-weight='3']
            --blur 0.8px

          &[data-weight='4']
            --blur 0.4px

          &[data-weight='5']
            --blur 0

          font-size 1.4rem
          opacity var(--opacity)
          padding 0.9rem 1.4rem
          display block
          position relative
          flex 1
          border-radius $redefine-border-radius-large
          box-shadow var(--redefine-box-shadow)
          margin 10px
          color var(--default-text-color)
          filter blur(var(--blur))
          transform scaleX(1) scaleY(1)
          background-color var(--default-background-color)
          transition transform 0.2s ease, filter 0.3s ease, color 0.2s ease, background-color 0.2s ease

          .fa-hashtag
            margin-right 5px
            opacity 0.3

          &:hover
            transform scaleX(1.05) scaleY(1.05)
            box-shadow var(--redefine-box-shadow-hover)
            background-color var(--primary-color)
            color var(--invert-text-color)
            transition transform 0.3s ease, background-color 0.3s ease, color 0.3s ease

            .fa-hashtag
              margin-right 5px
              color var(--invert-text-color)
              opacity 0.4

          &:active
            transform translateY(2px)
            transition transform 0.1s

    @media (prefers-reduced-motion )
      .tag-list *
        transition none !important
