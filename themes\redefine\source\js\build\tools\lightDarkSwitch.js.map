{"version": 3, "file": "lightDarkSwitch.js", "names": ["main", "elementCode", "ModeToggle", "modeToggleButton_dom", "iconDom", "mermaidLightTheme", "mermaidDarkTheme", "mermaidInit", "theme", "window", "mermaid", "Promise", "resolve", "reject", "els", "document", "querySelectorAll", "count", "length", "for<PERSON>ach", "element", "getAttribute", "removeAttribute", "innerHTML", "error", "initialize", "init", "enableLightMode", "body", "classList", "remove", "documentElement", "add", "this", "className", "styleStatus", "isDark", "setStyleStatus", "setGiscusTheme", "enableDarkMode", "querySelector", "giscusFrame", "r", "setTimeout", "contains", "contentWindow", "postMessage", "giscus", "setConfig", "isDarkPrefersColorScheme", "matchMedia", "initModeStatus", "getStyleStatus", "matches", "initModeToggleButton", "addEventListener", "initModeAutoTrigger", "e", "style", "light", "dark", "setAttribute", "catch", "console", "initModeToggle"], "sources": ["0"], "mappings": "eAASA,MAAY,aAErB,MAAMC,EAAc,kBAwCb,MAAMC,WAAa,CACxBC,qBAAsB,KACtBC,QAAS,KACTC,kBAAmB,KACnBC,iBAAkB,KAElB,iBAAMC,CAAYC,GACZC,OAAOC,gBA1BN,IAAIC,SAAQ,CAACC,EAASC,KAC3B,IACE,IAAIC,EAAMC,SAASC,iBAAiBf,GAClCgB,EAAQH,EAAII,OACdJ,EAAIK,SAASC,IACuC,MAA9CA,EAAQC,aAAa,wBACvBD,EAAQE,gBAAgB,kBACxBF,EAAQG,UAAYH,EAAQC,aAAa,uBAG9B,KADbJ,GAEEL,GACF,GAEJ,CAAE,MAAOY,GACPX,EAAOW,EACT,KAYEd,QAAQe,WAAW,CAAEjB,UACrBE,QAAQgB,KAAK,CAAElB,SAASO,SAASC,iBAAiBf,IAEtD,EAEA,eAAA0B,GACEZ,SAASa,KAAKC,UAAUC,OAAO,aAC/Bf,SAASgB,gBAAgBF,UAAUC,OAAO,QAC1Cf,SAASa,KAAKC,UAAUG,IAAI,cAC5BjB,SAASgB,gBAAgBF,UAAUG,IAAI,SACvCC,KAAK7B,QAAQ8B,UAAY,qBACzBlC,EAAKmC,YAAYC,QAAS,EAC1BpC,EAAKqC,iBACLJ,KAAK1B,YAAY0B,KAAK5B,mBACtB4B,KAAKK,gBACP,EAEA,cAAAC,GACExB,SAASa,KAAKC,UAAUC,OAAO,cAC/Bf,SAASgB,gBAAgBF,UAAUC,OAAO,SAC1Cf,SAASa,KAAKC,UAAUG,IAAI,aAC5BjB,SAASgB,gBAAgBF,UAAUG,IAAI,QACvCC,KAAK7B,QAAQ8B,UAAY,2BACzBlC,EAAKmC,YAAYC,QAAS,EAC1BpC,EAAKqC,iBACLJ,KAAK1B,YAAY0B,KAAK3B,kBACtB2B,KAAKK,gBACP,EAEA,oBAAMA,CAAe9B,GACnB,GAAIO,SAASyB,cAAc,qBAAsB,CAC/C,IAAIC,EAAc1B,SAASyB,cAAc,uBACzC,MAAQC,SACA,IAAI9B,SAAS+B,GAAMC,WAAWD,EAAG,OACvCD,EAAc1B,SAASyB,cAAc,uBAEvC,KAAOC,EAAYZ,UAAUe,SAAS,gCAC9B,IAAIjC,SAAS+B,GAAMC,WAAWD,EAAG,OACzClC,IAAUR,EAAKmC,YAAYC,OAAS,OAAS,QAC7CK,EAAYI,cAAcC,YACxB,CACEC,OAAQ,CACNC,UAAW,CACTxC,MAAOA,KAIb,qBAEJ,CACF,EAEAyC,yBAAwB,IAEpBxC,OAAOyC,YAAczC,OAAOyC,WAAW,gCAI3C,cAAAC,GACE,MAAMhB,EAAcnC,EAAKoD,iBAErBjB,EACFA,EAAYC,OAASH,KAAKM,iBAAmBN,KAAKN,kBAElDM,KAAKgB,2BAA2BI,QAC5BpB,KAAKM,iBACLN,KAAKN,iBAEb,EAEA,oBAAA2B,GACErB,KAAK9B,qBAAqBoD,iBAAiB,SAAS,KACnCxC,SAASa,KAAKC,UAAUe,SAAS,aACvCX,KAAKN,kBAAoBM,KAAKM,gBAAgB,GAE3D,EAEA,mBAAAiB,GACqBvB,KAAKgB,2BACbM,iBAAiB,UAAWE,IACrCA,EAAEJ,QAAUpB,KAAKM,iBAAmBN,KAAKN,iBAAiB,GAE9D,EAEA,UAAMD,GACJO,KAAK9B,qBAAuBY,SAASyB,cACnC,2BAEFP,KAAK7B,QAAUW,SAASyB,cAAc,6BACtCP,KAAK5B,uBACsB,IAAlBG,MAAME,cACkB,IAAxBF,MAAME,QAAQgD,YACgB,IAA9BlD,MAAME,QAAQgD,MAAMC,MACvBnD,MAAME,QAAQgD,MAAMC,MACpB,UACN1B,KAAK3B,sBACsB,IAAlBE,MAAME,cACkB,IAAxBF,MAAME,QAAQgD,YACe,IAA7BlD,MAAME,QAAQgD,MAAME,KACvBpD,MAAME,QAAQgD,MAAME,KACpB,OACN3B,KAAKkB,iBACLlB,KAAKqB,uBACLrB,KAAKuB,sBACL,UAtJK,IAAI7C,SAAQ,CAACC,EAASC,KAC3B,IACE,IAAIC,EAAMC,SAASC,iBAAiBf,GAClCgB,EAAQH,EAAII,OACdJ,EAAIK,SAASC,IACXA,EAAQyC,aAAa,qBAAsBzC,EAAQG,WAEtC,KADbN,GAEEL,GACF,GAEJ,CAAE,MAAOY,GACPX,EAAOW,EACT,KA0I2BsC,MAAMC,QAAQvC,MACzC,CAAE,MAAOA,GAAQ,CACnB,kBAIa,SAASwC,iBACtB9D,WAAWwB,MACb", "ignoreList": []}