export default function initBookmarkNav(){const t=document.querySelectorAll(".bookmark-nav-item"),e=document.querySelectorAll("section[id]");function setActiveNavItem(){const o=window.scrollY+100;let n=null;e.forEach((t=>{const e=t.offsetTop,c=t.offsetHeight;o>=e&&o<e+c&&(n=t)})),t.forEach((t=>{t.classList.remove("bg-second-background-color"),n&&t.getAttribute("data-category")===n.getAttribute("id")&&t.classList.add("bg-second-background-color")}))}t.length&&e.length&&(window.addEventListener("scroll",function throttle(t,e){let o;return function(){const n=arguments,c=this;o||(t.apply(c,n),o=!0,setTimeout((()=>o=!1),e))}}(setActiveNavItem,100)),setActiveNavItem())}try{swup.hooks.on("page:view",initBookmarkNav)}catch(t){}document.addEventListener("DOMContentLoaded",initBookmarkNav);
//# sourceMappingURL=bookmarkNav.js.map