---
title: dify-introduction
date: 2025-05-13 11:02:28
tags:
---





## dify部署与访问

部署在——本地机特定端口
只要在局域网下就能直接访问(包括编写好的AI应用提供的服务)(检验过) (个人测试时没有受到本地防火墙的干扰，但若出现，可以手动关闭)
以浙大为例，只要连在同一个无线网下就可以，校外用浙大RVPN应该能实现同样的效果(还没检验过)

![PixPin_2025-05-14_14-06-31](./dify-introduction/PixPin_2025-05-14_14-06-31.png)

反正找到能访问本地机的IP地址即可，如果没有上述红框圈起的地址或地址无效。可以使用ipconfig寻找当前机器的无线网络适配器的IP地址。

其他人只需要在浏览器网址栏输入 `[该地址]/signin`即可访问。如果不行的话有可能会是本地有多个web服务(产生冲突，不能自动索引？我猜的)。不管怎么说，可以使用netstat命令观察本地所有对外开放的端口，无非全部检查一遍，在明确dify服务监听的特定端口之后，在浏览器网址栏输入`[该地址]:[端口号]/signin`应该就万无一失了。

![PixPin_2025-05-14_14-19-55](./dify-introduction/PixPin_2025-05-14_14-19-55.png)



## 一些前置知识

**ollama是什么？**

~~玩具，并不重要，事实上现在有很多能够提供模型API的平台了，比如https://openrouter.ai/models  。包括我们也可以使用模型官网提供的API或者其他各种模型管理平台，对于dify来说，只需要安装对应插件就能提供支持~~

ollama是一个**模型管理平台**，可以用来管理模型，包括模型的上传、下载、部署、监控等。本身并不是模型
部署在本地机上。
**开源**~~



**模型后面跟的B是什么？**
B:billion，表示10^9，表示模型的**可训练参数的数量**



**dify是什么？**
dify是一个**开源的AI应用开发平台**
我们可以将它作为脚手架进行AI应用的开发

- 减少造轮子的工作
- 可以以较高地效率完成复杂创意工作流的设计
- 自由度很高，可以diy (比如不满意它自带的知识库检索，可以外接RAGflow API)
- 有别人写好的插件或模版可以直接套用
- 方便团队协作
- 方便调试

## 模型从哪来？

右上角用户->设置->模型供应商 (在右上角系统模型设置还能看到我们的默认模型)
dify支持了很多了模型供应商——本质上是通过插件控制了接口，非常简单，安装插件即可增加支持

![PixPin_2025-05-14_14-35-06](./dify-introduction/PixPin_2025-05-14_14-35-06.png)



### 如何添加dify上可用的模型？


**如果是我们本地GPU服务器提供的模型**

- 在ollama模型供应商里选择添加模型
  - 直接填写提供ollama服务的服务器的ip地址和端口号就好(虽然这也是ollama的安全隐患，但作为玩具，无所谓了)
    - 之后填写该服务下提供的一个 **模型名称** 即可。若是通过检验，就会成功加入

**如果是自己本机跑的模型**

- 那就填自己本机提供服务的ip地址和端口号，原理上是类似的。
  本机上的模型不一定要依赖于ollama，还可以考虑**Xorbits Inference**



**其它闭源模型**
直接填API key即可
~~但是不小心被别人拿去用了可能会狠狠烧钱~~

- 不确定添加闭源模型之后别人能否直接使用该模型进行编排(可以测试)
- ~~但其他人至少可以通过无休止地运行你编排的工作流而间接地燃烧你的API token~~

![PixPin_2025-05-14_14-34-21](./dify-introduction/PixPin_2025-05-14_14-34-21.png)



## 工作室

**创建AI应用的地方**

导入DSL文件是直接导入现有应用(别人做好的)
从应用模版创建可以从现有模版创建，~~站在巨人的肩膀上XD~~

![PixPin_2025-05-14_14-47-28](./dify-introduction/PixPin_2025-05-14_14-47-28.png)

**创建空白应用**就是正常地自己创建
~~有四种应用类型，需要关注的主要是三种~~，现2025年已经拓展到5种

- 聊天助手
- Agent
- 文本生成应用
- chatflow
- 工作流

### 三种应用类型区别

聊天助手就是字面意思：一个可以和你进行多轮对话的机器人
Agent可以简单理解为聊天助手的升级版——**可以调用工具**辅助完成任务
工作流就是一个**流程图**，在其中我们可以编排任务的处理方式，可以联合调用多个模型、工具，可以实现自动化的任务处理。

三种应用类型各有优劣

- 聊天助手：可多轮对话，支持基础编排和**工作流编排**，后者的情况下可以应用为**可多次根据用户输入处理任务的工作流**
- **工作流(~~无敌~~)**：静态编排，稳定性强，但一次性工作，结束了就结束了。但是可发布为**工具**被Agent调用
  - 如果使用新增的chatflow，那么可以循环对话，感觉已经没有痛点了......~~工作流is all you need~~

- Agent: 动态编排，灵活性强。理论上来讲，我们期望Agent做的事是：**利用自身的推理能力，将任务分解成多个子任务或者一个任务流程，并利用工具分步解决各个任务，最后输出结果**。实际上我们期望的就是它自己去实现一个工作流的编排，并执行。容易知道这对模型推理能力的要求是很高的，就我们目前拥有的模型实例而言，Agent的推理能力还不够强，所以不能太依赖模型本身的推理能力去完成任务。**通过在提示词中给出工作流程的示例或规定**、**将能完成特定任务的工作流作为工具封装好**，从一定意义上可以减轻这个问题。
  - 但个人认为这个行为本质上还是通过**降低了编排的动态性，增强其静态性**，让Agent更趋于工作流，从而降低灵活性，提高稳定性，降低对模型推理能力的要求。



### 关于提示词

可以发现需要提示词的地方右上角都有一个**AI自动生成**。如果自己不想写的话可以利用那个功能快速写一段提示词。

注意提示词的生成能力取决于你在设置的时候设置的推理模型

- 不过坦白说可以直接这样生成提示词——https://www.aipromptgenerator.net/zh?utm_source=ai-bot.cn



#### 关于编写提示词

下面是一些实践经验但不一定靠谱，靠谱的可以看这——https://www.promptingguide.ai/zh

很多时候，只需要在最后面打上几个换行，然后用 ## 开头写个小标题，然后在下面补上几句说明就行了。
使用 ## 打小标题的目的是**提供更清晰、更结构化的说明**，这有助于模型的理解。

> 例如： 
> \#\# 输出说明
> 使用中文输出
>
> \#\# 输入说明
> 用户输入为{{input}}

在提示词等地方需要插入或者绑定变量的话只需要用 \{ \{  \} \} 框起来就行

在工作流环境中给大模型配置提示词的时候可以发现有三种类型

- SYSTEM: 系统提示词——为对话提供高层指导(比如它要干什么，它的约束是什么~输出格式等~)
- USER: 用户提示词——向模型提供指令、查询或任何基于文本的输入(\{\{插入变量\}\})
- ASSITANT: 助手提示词——理论上是提供基于用户消息的模型回复
  但在实际中目前似乎并未发现三者除了名字外有什么应用差异((?



### 关于工作流的编排

**核心操作**

- 鼠标右键可添加节点或注释
- 鼠标左键选中连接线之后按**backspace**键可以删除连接线
- ctrl+c, ctrl+v可以复制粘贴节点
- 在连接线上按 **+** 图标可以直接插入一个节点
- 可以添加的节点除基础节点外还有工具(可以把工作流理解成静态编排的Agent，也是有工具调用能力的)


chatflow和workflow的节点可能会稍有不同，但本质上是一致的。

- 在**开始**节点，可定义输入变量。包括文本输入和文件输入等

- **问题分类器**，可利用模型对输入变量进行检验，完成问题的分类，决定工作流的走向

- **变量聚合器**，可将多个输入变量聚合成一个变量，方便后续进行统一处理(但是类型匹配是个讨厌的问题)

- **结束**节点，结束工作流，记得配置输出变量

完成配置之后记得**发布**和**更新**，不然不会生效
在发布选项卡里还有发布为**工具**，相当于可以对工作流进行封装，只暴露输入变量和输出变量的接口。



反正结果大概就这样，挺图形化的，挺简单的

![PixPin_2025-05-14_14-48-14](./dify-introduction/PixPin_2025-05-14_14-48-14.png)





### Agent的使用

提示词原理同上，可以AI生成，也可以人工输入。用自然语言描述即可。结构化的提示词is preferred。

可以调用工具

**特别注意**：写完之后记得发布更新！！！不然退出之后什么都不会留下(2024/12/13)

**AI应用创建完成之后按运行**，在任何地方都可以使用。直接把链接发给别人，别人就能用。(前提应该是能访问到dify部署的机子，在浙大局域网下应该就能访问)



~~坦白说，dify在创建应用模版的时候所谓的Agent并不是最符合个人对Agent的印象的，感觉实际上工作流或者说chatflow可实现的智能化程度更高？~~

好吧其实也只能说是工作流上限更高，但可能做的不好地话会偏静态

Agent应该是可以动态调用工具。

![PixPin_2025-05-14_14-52-33](./dify-introduction/PixPin_2025-05-14_14-52-33.png)



### 知识库的建立

可以上传各种文本。实际上文本本身没有特殊要求——**即便更加结构化的数据有利于AI更好地建立知识库**。随便写一段txt都可以导入建立知识库。但是杂乱的数据可能对嵌入模型的能力需求更高。

**分段设置**
自动分段与清洗，挺好的。对于一般的数据可以使用它。
如果是自己手写的知识库，推荐使用自定义。比如定义 **\#\#** 为分段标识符，就基本可以保证实现完美的分段效果。

在索引方式方面。选择**高质量**意味着用模型去进行文本嵌入，否则使用离线的文本索引方式(dify自带的)。

检索设置涉及到重排序问题，需要rerank模型。



后面新增的**父子分段**可能更好地捕捉上下文关系，见仁见智吧



## 调试



打开对应的AI应用，点到**日志**页面，可以追踪运行结果，调试还是比较方便的

![PixPin_2025-05-14_14-54-28](./dify-introduction/PixPin_2025-05-14_14-54-28.png)



## 工具



工具是Agent的灵魂，不必我多说了叭(

除了自己编排工作流发布成工具外

在dify的市场界面也可以使用很多人做好的工具

![PixPin_2025-05-14_14-56-51](./dify-introduction/PixPin_2025-05-14_14-56-51.png)



### MCP

MCP协议的出现极大地赋能了AI工具的开发，现在市面上已有上万MCP server

- 其中一个网站：https://mcp.so/

虽然尚未尝试，但注意到dify的拓展中已经出现了对MCP相关的支持，如果能够使用MCP赋能，那么dify的边界将会更加强大

—— ~~或许只要不涉及到底层算法层面的革新，dify已经能够满足对AI应用开发的所有需求(bushi)~~

![PixPin_2025-05-14_14-57-34](./dify-introduction/PixPin_2025-05-14_14-57-34.png)

