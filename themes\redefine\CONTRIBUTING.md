# Contributing to hexo-theme-redefine

Thank you for considering contributing to hexo-theme-redefine! We appreciate your time and effort to make our theme better.

Before you start, please make sure you read the [README](README.md) and familiarize yourself with the project.

## Getting Started

To start contributing to hexo-theme-redefine, please follow these steps:

1. Fork the [repository](https://github.com/EvanNotFound/hexo-theme-redefine) on GitHub.
2. Clone your forked repository to your local machine.
3. Make your changes and test them locally.
4. Commit your changes and push them to your forked repository.
5. Create a pull request (PR) on GitHub to **`dev` branch**.

## Guidelines

We value your contributions and want to ensure they are appropriate for the project. Please follow these guidelines when contributing:

1. Use [GitHub issues](https://github.com/yourusername/yourthemename/issues) to report bugs, suggest new features, or ask questions.

2. Use descriptive commit messages and include references to related issues or PRs.

   Format: 

   ```
   [section]: [brief info]
   ```

   For example:

   ```
   footer: optimize style
   ```

3. Follow the existing code style and conventions.

4. Write clear and concise documentation for any changes you make.

5. Test your changes locally and ensure they do not break the existing functionality.

## Code of Conduct

To ensure a welcoming and inclusive community, we adhere to the [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). Please read this before contributing.

## Contact

If you have any questions or need help with your contributions, please contact <NAME_EMAIL>.

Thank you for your contributions to hexo-theme-redefine!