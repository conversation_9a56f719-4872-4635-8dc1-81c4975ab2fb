{"version": 3, "file": "codeBlock.js", "names": ["HTMLElement", "prototype", "wrap", "wrapper", "this", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "document", "querySelectorAll", "for<PERSON>ach", "element", "container", "createElement", "classList", "add", "insertAdjacentHTML", "copyButton", "querySelector", "foldButton", "addEventListener", "code", "map", "line", "innerText", "join", "navigator", "clipboard", "writeText", "className", "setTimeout", "toggle", "contains"], "sources": ["0"], "mappings": "cAAqB,KACnBA,YAAYC,UAAUC,KAAO,SAAUC,GACrCC,KAAKC,WAAWC,aAAaH,EAASC,MACtCA,KAAKC,WAAWE,YAAYH,MAC5BD,EAAQK,YAAYJ,KACtB,EAEAK,SAASC,iBAAiB,oBAAoBC,SAASC,IACrD,MAAMC,EAAYJ,SAASK,cAAc,OACzCF,EAAQV,KAAKW,GACbA,EAAUE,UAAUC,IAAI,uBACxBH,EAAUI,mBACR,YACA,qEAEFJ,EAAUI,mBACR,YACA,2EAEF,MAAMC,EAAaL,EAAUM,cAAc,gBACrCC,EAAaP,EAAUM,cAAc,gBAC3CD,EAAWG,iBAAiB,SAAS,KACnC,MACMC,EADY,IAAIT,EAAUH,iBAAiB,gBAC1Ba,KAAKC,GAASA,EAAKC,YAAWC,KAAK,MAG1DC,UAAUC,UAAUC,UAAUP,GAG9BJ,EAAWC,cAAc,KAAKW,UAAY,sBAG1CC,YAAW,KACTb,EAAWC,cAAc,KAAKW,UAAY,oBAAoB,GAC7D,IAAK,IAEVV,EAAWC,iBAAiB,SAAS,KACnCR,EAAUE,UAAUiB,OAAO,UAC3BZ,EAAWD,cAAc,KAAKW,UAAYjB,EAAUE,UAAUkB,SAC5D,UAEE,yBACA,0BAA0B,GAC9B,GACF", "ignoreList": []}