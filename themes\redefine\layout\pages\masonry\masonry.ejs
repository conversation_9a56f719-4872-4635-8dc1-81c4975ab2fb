<%
// Define an array of images to display in the masonry layout
const images = theme.masonry;
%>

<h1 class="page-title-header">
	<%- getPageTitle(page) %>
</h1>

<div class="loading-placeholder">
	<div class="flex-grid generic-card">
		<div class="card loading"></div>
		<div class="card loading"></div>
		<div class="card loading"></div>
	</div>
</div>

<div id="masonry-container">
	<% images.forEach(function(image) { %>
	<div class="masonry-item">
		<div class="image-container">
			<img 
				class="lazyload"
				data-src="<%- image.image %>"
				width="<%- image.width %>" 
				height="<%- image.height %>"
				alt="<%- image.title %>"
			>
			<div class="image-title"><%- image.title %></div>
			<div class="image-description"><%- image.description %></div>
		</div>
	</div>
	<% }); %>
</div>

<!-- import lazysizes -->
<script src="<%- url_for('js/libs/lazysizes.min.js') %>" async></script>