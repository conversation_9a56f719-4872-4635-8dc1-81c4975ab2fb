@require '../variables.styl'

if (hexo-config('articles.code_block.highlight_theme.light') == 'github') {
  @require 'hljs-themes/light/github.styl'
} else if (hexo-config('articles.code_block.highlight_theme.light') == 'atom-one-light') {
  @require 'hljs-themes/light/atom-one-light.styl'
} else if (hexo-config('articles.code_block.highlight_theme.light') == 'default') {
  @require 'hljs-themes/light/default.styl'
} else {
  @require 'hljs-themes/light/github.styl'
}

if (hexo-config('articles.code_block.highlight_theme.dark') == 'github-dark') {
  @require 'hljs-themes/dark/github-dark.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'vs2015') {
  @require 'hljs-themes/dark/vs2015.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'atom-one-dark') {
  @require 'hljs-themes/dark/atom-one-dark.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'a11y-dark') {
  @require 'hljs-themes/dark/a11y-dark.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'agate') {
  @require 'hljs-themes/dark/agate.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'monokai-sublime') {
  @require 'hljs-themes/dark/monokai-sublime.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'night-owl') {
  @require 'hljs-themes/dark/night-owl.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'nord') {
  @require 'hljs-themes/dark/nord.styl'
} else if (hexo-config('articles.code_block.highlight_theme.dark') == 'tokyo-night-dark') {
  @require 'hljs-themes/dark/tokyo-night-dark.styl'
} else {
  @require 'hljs-themes/dark/github-dark.styl'
}

$highlight-gutter-color = #6a737d
$highlight-gutter-bg-color = #f6f8fa
$dark-highlight-gutter-color = #8b949e
$dark-highlight-gutter-bg-color = #161b22

$inline-code-foreground = #24292e
$inline-code-background = #f1f1f1
$dark-inline-code-foreground = #c9d1d9
$dark-inline-code-background = #313131

code-theme(mode)
  --inline-code-foreground: mode == 'light' ? $inline-code-foreground : $dark-inline-code-foreground
  --inline-code-background: mode == 'light' ? $inline-code-background : $dark-inline-code-background
  --highlight-background: mode == 'light' ? $highlight-background : $dark-highlight-background
  --highlight-foreground: mode == 'light' ? $highlight-foreground : $dark-highlight-foreground
  --highlight-keyword: mode == 'light' ? $highlight-keyword : $dark-highlight-keyword
  --highlight-link: mode == 'light' ? $highlight-link : $dark-highlight-link
  --highlight-built-in: mode == 'light' ? $highlight-built-in : $dark-highlight-built-in
  --highlight-number: mode == 'light' ? $highlight-number : $dark-highlight-number
  --highlight-string: mode == 'light' ? $highlight-string : $dark-highlight-string
  --highlight-regexp: mode == 'light' ? $highlight-regexp : $dark-highlight-regexp
  --highlight-subst: mode == 'light' ? $highlight-subst : $dark-highlight-subst
  --highlight-comment: mode == 'light' ? $highlight-comment : $dark-highlight-comment
  --highlight-doctag: mode == 'light' ? $highlight-doctag : $dark-highlight-doctag
  --highlight-meta: mode == 'light' ? $highlight-meta : $dark-highlight-meta
  --highlight-variable: mode == 'light' ? $highlight-variable : $dark-highlight-variable
  --highlight-attr: mode == 'light' ? $highlight-attr : $dark-highlight-attr
  --highlight-section: mode == 'light' ? $highlight-section : $dark-highlight-section
  --highlight-bullet: mode == 'light' ? $highlight-bullet : $dark-highlight-bullet
  --highlight-addition: mode == 'light' ? $highlight-addition : $dark-highlight-addition
  --highlight-deletion: mode == 'light' ? $highlight-deletion : $dark-highlight-deletion

:root
  code-theme('light')

@media (prefers-color-scheme light)
  :root
    code-theme('light')

@media (prefers-color-scheme dark)
  :root
    code-theme('dark')

.light-mode
  code-theme('light')

.dark-mode
  code-theme('dark')
