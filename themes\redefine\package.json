{"name": "hexo-theme-redefine", "version": "2.8.5", "private": false, "description": "Redefine your writing with Hexo Theme Redefine.", "scripts": {"npm:publish": "npm run build && npm publish", "build": "npm run build:css && npm run build:js", "build:css": "npx @tailwindcss/cli -i source/css/tailwind.source.css -o source/css/build/tailwind.css --minify", "build:js": "node source/js/build.js", "watch:css": "npx @tailwindcss/cli -i source/css/tailwind.source.css -o source/css/build/tailwind.css --watch --minify", "prepare": "husky", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:patch": "standard-version --release-as patch", "release:major": "standard-version --release-as major"}, "repository": {"type": "git", "url": "git+https://github.com/EvanNotFound/hexo-theme-redefine.git"}, "keywords": ["hexo", "theme", "redefine", "simple", "pure", "powerful", "hexo-theme"], "author": "EvanNotFound", "license": "GPL-3.0", "bugs": {"url": "https://github.com/EvanNotFound/hexo-theme-redefine/issues"}, "homepage": "https://redefine.ohevan.com", "devDependencies": {"eslint": "^8.47.0", "glob-promise": "^6.0.7", "husky": "^9.1.6", "postcss": "^8.4.47", "standard-version": "^9.5.0", "tailwindcss": "^4.1.11", "terser": "^5.36.0"}, "dependencies": {"image-size": "^2.0.2", "node-fetch": "^2.7.0"}}