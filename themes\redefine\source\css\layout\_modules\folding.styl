details
    margin-bottom 1rem

    &> summary
        list-style none

    &> summary::-webkit-details-marker
        display none

    &> summary::marker
        display none

details[open] > summary
    border-radius $redefine-border-radius-small $redefine-border-radius-small 0 0
    border-bottom 1px solid transparent

    i.fa-chevron-right
        transform rotate(90deg)
        transition transform 0.2s ease

summary
    &:marker
        margin-right 100px

    i.fa-chevron-right
        transform rotate(0deg)
        transition transform 0.2s ease
        margin-right 1rem
        float right
        padding-top 6px

    border solid 1px var(--shadow-color-1)
    border-radius $redefine-border-radius-small
    padding 15px
    font-size 1.2rem
    background-color var(--background-color)
    line-height 2rem
    cursor pointer

    &:hover
        background-color #A6A6A630

    p
        display inline-block
        margin 0
        float left

details div.content
    border-radius 0 0 $redefine-border-radius-small $redefine-border-radius-small
    border solid 1px var(--shadow-color-1)
    padding 1rem
    margin-bottom 1rem
    background-color var(--second-background-color)
    animation fade 0.2s ease forwards

    &>:first-child
        margin-top 0

::marker
    // padding-left 100px

details.yellow
    summary
        // border-left solid 4px #FFA930
        &:hover
            background-color #FFEE3030

    &[open] > summary
        background-color #FFEE3030

details.blue
    summary
        // border-left solid 4px #00A6FF
        &:hover
            background-color #00A6FF30

    &[open] > summary
        background-color #00A6FF30

details.green
    summary
        // border-left solid 4px #00FFA6
        &:hover
            background-color #00FFA630
    
    &[open] > summary
        background-color #00FFA630

details.red
    summary
        // border-left solid 4px #FF0000
        &:hover
            background-color #FF000030

    &[open] > summary
        background-color #FF000030

details.orange
    summary
        // border-left solid 4px #FFA930
        &:hover
            background-color #FF983030

    &[open] > summary
        background-color #FF983030

details.pink
    summary
        // border-left solid 4px #FF00FF
        &:hover
            background-color #FF00FF30

    &[open] > summary
        background-color #FF00FF30

details.cyan
    summary
        &:hover
            background-color #00FFFF30
    
    &[open] > summary
        background-color #00FFFF30

    

details.white
    summary
        // border-left solid 4px #FFFFFF
        &:hover
            background-color #FFFFFF30

    &[open] > summary
        background-color #FFFFFF30
    
details.black
    summary
        // border-left solid 4px #000000
        &:hover
            background-color #00000030
    
    &[open] > summary
        background-color #00000030

details.gray
    summary
        // border-left solid 4px #A6A6A6
        &:hover
            background-color #A6A6A630

    &[open] > summary
        background-color #A6A6A630

details.purple
    summary
        // border-left solid 4px #A763FF
        &:hover
            background-color #A763FF30

    &[open] > summary
        background-color #A763FF30

details
    summary
        // border-left solid 4px #A6A6A6
        &:hover
            background-color #A6A6A630

    &[open] > summary
        background-color #A6A6A630

@keyframes fade
    from
        opacity 0
        visibility hidden
        transform translateY(-10px)

    to
        opacity 1
        visibility visible
        transform translateY(0)
