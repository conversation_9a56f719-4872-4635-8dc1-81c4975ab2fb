{"version": 3, "file": "essays.js", "names": ["formatEssayDates", "dateElements", "document", "querySelectorAll", "for<PERSON>ach", "element", "rawDate", "getAttribute", "locale", "config", "language", "formattedDate", "moment", "calendar", "textContent", "swup", "hooks", "on", "e", "console", "error", "addEventListener"], "sources": ["0"], "mappings": "AACA,SAASA,mBACP,MAAMC,EAAeC,SAASC,iBAAiB,eAE1CF,GAILA,EAAaG,SAAQ,SAAUC,GAC7B,MAAMC,EAAUD,EAAQE,aAAa,aAC/BC,EAASC,OAAOC,UAAY,KAE5BC,EAAgBC,OAAON,GAASE,OAAOA,GAAQK,WACrDR,EAAQS,YAAcH,CACxB,GACF,CAEA,IACEI,KAAKC,MAAMC,GAAG,YAAajB,iBAC7B,CAAE,MAAOkB,GACPC,QAAQC,MAAMF,EAChB,CAGAhB,SAASmB,iBAAiB,mBAAoBrB", "ignoreList": []}