# See http://help.github.com/ignore-files/ for more about ignoring files

# compiled output
/dist
/tmp
/out-tsc
/cache

# dependencies
/node_modules
yarn.lock

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
# !.vscode/settings.json
# !.vscode/tasks.json
# !.vscode/launch.json
# !.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
testem.log
/typings

# e2e
/e2e/src/*.js
/e2e/src/*.map
/cypress/screenshots

# System Files
.DS_Store
Thumbs.db
.com.greenworldsoft.syncfolderspro
_DelSyncFiles/

.env
.env.local
.vercel
