.image-viewer-container
  position fixed
  left 0
  top 0
  width 100%
  height 100%
  display flex
  align-items center
  justify-content center
  background rgba(0, 0, 0, 0)
  visibility hidden
  z-index $z-index-8
  padding 6%
  box-sizing border-box
  backdrop-filter blur(0px)
  -webkit-backdrop-filter blur(0px)
  transition-t('visibility, background,backdrop-filter', '0, 0,0', '0.3, 0.3,0.3', 'ease, ease,ease')

  &.active
    background rgba(0, 0, 0, 0.3)
    backdrop-filter blur(10px)
    -webkit-backdrop-filter blur(10px)
    visibility visible

    img
      cursor grab
      transform scale(1)
      opacity 1
      padding 2px
      background var(--background-color)

  img
    max-width 100%
    max-height 100%
    transform scale(0.5)
    opacity 0
    transition-t('transform, opacity', '0,0', '0.3,0.3', 'ease')
