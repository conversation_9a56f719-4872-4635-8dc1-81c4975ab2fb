$temp-title-fs = hexo-config('home_banner.text_style.title_size')
$title-font-size = $temp-title-fs ? convert($temp-title-fs) : 2.8rem
$temp-subtitle-fs = hexo-config('home_banner.text_style.subtitle_size')
$subtitle-font-size = $temp-subtitle-fs ? convert($temp-subtitle-fs) : 1.4rem
$temp-line-height = hexo-config('style.first_screen.line_height')
$line-height = $temp-line-height ? $temp-line-height : 1.2
$home-banner-icon-size = 1.6rem

@require '../../common/variables'

.home-banner-background
  //background var(--home-banner-img) center center / cover no-repeat
  transition filter 0.3s ease

.home-banner-container
  font-family 'Chillax-Variable', sans-serif
  position relative
  box-sizing border-box
  width 100%
  min-height 100vh
  overflow hidden
  will-change transform

  .content
    position relative
    box-sizing border-box
    min-height 90vh

    .description
      font-size $title-font-size
      line-height $line-height
      color var(--home-banner-text-color)

      p
        margin 0
        opacity 0.9
        font-family 'Geist Variable', sans-serif
        font-weight 350

        i
          font-size $subtitle-font-size
          font-style normal

        span
          font-size: $subtitle-font-size * 1.05

      +redefine-tablet()
        font-size: $title-font-size * 0.9