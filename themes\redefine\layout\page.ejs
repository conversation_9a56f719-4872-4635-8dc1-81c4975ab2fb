<%- partial('components/header/progress-bar') %>
<% if (theme.global.preloader === true || theme.global.preloader.enable === true) { %>
<%- partial('components/header/preloader') %>
<% } %>
<main class="page-container" id="swup">

	<% if (theme.home_banner.enable === true && is_home() && !page.prev) { %>
	<%- partial('pages/home/<USER>') %>
	<% } else if (theme.home_banner.enable === true && is_home() && page.prev && theme.home_banner.style === "fixed") { %>
	<%- partial('pages/home/<USER>') %>
	<% } %>

	<div class="main-content-container flex flex-col justify-between min-h-dvh">
		<div class="main-content-header">
			<%- partial('components/header/navbar') %>
		</div>

		<div class="main-content-body transition-fade-up">
			<% if (is_home() && theme.home.sidebar.enable === true && theme.home.sidebar.position === "left") { %>
			<%- partial('pages/home/<USER>') %>
			<% } %>

			<div class="main-content">
				<%- partial(getPagePartialPath(page)) %>
			</div>

			<% if (is_home() && theme.home.sidebar.enable === true && theme.home.sidebar.position === "right") { %>
			<%- partial('pages/home/<USER>') %>
			<% } %>
		</div>

		<div class="main-content-footer">
			<%- partial('components/footer/footer') %>
		</div>
	</div>

	<% if (is_post()) { %>
	<div class="post-tools">
		<%- partial('pages/post/post-tools') %>
	</div>
	<% } %>

	<div class="right-side-tools-container">
		<%- partial('utils/side-tools') %>
	</div>

	<%- partial('utils/image-viewer') %>

	<% if (theme.navbar.search.enable) { %>
	<%- partial('utils/local-search') %>
	<% } %>

</main>

<% if (theme.global.single_page !== false) { %>
<%- partial('components/swup') %>
<% } %>