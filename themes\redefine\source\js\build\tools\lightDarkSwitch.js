import{main as e}from"../main.js";const t=".mermaid";export const ModeToggle={modeToggleButton_dom:null,iconDom:null,mermaidLightTheme:null,mermaidDarkTheme:null,async mermaidInit(e){window.mermaid&&(await new Promise(((e,i)=>{try{var o=document.querySelectorAll(t),a=o.length;o.forEach((t=>{null!=t.getAttribute("data-original-code")&&(t.removeAttribute("data-processed"),t.innerHTML=t.getAttribute("data-original-code")),0==--a&&e()}))}catch(e){i(e)}})),mermaid.initialize({theme:e}),mermaid.init({theme:e},document.querySelectorAll(t)))},enableLightMode(){document.body.classList.remove("dark-mode"),document.documentElement.classList.remove("dark"),document.body.classList.add("light-mode"),document.documentElement.classList.add("light"),this.iconDom.className="fa-regular fa-moon",e.styleStatus.isDark=!1,e.setStyleStatus(),this.mermaidInit(this.mermaidLightTheme),this.setGiscusTheme()},enableDarkMode(){document.body.classList.remove("light-mode"),document.documentElement.classList.remove("light"),document.body.classList.add("dark-mode"),document.documentElement.classList.add("dark"),this.iconDom.className="fa-regular fa-brightness",e.styleStatus.isDark=!0,e.setStyleStatus(),this.mermaidInit(this.mermaidDarkTheme),this.setGiscusTheme()},async setGiscusTheme(t){if(document.querySelector("#giscus-container")){let i=document.querySelector("iframe.giscus-frame");for(;!i;)await new Promise((e=>setTimeout(e,1e3))),i=document.querySelector("iframe.giscus-frame");for(;i.classList.contains("giscus-frame--loading");)await new Promise((e=>setTimeout(e,1e3)));t??=e.styleStatus.isDark?"dark":"light",i.contentWindow.postMessage({giscus:{setConfig:{theme:t}}},"https://giscus.app")}},isDarkPrefersColorScheme:()=>window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)"),initModeStatus(){const t=e.getStyleStatus();t?t.isDark?this.enableDarkMode():this.enableLightMode():this.isDarkPrefersColorScheme().matches?this.enableDarkMode():this.enableLightMode()},initModeToggleButton(){this.modeToggleButton_dom.addEventListener("click",(()=>{document.body.classList.contains("dark-mode")?this.enableLightMode():this.enableDarkMode()}))},initModeAutoTrigger(){this.isDarkPrefersColorScheme().addEventListener("change",(e=>{e.matches?this.enableDarkMode():this.enableLightMode()}))},async init(){this.modeToggleButton_dom=document.querySelector(".tool-dark-light-toggle"),this.iconDom=document.querySelector(".tool-dark-light-toggle i"),this.mermaidLightTheme=void 0!==theme.mermaid&&void 0!==theme.mermaid.style&&void 0!==theme.mermaid.style.light?theme.mermaid.style.light:"default",this.mermaidDarkTheme=void 0!==theme.mermaid&&void 0!==theme.mermaid.style&&void 0!==theme.mermaid.style.dark?theme.mermaid.style.dark:"dark",this.initModeStatus(),this.initModeToggleButton(),this.initModeAutoTrigger();try{await new Promise(((e,i)=>{try{var o=document.querySelectorAll(t),a=o.length;o.forEach((t=>{t.setAttribute("data-original-code",t.innerHTML),0==--a&&e()}))}catch(e){i(e)}})).catch(console.error)}catch(e){}}};export default function initModeToggle(){ModeToggle.init()}
//# sourceMappingURL=lightDarkSwitch.js.map