@require 'variables.styl'
@require 'animated.styl'
@require 'redefine-theme.styl'

// MINXIN ======================================================================
redefine-tablet()
  @media (max-width $media-max-width)
    {block}

redefine-mobile()
  @media (max-width $media-max-width-mobile)
    {block}

// ALL ======================================================================
*
  transition-g()


// html, body
html, body
  position relative
  width 100%
  height 100%
  margin 0
  padding 0
  color var(--default-text-color)
  background var(--background-color)
  font-family $default-font-family
  font-weight $default-font-weight
  font-size $default-font-size
  line-height $default-line-height

  --navbar-height: $navbar-height

  // Custom scrollbar
  &::-webkit-scrollbar
    width 14px
    height 8px
    transition all 0.2s ease
    display block

  &::-webkit-scrollbar-thumb
    background var(--scrollbar-color)
    border-radius $redefine-border-radius-large
    background-clip content-box
    border 3px solid transparent

  &::-webkit-scrollbar-track
    background-color var(--scroll-bar-bg-color)
    border 1px solid var(--shadow-color-1)

  &::-webkit-scrollbar-thumb:hover
    background var(--scrollbar-color-hover)
    border-radius $redefine-border-radius-large
    background-clip content-box
    border 3px solid transparent

  +redefine-tablet()
    font-size: $default-font-size * 0.96
    line-height: $default-line-height * 0.96

  +redefine-mobile()
    font-size: $default-font-size * 0.9
    line-height: $default-line-height * 0.9

  .navbar-shrink
    --navbar-height: $navbar-shrink-height

// ======================================================================
// selection
// ======================================================================
::selection
  background var(--selection-color)
  color #fff

// ======================================================================
// ul, ol, li
// ======================================================================
ul, ol, li
  list-style none

// ======================================================================
// a
// ======================================================================
a
  text-decoration none
  color var(--default-text-color)

  i, span
    color var(--default-text-color)

  &:hover, &:active
    text-decoration none !important
    color var(--primary-color)

    i, span
      color var(--primary-color)

// ======================================================================
// img
// ======================================================================
img
  &[lazyload]
    padding 10px
    margin 20px auto !important
    cursor not-allowed
    pointer-events none

// ======================================================================
// button
// ======================================================================
button
  padding 0
  margin 0
  border 0
  outline none
  cursor pointer
  background transparent

.btn
  display inline-block
  position relative
  text-align center
  cursor pointer
  white-space nowrap
  padding 8px 16px
  background-color var(--second-background-color) !important
  border-radius $redefine-border-radius-medium !important
  padding 12px 16px !important
  text-decoration none !important
  redefine-container(
    false,
    0,
    0,
    0,
    1rem
  )
  box-shadow var(--redefine-box-shadow-flat) !important
  user-select none
  -moz-user-select none
  -webkit-user-select none
  -ms-user-select none
  hover-style(
    true,
    1.06,
    1.06
  )

  &:hover, &:focus
    color var(--primary-color)
    background-color var(--background-color) !important
    box-shadow var(--redefine-box-shadow) !important

  &:active
    box-shadow var(--redefine-box-shadow-flat) !important
    transform scale(0.95)
    transition transform 0.1s ease
    background-color var(--third-background-color) !important

// ======================================================================
// clear float
// ======================================================================
.clear
  clear both



