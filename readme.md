使用pnpm

使用主题
- redefine
  - 这里通过在package.json中引入主题来安装redefine: "hexo-theme-redefine": "^2.8.2",
  - 坑：需要pnpm add js-yaml
  - 2025/8/5，更新，为了更方便地对主题进行DIY，使用pnpm remove hexo-theme-redefine从node_modules里面移除该主题，并通过git clone的方式下载到themes/redefine/

by the way
还安装了word count 和 nodejieba——根据关键词推荐，具体可见package.json
-  据说这玩意儿比nodejieba更稳定且更快，但是可惜主题用的是nodejieba —— "@node-rs/jieba": "^2.0.1",
-  pnpm在v10之后对自动化脚本构建有了更严格的要求，可能导致nodejieba的二进制文件编译不出来，所以我回退了pnpm版本
   -  现为9.14.2

安装hexo-asset-link
用来管理markdown图片引用的链接问题

- 只需要在对应md文件旁边的同名文件夹里放图片并引用即可
  - 我的typora已经设置为自动插入到同名文件夹
  - by the way, ctrl+F可以快速替换原来的路径引用为新的

## 关于redefine主题

害，这些记起来太麻烦了，不如直接去看 https://redefine-docs.ohevan.com/zh/home/<USER>
- 看**配置**版块，都挺清楚的

### _config.redefine.yml中的一些属性

主题色
配置项名称：colors.primary

请写 Hex 值，如 #1890ff。

主题色会影响到按钮、进度条、链接、选中等组件的颜色。

---

global属性中有一个hover属性，可以控制鼠标悬停的时候的阴影和缩放
- 默认缩放是关的，我把他开了

---

homebanner 可以放链接

---

**navbar 配置目前还有问题**


启用了deletemask
- 删除线自带黑幕遮罩效果

sidebar部分可以加link 哦~

author_label部分可以自定义作者标签 , 如Lv6

评论功能关了

推荐文章的图片显示目前只能显示同一种——/images/open_graph.png
不知道能不能优化成显示每篇文章的图片


footer 丑了点，但是它提供了inject功能，我们后期对footer魔改一下叭~~
- 已经完成，通过inject注入自定义的css文件，对原样式进行覆盖

**资源压缩：npm install hexo-all-minifier**
**all_minifier: true**
**本地调试的时候关一下，会影响构建速度**


文章封面缩略图和头图可以魔改一下
https://redefine-docs.ohevan.com/zh/article_customize/thumbnail



# 关于书写

## 文章自定义排序

要实现顶置的文章，需在文章页添加 sticky 属性，sticky 值越大，顶置的文章越靠前，参考如下。
```markdown
---
title: Redefine 主题使用指南
date: 2022-9-28 11:45:14
tags: [Hexo]
categories: [Hexo]
sticky: 999
---
```

## 文章缩略图 —— 显示在首页

每篇文章都可以添加缩略图
在对应文章的 front-matter 中增加
thumbnail: "IMAGE_LINK"

## 文章头图 —— 显示在文章顶部

```md
---
cover: "图片链接"
---
```

如果你设置了 首页缩略图 thumbnail  ，文章封面也会自动选择为 thumbnail 链接，当然，banner 和 cover 的优先级最高。

如果你不想设置文章封面却想保留首页头图，请把 cover 或者 banner 设置成 false


## 文章摘要

添加excerpt属性

```md
---
title: Excerpt Test
date: 2022-12-20 12:12:12
tags: Excerpt
categories: Excerpt
excerpt: "这是文章摘要 This is the excerpt of the post"
---
```

## 其它

标题和日期
可以使用
title: 
date: 

- 标题就不需要在内容中再来一遍 # 标题  了


# 未来性能优化

