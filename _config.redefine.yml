# >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# THEME REDEFINE CONFIGURATION FILE V2
# BY EVANNOTFOUND
# GITHUB: https://github.com/EvanNotFound/hexo-theme-redefine
# DOCUMENTATION: https://redefine-docs.ohevan.com
# DEMO: https://redefine.ohevan.com
# <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# BASIC INFORMATION >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/basic/info
info:
  # Site title
  title: 'Null Pointer律者 😋'
  # Site subtitle —— 这个部分是在页面上方标签部分显示的
  subtitle: ' 日常发电 | 技术分享'
  # Author name
  author: Prometheus0017
  # Site URL
  url: https://blog-seeles-secret-garden.vercel.app
# BASIC INFORMATION <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# IMAGE CONFIGURATION >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/basic/defaults
defaults:
  # Favicon
  favicon: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/seele-cute-black-removebg.png
  # Site logo
  logo: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/Griseo.png
  # Site avatar
  avatar:  https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/seele-cute-white-removebg.png
# IMAGE CONFIGURATION <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# COLORS >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/basic/colors
colors:
  #Primary color
  primary: "#fe5d6d"
  # Secondary color (TBD)
  secondary: 
  # Default theme mode initial value (will be overwritten by prefer-color-scheme)
  default_mode: light # light, dark
# COLORS <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# SITE CUSTOMIZATION >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/basic/global
global:
  # Custom global fonts
  fonts:
    # Chinese fonts
    chinese: 
      enable: true # Whether to enable custom chinese fonts
      family: "'Ma Shan Zheng', cursive" # Font family
      url: https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&display=swap # Font URL to CSS file
    # English fonts
    english: 
      enable: false # Whether to enable custom english fonts
      family:  # Font family
      url:  # Font URL to CSS file
    # Custom title fonts (navbar, sidebar)
    title:
      enable: true # Whether to enable custom title fonts
      family: "'Ma Shan Zheng', cursive" # Font family
      url: https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&display=swap # Font URL to CSS file
  # Content max width
  content_max_width: 1000px
  # Sidebar width
  sidebar_width: 210px
  # Effects on mouse hover
  hover:
    shadow: true # shadow effect
    scale: true # scale effect
  # Scroll progress
  scroll_progress:
    bar: false # progress bar
    percentage: true # percentage
  # Website counter
  website_counter:
    url: https://cn.vercount.one/js # counter API URL (no need to change)
    enable: true # enable website counter or not
    site_pv: true # site page view
    site_uv: true # site unique visitor
    post_pv: true # post page view
  # Whether to enable single page experience (using swup). See https://swup.js.org/. similar to pjax
  single_page: true
  # Whether to enable Preloader.
  preloader:
    enable: true
    custom_message: '少女祈祷中... (◕ᴗ◕✿)' # Custom message. If empty, the site title will be displayed
  # Whether to enable open graph
  open_graph:
    enable: true
    image: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/open_graph.png  # default og:image
    description: '分享技术路上的所学所思，也拾掇些钟情角色的记忆碎片💖。一个不成体系的小破站 (〃∀〃)ゞ 希望能遇见兴趣相投的你~ ✨'
  # Google Analytics
  google_analytics:
    enable: false # Whether to enable Google Analytics
    id:  # Google Analytics Measurement ID
# SITE CUSTOMIZATION <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end
    

# FONTAWESOME >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/basic/fontawesome
fontawesome: # Pro v6.2.1
  # Thin version
  thin: false
  # Light version
  light: false
  # Duotone version
  duotone: false
  # Sharp Solid version
  sharp_solid: false
# FONTAWESOME <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# HOME BANNER >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/home/<USER>
home_banner:
  # Whether to enable home banner
  enable: true
  # style of home banner
  style: fixed # static or fixed
  # Home banner image
  image: 
    light: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/夏日生存狂想曲17.png # light mode
    dark: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/sparkle.jpg # dark mode
  # Home banner title
  title: 17号的航行日志
  # Home banner subtitle
  subtitle:
    text: ['当你为错过太阳而哭泣的时候，你也要再错过群星了',
      '有一个夜晚我烧毁了所有的记忆，从此我的梦就透明了',
      '有一个早晨我扔掉了所有的昨天，从此我的脚步就轻盈了',
      '我无法选择命运，但我可以选择成为什么样的人',
      '世界是不美好，所以美好才是人们心中的渴望',
      '是大家的心意，让我成为了现在的我',
      '当一个故事要结束的时候，我们总会想起它的开始，就像是一切回到了从前',
      '生命是昂贵又脆弱的东西。一个人能活着，是因为许多无辜的生命为他献上了牺牲',
      '故事总有结束的时候，但留下的记忆不会消失，传递的感情不会消失。它们会成为后来者的钥匙，被人铭记......延伸向未来，永远存在',
      '那些带领我、指引我的人，我会循着他们的足迹前进，以此铭记，他们存在过',
      '永远不要期待在雪地里能走回头路，风雪会将你的脚印全部掩埋，你只能向前',
      '在身处黑暗时，人总会想抓住点什么吧',
      '每个人都是一面镜子，倒映出在他眼中的样子',
      '我们不需要救世主，我们自己来拯救自己',
      '铭记过去，是为了开拓更好的未来',
      '有时候，如果过于在意一件事，它反而会让人忘记初心，甚至变成阻碍我们前进的魔咒',
      '如果因为害怕玫瑰上的刺而否定玫瑰本身，进而远离花园，那会是多么遗憾的一件事啊',
      '我想……每个人的回忆，都值得被守护',
      '相信你愿意相信的，坚持你愿意坚持的，这不就够了吗？',
      '所谓童话就是总有一天会结束的故事',
      '不知何时起，我已不再畏惧黑暗，因为我知道，有一束光，永远在那里',
      '我从不相信什么命运，未来由我自己来决定',
      '即使未来不能改变，我也要自己决定到达那个结果的过程。重要的是在这段旅途中，我想要相信什么。那会决定在抵达终点时，我能够得到什么。而结局，也会因此展现截然不同的意义',
      '一个人的性格，就是他的命运',
      '任何人都有机会看清世界的本貌，并且承担它的重量——这就是我们所说的牺牲',
      '赫克托尔知道王国终将陷落，阿喀琉斯也明白自己正在走向死亡。但他们两人，依然义无反顾地踏上了战场，在这个从一开始就注定是悲剧的故事里，没有一个人曾为此犹豫或者动摇，他们如此，我们，亦是如此',
      '他们并非坚韧过于常人，只是知晓失败是胜利的另一种名字',
      '鸟为什么会飞？因为它们必须飞向天际',
      '当终焉的陨星在白垩纪降下，唯有自由的鸟儿才能跳出既定的灭亡',
      '真理属于人类，谬误属于时代',
      '执拗的花朵永远不会因暴雨而褪去颜色，你的决心也一定能在绝境中绽放真我',
      '愿你前行的道路有群星闪耀。愿你留下的足迹有百花绽放。你即是上帝的馈赠，世界因你而瑰丽',
      '悲剧并非终结，而是希望的起始。只有人类会这样想，也只有人类会将这样的信念化为现实',
      '告别过去，是为了走向未来',
      '要流传给后世的，绝不应该只有憎恨和使命。只有领略过这个时代的光辉和灿烂，未来的人们才能理解我们为之而战的意义',
      '不是被推搡，被裹挟着向前，也不是沿着被别人选定的道路行进。而是怀着这样的想法——我想要怎么做，我应该怎么做——去决定自己的命路，自己的刻印',
      '以自我的意志，朝着自身所设下的目标，用自在的方式，竭尽全力活过“我”作为主角的一生',
      '悲伤不会凭空消失，但温暖的感情也会永远在心底珍藏',
      '我们在世间留下的足迹，终会在未来的某一日，成为另一个人前行的灯火',
      '想验证一个人的真心，首先得要相信，不是吗?',
      '人性的美丽……就是如此闪耀，却易碎的事物呀。但正因为易碎，所以才要用心去保护，对不对?',
      '我们会留在过去，而你将走向未来。然后，就去绽放出独属于你的，无瑕而美丽的光辉吧!'
      ] # subtitle text, array
    hitokoto:  # 一言配置
      enable: false # Whether to enable hitokoto
      show_author: false # Whether to show author
      api: https://v1.hitokoto.cn # API URL, can add types, see https://developer.hitokoto.cn/sentence/#%E5%8F%A5%E5%AD%90%E7%B1%BB%E5%9E%8B-%E5%8F%82%E6%95%B0
    typing_speed: 80 # Typing speed (ms)
    backing_speed: 25 # Backing speed (ms)
    starting_delay: 500 # Start delay (ms)
    backing_delay: 1500 # Backing delay (ms)
    loop: true # Whether to loop
    smart_backspace: false # Whether to smart backspace
  # Color of home banner text
  text_color: 
    light: "#ffffff" # light mode
    dark: "#ff0000" # dark mode
  # Specific style of the text
  text_style: 
    # Title font size
    title_size: 2.8rem
    # Subtitle font size
    subtitle_size: 1.5rem
    # Line height between title and subtitle
    line_height: 1.2
  # Home banner custom font
  custom_font: 
    # Whether to enable custom font
    enable: false
    # Font family
    family: 
    # URL to font CSS file
    url:
  # Home banner social links
  social_links:
    # Whether to enable
    enable: true
    # Social links style
    style: default # default, reverse, center
    # Social links
    links:
      github:  https://github.com/ElysiaFollower  # your GitHub URL
      bilibili: https://space.bilibili.com/86672293?spm_id_from=333.1007.0.0
      zhihu: https://www.zhihu.com/people/54-47-65-12-54  # your ZhiHu URL
      instagram: # your Instagram URL
      twitter:  # your twitter URL
      email: <EMAIL> # your email
      # ...... # you can add more
    # Social links with QRcode drawers
    qrs:
      weixin: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/wechat-QR.png  # your Wechat QRcode image URL
      # ...... # you can add more
# HOME BANNER <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# NAVIGATION BAR >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/home/<USER>
navbar:
  # Auto hide navbar
  auto_hide: true
  # Navbar background color
  color:
    left: "#f78736" #left side //左右渐变色
    right: "#367df7"  #right side
    transparency: 35 #percent (10-99) //透明度
  # Navbar width (usually no need to modify)
  width:
    home: 1200px #home page
    pages: 1000px #other pages
  # Navbar links
  links:
    Home: 
      path: / 
      icon: fa-regular fa-house # can be empty
    Archives: # 安装的依赖里自带的，如果没有自定义则默认使用
      path: /archives 
      icon: fa-regular fa-archive # can be empty
    Categories:
     path: /categories
     icon: fa-regular fa-folder
    Tags:
      path: /tags
      icon: fa-regular fa-tags
    相册:
      path: /masonry
      icon: fa-regular fa-image
    Friends:
      path: /links
      icon: fa-regular fa-link
    # About: 
    #   icon: fa-regular fa-user
    #   submenus:
    #     Me: /about
    #     Github: https://github.com/ElysiaFollower/blog
    #     Friends: /links


    # Links: 
    #   icon: fa-regular fa-link
    #   submenus:
    #     Link1: /link1
    #     Link2: /link2
    #     Link3: /link3
    # ...... # you can add more
  # Navbar search (local search). Requires hexo-generator-searchdb (npm i hexo-generator-searchdb). See https://github.com/theme-next/hexo-generator-searchdb
  search:
    # Whether to enable
    enable: true
    # Preload search data when the page loads
    preload: true
# NAVIGATION BAR <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# HOME PAGE ARTICLE SETTINGS >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/home/<USER>
home:
  # Sidebar settings
  sidebar:
    enable: true  # Whether to enable sidebar
    position: left # Sidebar position. left, right
    first_item: info # First item in sidebar. menu, info
    # Announcement text
    announcement: |
      技术力有限，如果文章中存在错误或不足之处，欢迎来联系博主~🫡<br><br>
      引用图片来自网络，能力有限，无法一一找出原作者orz😭<br>
      如需注明作者请联系博主,侵删🫶    
    show_on_mobile: true # Whether to show sidebar navigation on mobile sheet menu
    links:
      Archives: 
        path: /archives 
        icon: fa-regular fa-archive # can be empty
      Tags: 
        path: /tags 
        icon: fa-regular fa-tags # can be empty
      Categories: 
        path: /categories 
        icon: fa-regular fa-folder # can be empty
      # ...... # you can add more

  # Article date format
  article_date_format: auto # auto, relative, YYYY-MM-DD, YYYY-MM-DD HH:mm:ss etc.
  # Article excerpt length
  excerpt_length: 200 # Max length of article excerpt
  # Article categories visibility
  categories:
    enable: true  # Whether to enable
    limit: 3 # Max number of categories to display
  # Article tags visibility
  tags:
    enable: true  # Whether to enable
    limit: 3  # Max number of tags to display
# HOME PAGE ARTICLE SETTINGS <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# ARTICLE >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/posts/articles
articles:
  # Set the styles of the article
  style:
    font_size: 16px # Font size
    line_height: 1.5 # Line height
    image_border_radius: 14px # image border radius
    image_alignment: center # image alignment. left, center
    image_caption: false # Whether to display image caption
    link_icon: true # Whether to display link icon
    delete_mask: true # Add mask effect to <del> tags, hiding content by default and revealing on hover
    title_alignment: left # Title alignment. left, center
    headings_top_spacing: # Top spacing for headings from h1-h6
      h1: 3.2rem
      h2: 2.4rem
      h3: 1.9rem
      h4: 1.6rem
      h5: 1.4rem
      h6: 1.3rem
  # Word count. Requires hexo-wordcount (npm install hexo-wordcount). See https://github.com/willin/hexo-wordcount
  word_count:
    enable: true # Whether to enable
    count: true # Whether to display word count
    min2read: true # Whether to display reading time
  # Author label
  author_label: 
    enable: true # Whether to enable
    auto: false # Whether to automatically add author label, e.g. Lv1, Lv2, Lv3...
    list: ["Lv6"]
  # Code block settings
  code_block:
    copy: true # Whether to enable code block copy button
    style: mac # mac | simple
    highlight_theme: # Color scheme for highlightjs code highlighting. For preview, see https://highlightjs.org/examples
      light: github # light mode theme, support: github, atom-one-light, default
      dark: atom-one-dark # dark mode theme, support: github-dark, monokai-sublime, vs2015, night-owl, atom-one-dark, nord, tokyo-night-dark, a11y-dark, agate
    font: # Custom font
      enable: false # Whether to enable
      family: # Font family
      url: # Font URL to CSS file
  # Table of contents settings
  toc:
    enable: true # Whether to enable TOC
    max_depth: 6 # TOC depth
    number: false # Whether to add number to TOC automatically
    expand: true # Whether to expand TOC
    init_open: true # Open toc by default
  # Whether to enable copyright notice
  copyright:
    enable: true # Whether to enable
    default: cc_by_sa # Default license, can be cc_by_nc_sa, cc_by_nd, cc_by_nc, cc_by_sa, cc_by, all_rights_reserved, public_domain
  # Whether to enable lazyload for images
  lazyload: true
  # Pangu.js (automatically add space between Chinese and English). See https://github.com/vinta/pangu.js
  pangu_js: true
  # Article recommendation. Requires nodejieba (npm install nodejieba). Transplanted from hexo-theme-volantis.
  recommendation:
    # Whether to enable article recommendation
    enable: true
    # Article recommendation title
    title: 推荐阅读
    # Max number of articles to display
    limit: 3
    # Max number of articles to display mobile
    mobile_limit: 2
    # Placeholder image
    placeholder: https://pub-a1cb67b2f5c34421bbd8c98bcf68643b.r2.dev/assets/open_graph.png
    # Skip directory
    skip_dirs: ["/docs/", "/private/"] # 推荐文章时排除的目录
# ARTICLE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# COMMENT >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/posts/comment
comment:
  # Whether to enable comment
  enable: true
  # Comment system
  system: giscus # waline, gitalk, twikoo, giscus
  # System configuration
  config:
    # Waline comment system. See https://waline.js.org/
    waline:
      serverUrl: https://example.example.com # Waline server URL. e.g. https://example.example.com
      lang: zh-CN # Waline language. e.g. zh-CN, en-US. See https://waline.js.org/guide/client/i18n.html
      emoji: [] # Waline emojis, see https://waline.js.org/guide/features/emoji.html
      recaptchaV3Key: # Google reCAPTCHA v3 key. See https://waline.js.org/reference/client/props.html#recaptchav3key
      turnstileKey: # Turnstile key. See https://waline.js.org/reference/client/props.html#turnstilekey
      reaction: false # Waline reaction. See https://waline.js.org/reference/client/props.html#reaction
    # Gitalk comment system. See https://github.com/gitalk/gitalk
    gitalk:
      clientID: # GitHub Application Client ID
      clientSecret: # GitHub Application Client Secret
      repo: # GitHub repository
      owner: # GitHub repository owner
      proxy: # GitHub repository proxy
    # Twikoo comment system. See https://twikoo.js.org/
    twikoo:
      version: 1.6.10 # Twikoo version, do not modify if you dont know what it is
      server_url: # Twikoo server URL. e.g. https://example.example.com
      region: # Twikoo region. can be empty
    # Giscus comment system. See https://giscus.app/
    giscus:
      repo: ElysiaFollower/ElysiaFollower.github.io # Github repository name e.g. EvanNotFound/hexo-theme-redefine
      repo_id: R_kgDOOlwzaQ # Github repository id
      category: Announcements # Github discussion category
      category_id: DIC_kwDOOlwzac4Ct0-D # Github discussion category id
      mapping: pathname # Which value to use as the unique identifier for the page. e.g. pathname, url, title, og:title. DO NOT USE og:title WITH PJAX ENABLED since pjax will not update og:title when the page changes
      strict: 0 # Whether to enable strict mode. e.g. 0, 1
      reactions_enabled: 1 # Whether to enable reactions. e.g. 0, 1
      emit_metadata: 0 # Whether to emit metadata. e.g. 0, 1
      lang: zh-CN # Giscus language. e.g. en, zh-CN, zh-TW
      input_position: bottom # Place the comment box above/below the comments. e.g. top, bottom
      loading: lazy # Load the comments lazily
# COMMENT <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# FOOTER >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/footer
footer:
  # Show website running time
  runtime: true # show website running time or not
  # Icon in footer, write fontawesome icon code here
  icon: '<i class="fa-solid fa-heart fa-beat" style="--fa-animation-duration: 0.5s; color: #f54545"></i>'
  # The start time of the website, format: YYYY/MM/DD HH:mm:ss
  start: 2025/5/7 23:03:00
  # Site statistics
  statistics: true # show site statistics or not (total articles, total words)
  # Footer message
  customize: '从二次元到二进制 (From ACG to Binary)'
  # ICP record number. See https://beian.miit.gov.cn/
  icp:
    enable: false # Whether to enable
    number: # ICP record number
    url: # ICP record url
# FOOTER <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# INJECT >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/inject
inject:
  # Whether to enable inject
  enable: true
  # Inject custom head html code
  head: 
    #- '<link rel="stylesheet" href="/css/my-custom-footer.css">'
    - 
  # Inject custom footer html code
  footer:
    -
    - 
# INJECT <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# PLUGINS >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/plugins
plugins:
  # RSS feed. Requires hexo-generator-feed (npm i hexo-generator-feed). See https://github.com/hexojs/hexo-generator-feed
  feed:
    enable: true # Whether to enable
  # Aplayer. See https://github.com/DIYgod/APlayer
  aplayer:
    enable: true # Whether to enable
    type: fixed # fixed, mini
    audios:
      - name: 光へ (向着光) # audio name
        artist: フランシュシュ # audio artist
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%85%89%E3%81%B8/光へ.mp3 # audio url
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%85%89%E3%81%B8/光へ.jpg # audio cover url
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%85%89%E3%81%B8/光へ.lrc # audio cover lrc
      - name: 輝いて
        artist: フランシュシュ
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E8%BC%9D%E3%81%84%E3%81%A6/輝いて.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E8%BC%9D%E3%81%84%E3%81%A6/輝いて.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E8%BC%9D%E3%81%84%E3%81%A6/輝いて.lrc
      - name: Dye the sky.
        artist: シャイニーカラーズ
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Dye%20the%20sky./Dye the sky..mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Dye%20the%20sky./Dye the sky..jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Dye%20the%20sky./Dye the sky..lrc
      - name: 限りなく灰色へ (feat. 宵崎奏&朝比奈まふゆ&東雲絵名&暁山瑞希&鏡音リン)
        artist: すりぃ, 25時、ナイトコードで。, 鏡音リン
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E9%99%90%E3%82%8A%E3%81%AA%E3%81%8F%E7%81%B0%E8%89%B2%E3%81%B8%20%28feat.%20%E5%AE%B5%E5%B4%8E%E5%A5%8F%26%E6%9C%9D%E6%AF%94%E5%A5%88%E3%81%BE%E3%81%B5%E3%82%86%26%E6%9D%B1%E9%9B%B2%E7%B5%B5%E5%90%8D%26%E6%9A%81%E5%B1%B1%E7%91%9E%E5%B8%8C%26%E9%8F%A1%E9%9F%B3%E3%83%AA%E3%83%B3%29/限りなく灰色へ (feat. 宵崎奏&朝比奈まふゆ&東雲絵名&暁山瑞希&鏡音リン).mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E9%99%90%E3%82%8A%E3%81%AA%E3%81%8F%E7%81%B0%E8%89%B2%E3%81%B8%20%28feat.%20%E5%AE%B5%E5%B4%8E%E5%A5%8F%26%E6%9C%9D%E6%AF%94%E5%A5%88%E3%81%BE%E3%81%B5%E3%82%86%26%E6%9D%B1%E9%9B%B2%E7%B5%B5%E5%90%8D%26%E6%9A%81%E5%B1%B1%E7%91%9E%E5%B8%8C%26%E9%8F%A1%E9%9F%B3%E3%83%AA%E3%83%B3%29/限りなく灰色へ (feat. 宵崎奏&朝比奈まふゆ&東雲絵名&暁山瑞希&鏡音リン).jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E9%99%90%E3%82%8A%E3%81%AA%E3%81%8F%E7%81%B0%E8%89%B2%E3%81%B8%20%28feat.%20%E5%AE%B5%E5%B4%8E%E5%A5%8F%26%E6%9C%9D%E6%AF%94%E5%A5%88%E3%81%BE%E3%81%B5%E3%82%86%26%E6%9D%B1%E9%9B%B2%E7%B5%B5%E5%90%8D%26%E6%9A%81%E5%B1%B1%E7%91%9E%E5%B8%8C%26%E9%8F%A1%E9%9F%B3%E3%83%AA%E3%83%B3%29/限りなく灰色へ (feat. 宵崎奏&朝比奈まふゆ&東雲絵名&暁山瑞希&鏡音リン).lrc
      - name: アンチユー (Anti You)
        artist: MORE MORE JUMP!
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%82%A2%E3%83%B3%E3%83%81%E3%83%A6%E3%83%BC/アンチユー.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%82%A2%E3%83%B3%E3%83%81%E3%83%A6%E3%83%BC/アンチユー.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%82%A2%E3%83%B3%E3%83%81%E3%83%A6%E3%83%BC/アンチユー.lrc
      - name: 白昼梦
        artist: こぴ
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%99%BD%E6%98%BC%E6%A2%A6/白昼梦.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%99%BD%E6%98%BC%E6%A2%A6/白昼梦.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%99%BD%E6%98%BC%E6%A2%A6/白昼梦.lrc
      - name: 罪の天使
        artist: TetraCalyx
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%BD%AA%E3%81%AE%E5%A4%A9%E4%BD%BF/罪の天使.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%BD%AA%E3%81%AE%E5%A4%A9%E4%BD%BF/罪の天使.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E7%BD%AA%E3%81%AE%E5%A4%A9%E4%BD%BF/罪の天使.lrc
      - name: Da Capo
        artist: HOYO-MiX
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Da%20Capo/Da Capo.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Da%20Capo/Da Capo.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Da%20Capo/Da Capo.lrc
      - name: Moon Halo
        artist: 茶理理, TetraCalyx, Hanser, HOYO-MiX
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Moon%20Halo/Moon Halo.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Moon%20Halo/Moon Halo.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Moon%20Halo/Moon Halo.lrc
      - name: TruE
        artist: 黄龄, HOYO-MiX
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/TruE/TruE.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/TruE/TruE.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/TruE/TruE.lrc
      - name: 崩坏世界的歌姬 (Movie Ver.)
        artist: 小林未郁
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%B4%A9%E5%9D%8F%E4%B8%96%E7%95%8C%E7%9A%84%E6%AD%8C%E5%A7%AC%20%28Movie%20Ver.%29/崩坏世界的歌姬 (Movie Ver.).mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%B4%A9%E5%9D%8F%E4%B8%96%E7%95%8C%E7%9A%84%E6%AD%8C%E5%A7%AC%20%28Movie%20Ver.%29/崩坏世界的歌姬 (Movie Ver.).jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%B4%A9%E5%9D%8F%E4%B8%96%E7%95%8C%E7%9A%84%E6%AD%8C%E5%A7%AC%20%28Movie%20Ver.%29/崩坏世界的歌姬 (Movie Ver.).lrc
      - name: Starfall
        artist: 袁娅维TIA RAY
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Starfall/Starfall.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Starfall/Starfall.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Starfall/Starfall.lrc
      - name: Cyberangel
        artist: Hanser
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Cyberangel/Cyberangel.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Cyberangel/Cyberangel.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Cyberangel/Cyberangel.lrc
      - name: ノット・グッド・ワールド
        artist: あやぽんず*, あよ
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%8E%E3%83%83%E3%83%88%E3%83%BB%E3%82%B0%E3%83%83%E3%83%89%E3%83%BB%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/ノット・グッド・ワールド.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%8E%E3%83%83%E3%83%88%E3%83%BB%E3%82%B0%E3%83%83%E3%83%89%E3%83%BB%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/ノット・グッド・ワールド.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%8E%E3%83%83%E3%83%88%E3%83%BB%E3%82%B0%E3%83%83%E3%83%89%E3%83%BB%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/ノット・グッド・ワールド.lrc
      - name: 星になる (feat. 倚水)
        artist: Islet, 倚水
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E6%98%9F%E3%81%AB%E3%81%AA%E3%82%8B%20%28feat.%20%E5%80%9A%E6%B0%B4%29/星になる (feat. 倚水).mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E6%98%9F%E3%81%AB%E3%81%AA%E3%82%8B%20%28feat.%20%E5%80%9A%E6%B0%B4%29/星になる (feat. 倚水).jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E6%98%9F%E3%81%AB%E3%81%AA%E3%82%8B%20%28feat.%20%E5%80%9A%E6%B0%B4%29/星になる (feat. 倚水).lrc
      - name: REVENGE
        artist: フランシュシュ
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/REVENGE/REVENGE.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/REVENGE/REVENGE.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/REVENGE/REVENGE.lrc
      - name: 拂晓 Proi Proi
        artist: HOYO-MiX, NIDA
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/拂晓 Proi Proi/拂晓 Proi Proi.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/拂晓 Proi Proi/拂晓 Proi Proi.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/拂晓 Proi Proi/拂晓 Proi Proi.lrc
      - name: 何者
        artist: 谭晶, HOYO-MiX
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E4%BD%95%E8%80%85/何者.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E4%BD%95%E8%80%85/何者.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E4%BD%95%E8%80%85/何者.lrc
      - name: アンノウン・マザーグース
        artist: wowaka, 初音ミク
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/wowaka%2C%20%E5%88%9D%E9%9F%B3%E3%83%9F%E3%82%AF/wowaka, 初音ミク.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/wowaka%2C%20%E5%88%9D%E9%9F%B3%E3%83%9F%E3%82%AF/wowaka, 初音ミク.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/wowaka%2C%20%E5%88%9D%E9%9F%B3%E3%83%9F%E3%82%AF/wowaka, 初音ミク.lrc
      - name: "[囧菌] シニカルナイトプラン（翻自 初音ミク）"
        artist: 封茗囧菌
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%5B%E5%9B%A7%E8%8F%8C%5D%20%E3%82%B7%E3%83%8B%E3%82%AB%E3%83%AB%E3%83%8A%E3%82%A4%E3%83%88%E3%83%97%E3%83%A9%E3%83%B3/[囧菌] シニカルナイトプラン.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%5B%E5%9B%A7%E8%8F%8C%5D%20%E3%82%B7%E3%83%8B%E3%82%AB%E3%83%AB%E3%83%8A%E3%82%A4%E3%83%88%E3%83%97%E3%83%A9%E3%83%B3/[囧菌] シニカルナイトプラン.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%5B%E5%9B%A7%E8%8F%8C%5D%20%E3%82%B7%E3%83%8B%E3%82%AB%E3%83%AB%E3%83%8A%E3%82%A4%E3%83%88%E3%83%97%E3%83%A9%E3%83%B3/[囧菌] シニカルナイトプラン.lrc
      - name: ツバサ (羽翼)
        artist: 若山詩音
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%84%E3%83%90%E3%82%B5/ツバサ.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%84%E3%83%90%E3%82%B5/ツバサ.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E3%83%84%E3%83%90%E3%82%B5/ツバサ.lrc
      - name: spiral
        artist: LONGMAN
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/spiral/spiral.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/spiral/spiral.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/spiral/spiral.lrc
      - name: Believe in you
        artist: nonoc
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Believe%20in%20you/Believe in you.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Believe%20in%20you/Believe in you.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/Believe%20in%20you/Believe in you.lrc
      - name: STYX HELIX
        artist: MYTH & ROID
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/STYX%20HELIX/STYX HELIX.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/STYX%20HELIX/STYX HELIX.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/STYX%20HELIX/STYX HELIX.lrc
      - name: 忘れてやらない
        artist: 結束バンド
        url: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%BF%98%E3%82%8C%E3%81%A6%E3%82%84%E3%82%89%E3%81%AA%E3%81%84/忘れてやらない.mp3
        cover: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%BF%98%E3%82%8C%E3%81%A6%E3%82%84%E3%82%89%E3%81%AA%E3%81%84/忘れてやらない.jpg
        lrc: https://pub-afcc413539f94c73959ec3d27d32e67d.r2.dev/%E5%BF%98%E3%82%8C%E3%81%A6%E3%82%84%E3%82%89%E3%81%AA%E3%81%84/忘れてやらない.lrc


      # .... you can add more audios here
  # Mermaid JS. Requires hexo-filter-mermaid-diagrams (npm i hexo-filter-mermaid-diagrams). See https://mermaid.js.org/
  mermaid:
    enable: true # enable mermaid or not
    version: "11.4.1" # default v11.4.1
# PLUGINS <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# PAGE TEMPLATES >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/page_templates
page_templates:
  # Friend Links page column number
  friends_column: 3
  # Tags page style
  tags_style: cloud # blur, cloud
# PAGE TEMPLATES <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end


# CDN >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/cdn
cdn:
  # Whether to enable CDN
  enable: false
  # CDN Provider
  provider: npmmirror # npmmirror, zstatic, cdnjs, jsdelivr, unpkg, custom
  # Custom CDN URL
  # format example: https://cdn.custom.com/hexo-theme-redefine/${version}/source/${path}
  # The ${path} must leads to the root of the "source" folder of the theme
  custom_url: 
# CDN <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end

# DEVELOPER MODE >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> start
# Docs: https://redefine-docs.ohevan.com/developer
developer:
  # Whether to enable developer mode (only for developers who want to modify the theme source code, not for ordinary users)
  enable: false
# DEVELOPER MODE <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< end