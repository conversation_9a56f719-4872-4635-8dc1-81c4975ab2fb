@require '../common/variables'

.page-title-header
  font-size 3.2rem
  line-height 1
  font-weight bold
  color var(--second-text-color)
  margin 6px 0 36px 0

.page-container
  position relative
  box-sizing border-box
  width 100%
  height auto
  background var(--background-color)

  +redefine-tablet()
    padding-right 0 !important

  .main-content-container
    padding-top $navbar-height
    position relative
    transition padding 0.2s ease

    .navbar-shrink &
      padding-top $navbar-shrink-height
      transition padding 0.2s ease

      +redefine-tablet()
        padding-top: ($navbar-shrink-height * 0.9)

      +redefine-mobile()
        padding-top: $navbar-shrink-height * 0.8

    +redefine-tablet()
      padding-top: $navbar-height * 0.9

    +redefine-mobile()
      padding-top: $navbar-height * 0.8

    .main-content-header
      position fixed
      top 0
      right 0
      width 100%
      height $navbar-height
      z-index $z-index-5
      box-sizing border-box
      transition-t('transform, padding-left, height', '0, 0, 0', '0.3, 0.2, 0.2', 'ease-out, linear, ease')

      &.hide
        transform translateY(-105%)

      .navbar-shrink &
        height $navbar-shrink-height

        +redefine-tablet()
          height: ($navbar-shrink-height * 0.9)

        +redefine-mobile()
          height: $navbar-shrink-height * 0.8

      +redefine-tablet()
        height: $navbar-height * 0.9
        padding-left 0 !important

      +redefine-mobile()
        height: $navbar-height * 0.8

    .main-content-body
      box-sizing border-box
      width 100%
      display flex
      justify-content center
      padding $spacing-unit 0

      +redefine-tablet()
        padding ($spacing-unit * 0.8) 0

      +redefine-mobile()
        border none !important
        padding-top 0

      .main-content
        position relative
        box-sizing border-box
        width $main-content-width
        max-width $content-max-width
        height 100%
        transition-t('max-width, width', '0, 0', '0.2, 0.2', 'ease, ease')

        +redefine-tablet()
          width $main-content-width-tablet

        +redefine-mobile()
          width $main-content-width-mobile

        &.has-toc
          max-width $has-toc-content-max-width !important

      .main-content-footer
        width 100%

  .post-tools
    position fixed
    top: $navbar-height + $spacing-unit
    right $spacing-unit
    transition-t('top', '0', '0.2', 'ease')

    .navbar-shrink &
      top: $navbar-shrink-height + $spacing-unit

      +redefine-tablet()
        top ($navbar-shrink-height * 0.9) + $spacing-unit

      +redefine-mobile()
        top: ($navbar-shrink-height * 0.8 + $spacing-unit)

    +redefine-tablet()
      top: ($navbar-height * 0.9 + $spacing-unit)
      right 10px
      transform scale(0.82)
      transform-origin right top

    +redefine-mobile()
      display none
      top: ($navbar-height * 0.8 + $spacing-unit)
      right 5px
      transform scale(0.72)

  .right-side-tools-container
    position fixed
    bottom 5%
    right $spacing-unit
    opacity 1
    transition opacity 0.2s ease

    &.hide
      opacity 0
      pointer-events none
      transition opacity 0.2s ease

    +redefine-mobile()
      right 12px
