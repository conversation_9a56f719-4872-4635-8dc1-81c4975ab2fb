<% if(
        theme.comment.system === 'gitalk'
        && theme.comment.config.gitalk.clientID
        && theme.comment.config.gitalk.clientSecret
        && theme.comment.config.gitalk.owner
        && theme.comment.config.gitalk.repo
) { %>
    <div id="gitalk-container"></div>
    <script <%= theme.global.single_page === true && 'data-swup-reload-script' %>
            src="https://cdnjs.cloudflare.com/ajax/libs/gitalk/1.8.0/gitalk.min.js"></script>
    <script <%= theme.global.single_page === true && 'data-swup-reload-script' %>>

        function loadGitalk() {
            let __gitalk__pathname = decodeURI(location.pathname);
            const __gitalk__pathnameLength = __gitalk__pathname.length;
            const __gitalk__pathnameMaxLength = 50;
            if (__gitalk__pathnameLength > __gitalk__pathnameMaxLength) {
                __gitalk__pathname = __gitalk__pathname.substring(0, __gitalk__pathnameMaxLength - 3) + '...';
            }

            try {
                Gitalk && new Gitalk({
                    clientID: '<%= theme.comment.config.gitalk.clientID %>',
                    clientSecret: '<%= theme.comment.config.gitalk.clientSecret %>',
                    repo: '<%= theme.comment.config.gitalk.repo %>',
                    owner: '<%= theme.comment.config.gitalk.owner %>',
                    admin: ['<%= theme.comment.config.gitalk.owner %>'],
                    id: __gitalk__pathname,
                    language: '<%= config.language %>',
                    proxy: '<%= theme.comment.config.gitalk.proxy || "https://github.com/login/oauth/access_token" %>'
                }).render('gitalk-container');

            } catch (e) {
                window.Gitalk = null;
            }
        }

        if ('<%= theme.global.single_page %>') {
            const loadGitalkTimeout = setTimeout(() => {
                loadGitalk();
                clearTimeout(loadGitalkTimeout);
            }, 1000);
        } else {
            window.addEventListener('DOMContentLoaded', loadGitalk);
        }
    </script>
<% } %>
