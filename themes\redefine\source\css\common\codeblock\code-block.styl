disable-user-select()
  -moz-user-select none
  -ms-user-select none
  -webkit-user-select none
  user-select none

.code-container
  position relative

.code-container:hover .copy-button, .code-container .copy-button:focus, .code-container:hover .fold-button, .code-container .fold-button:focus
  opacity 1

.fold-button
  cursor pointer
  border-radius 0 0 0 0
  display inline-block
  font-weight bold
  line-height 1.8
  opacity 0
  outline 0
  padding 6px 15px
  position absolute
  vertical-align middle
  white-space nowrap
  font-size 1rem
  color var(--default-text-color)
  disable-user-select()
  transition-t('opacity', '0', '0.2', 'ease-in-out')
  background var(--scond-background-color)
  border 0
  right 40px
  top 0

.copy-button
  cursor pointer
  border-radius 0 $redefine-border-radius-large 0 0
  display inline-block
  font-weight bold
  line-height 1.8
  opacity 0
  outline 0
  padding 6px 15px
  position absolute
  vertical-align middle
  white-space nowrap
  font-size 1rem
  color var(--default-text-color)
  disable-user-select()
  transition-t('opacity', '0', '0.2', 'ease-in-out')
  background var(--scond-background-color)
  border 0
  right 0
  top 0

if (hexo-config('articles.code_block.style') == 'mac')
  .code-container
    background var(--third-background-color)
    border-radius $redefine-border-radius-large
    box-shadow var(--redefine-box-shadow-flat)
    padding-top 40px
    margin-top 10px
    margin-bottom 1.2rem
    position relative

    figcaption
      font-weight bold
      font-size 16px
      position absolute
      top 12px
      left 50%
      transform translateX(-50%)

      a
        margin-left 10px

    &::before
      position absolute
      content attr(data-rel)
      line-height 38px
      border-radius $redefine-border-radius-full
      box-shadow none
      height 12px
      left 12px
      position absolute
      width auto
      margin-left 80px
      top 0
      color var(--default-text-color)
      font-weight bold
      font-size 16px

    .folded
      height 0 !important

      figure
        height 0 !important
        width 0 !important
        overflow hidden !important

    &::after
      content ' '
      position absolute
      -webkit-border-radius $redefine-border-radius-full
      border-radius $redefine-border-radius-full
      background #fc625d
      width 12px
      height 12px
      top 0
      left 20px
      margin-top 13px
      -webkit-box-shadow 20px 0px #fdbc40, 40px 0px #35cd4b
      box-shadow 20px 0px #fdbc40, 40px 0px #35cd4b
      z-index 3
else
  .code-container
    background var(--highlight-background)
    border-radius $redefine-border-radius-large
    padding-top 40px
    padding-left 5px
    margin-top 10px
    margin-bottom 1.2rem
    position relative
    box-shadow var(--redefine-box-shadow-flat)

    figcaption
      font-weight bold
      font-size 16px
      position absolute
      top 16px
      left 50%
      transform translateX(-50%)

      a
        margin-left 10px

    .folded
      height 0 !important

      figure
        height 0 !important
        width 0 !important
        overflow hidden !important

    &::before
      position absolute
      content attr(data-rel)
      line-height 42px
      border-radius $redefine-border-radius-large
      box-shadow none
      height 12px
      left 4px
      position absolute
      width auto
      margin-left 10px
      top 2px
      color var(--default-text-color)
      font-weight bold
      font-size 16px
