{"version": 3, "file": "Swup.umd.js", "sources": ["../src/helpers/classify.ts", "../src/helpers/getCurrentUrl.ts", "../src/helpers/createHistoryRecord.ts", "../src/helpers/updateHistoryRecord.ts", "../node_modules/delegate-it/delegate.js", "../src/helpers/delegateEvent.ts", "../src/helpers/Location.ts", "../src/modules/Cache.ts", "../src/utils/index.ts", "../src/modules/Classes.ts", "../src/modules/Visit.ts", "../src/modules/Hooks.ts", "../src/modules/getAnchorElement.ts", "../src/modules/awaitAnimations.ts", "../src/modules/navigate.ts", "../src/modules/fetchPage.ts", "../src/modules/animatePageOut.ts", "../src/modules/replaceContent.ts", "../src/modules/scrollToContent.ts", "../src/modules/animatePageIn.ts", "../src/modules/renderPage.ts", "../src/modules/plugins.ts", "../src/modules/resolveUrl.ts", "../src/Swup.ts"], "sourcesContent": ["/** Turn a string into a slug by lowercasing and replacing whitespace. */\nexport const classify = (text: string, fallback?: string): string => {\n\tconst output = String(text)\n\t\t.toLowerCase()\n\t\t// .normalize('NFD') // split an accented letter in the base letter and the acent\n\t\t// .replace(/[\\u0300-\\u036f]/g, '') // remove all previously split accents\n\t\t.replace(/[\\s/_.]+/g, '-') // replace spaces and _./ with '-'\n\t\t.replace(/[^\\w-]+/g, '') // remove all non-word chars\n\t\t.replace(/--+/g, '-') // replace repeating '-' with single '-'\n\t\t.replace(/^-+|-+$/g, ''); // trim '-' from edges\n\treturn output || fallback || '';\n};\n", "/** Get the current page URL: path name + query params. Optionally including hash. */\nexport const getCurrentUrl = ({ hash }: { hash?: boolean } = {}): string => {\n\treturn location.pathname + location.search + (hash ? location.hash : '');\n};\n", "import { getCurrentUrl } from './getCurrentUrl.js';\n\nexport interface HistoryState {\n\turl: string;\n\tsource: 'swup';\n\trandom: number;\n\tindex?: number;\n\t[key: string]: unknown;\n}\n\n/** Create a new history record with a custom swup identifier. */\nexport const createHistoryRecord = (\n\turl: string,\n\tcustomData: Record<string, unknown> = {}\n): void => {\n\turl = url || getCurrentUrl({ hash: true });\n\tconst data: HistoryState = {\n\t\turl,\n\t\trandom: Math.random(),\n\t\tsource: 'swup',\n\t\t...customData\n\t};\n\thistory.pushState(data, '', url);\n};\n", "import { HistoryState } from './createHistoryRecord.js';\nimport { getCurrentUrl } from './getCurrentUrl.js';\n\n/** Update the current history record with a custom swup identifier. */\nexport const updateHistoryRecord = (\n\turl: string | null = null,\n\tcustomData: Record<string, unknown> = {}\n): void => {\n\turl = url || getCurrentUrl({ hash: true });\n\tconst state = (history.state as HistoryState) || {};\n\tconst data: HistoryState = {\n\t\t...state,\n\t\turl,\n\t\trandom: Math.random(),\n\t\tsource: 'swup',\n\t\t...customData\n\t};\n\thistory.replaceState(data, '', url);\n};\n", "/** Keeps track of raw listeners added to the base elements to avoid duplication */\nconst ledger = new WeakMap();\nfunction editLedger(wanted, baseElement, callback, setup) {\n    if (!wanted && !ledger.has(baseElement)) {\n        return false;\n    }\n    const elementMap = ledger.get(baseElement)\n        ?? new WeakMap();\n    ledger.set(baseElement, elementMap);\n    const setups = elementMap.get(callback) ?? new Set();\n    elementMap.set(callback, setups);\n    const existed = setups.has(setup);\n    if (wanted) {\n        setups.add(setup);\n    }\n    else {\n        setups.delete(setup);\n    }\n    return existed && wanted;\n}\nfunction safeClosest(event, selector) {\n    let target = event.target;\n    if (target instanceof Text) {\n        target = target.parentElement;\n    }\n    if (target instanceof Element && event.currentTarget instanceof Element) {\n        // `.closest()` may match ancestors of `currentTarget` but we only need its children\n        const closest = target.closest(selector);\n        if (closest && event.currentTarget.contains(closest)) {\n            return closest;\n        }\n    }\n}\n// This type isn't exported as a declaration, so it needs to be duplicated above\nfunction delegate(selector, type, callback, options = {}) {\n    const { signal, base = document } = options;\n    if (signal?.aborted) {\n        return;\n    }\n    // Don't pass `once` to `addEventListener` because it needs to be handled in `delegate-it`\n    const { once, ...nativeListenerOptions } = options;\n    // `document` should never be the base, it's just an easy way to define \"global event listeners\"\n    const baseElement = base instanceof Document ? base.documentElement : base;\n    // Handle the regular Element usage\n    const capture = Boolean(typeof options === 'object' ? options.capture : options);\n    const listenerFn = (event) => {\n        const delegateTarget = safeClosest(event, selector);\n        if (delegateTarget) {\n            const delegateEvent = Object.assign(event, { delegateTarget });\n            callback.call(baseElement, delegateEvent);\n            if (once) {\n                baseElement.removeEventListener(type, listenerFn, nativeListenerOptions);\n                editLedger(false, baseElement, callback, setup);\n            }\n        }\n    };\n    const setup = JSON.stringify({ selector, type, capture });\n    const isAlreadyListening = editLedger(true, baseElement, callback, setup);\n    if (!isAlreadyListening) {\n        baseElement.addEventListener(type, listenerFn, nativeListenerOptions);\n    }\n    signal?.addEventListener('abort', () => {\n        editLedger(false, baseElement, callback, setup);\n    });\n}\nexport default delegate;\n", "import delegate, { DelegateEventHandler, DelegateOptions, EventType } from 'delegate-it';\nimport { ParseSelector } from 'typed-query-selector/parser.js';\n\nexport type DelegateEventUnsubscribe = {\n\tdestroy: () => void;\n};\n\n/** Register a delegated event listener. */\nexport const delegateEvent = <\n\tSelector extends string,\n\tTElement extends Element = ParseSelector<Selector, HTMLElement>,\n\tTEvent extends EventType = EventType\n>(\n\tselector: Selector,\n\ttype: TEvent,\n\tcallback: DelegateEventHandler<GlobalEventHandlersEventMap[TEvent], TElement>,\n\toptions?: DelegateOptions\n): DelegateEventUnsubscribe => {\n\tconst controller = new AbortController();\n\toptions = { ...options, signal: controller.signal };\n\tdelegate<Selector, TElement, TEvent>(selector, type, callback, options);\n\treturn { destroy: () => controller.abort() };\n};\n", "/**\n * A helper for creating a Location from either an element\n * or a URL object/string\n *\n */\nexport class Location extends URL {\n\tconstructor(url: URL | string, base: string = document.baseURI) {\n\t\tsuper(url.toString(), base);\n\t}\n\n\t/**\n\t * The full local path including query params.\n\t */\n\tget url(): string {\n\t\treturn this.pathname + this.search;\n\t}\n\n\t/**\n\t * Instantiate a Location from an element's href attribute\n\t * @param el\n\t * @returns new Location instance\n\t */\n\tstatic fromElement(el: Element): Location {\n\t\tconst href = el.getAttribute('href') || el.getAttribute('xlink:href') || '';\n\t\treturn new Location(href);\n\t}\n\n\t/**\n\t * Instantiate a Location from a URL object or string\n\t * @param url\n\t * @returns new Location instance\n\t */\n\tstatic fromUrl(url: URL | string): Location {\n\t\treturn new Location(url);\n\t}\n}\n", "import Swup from '../Swup.js';\nimport { Location } from '../helpers.js';\nimport { PageData } from './fetchPage.js';\n\nexport interface CacheData extends PageData {}\n\n/**\n * In-memory page cache.\n */\nexport class Cache {\n\t/** Swup instance this cache belongs to */\n\tprotected swup: Swup;\n\n\t/** Cached pages, indexed by URL */\n\tprotected pages: Map<string, CacheData> = new Map();\n\n\tconstructor(swup: Swup) {\n\t\tthis.swup = swup;\n\t}\n\n\t/** Number of cached pages in memory. */\n\tget size(): number {\n\t\treturn this.pages.size;\n\t}\n\n\t/** All cached pages. */\n\tget all() {\n\t\tconst copy = new Map();\n\t\tthis.pages.forEach((page, key) => {\n\t\t\tcopy.set(key, { ...page });\n\t\t});\n\t\treturn copy;\n\t}\n\n\t/** Check if the given URL has been cached. */\n\thas(url: string): boolean {\n\t\treturn this.pages.has(this.resolve(url));\n\t}\n\n\t/** Return a shallow copy of the cached page object if available. */\n\tget(url: string): CacheData | undefined {\n\t\tconst result = this.pages.get(this.resolve(url));\n\t\tif (!result) return result;\n\t\treturn { ...result };\n\t}\n\n\t/** Create a cache record for the specified URL. */\n\tset(url: string, page: CacheData) {\n\t\turl = this.resolve(url);\n\t\tpage = { ...page, url };\n\t\tthis.pages.set(url, page);\n\t\tthis.swup.hooks.callSync('cache:set', { page });\n\t}\n\n\t/** Update a cache record, overwriting or adding custom data. */\n\tupdate(url: string, payload: object) {\n\t\turl = this.resolve(url);\n\t\tconst page = { ...this.get(url), ...payload, url } as CacheData;\n\t\tthis.pages.set(url, page);\n\t}\n\n\t/** Delete a cache record. */\n\tdelete(url: string): void {\n\t\tthis.pages.delete(this.resolve(url));\n\t}\n\n\t/** Empty the cache. */\n\tclear(): void {\n\t\tthis.pages.clear();\n\t\tthis.swup.hooks.callSync('cache:clear', undefined);\n\t}\n\n\t/** Remove all cache entries that return true for a given predicate function.  */\n\tprune(predicate: (url: string, page: CacheData) => boolean): void {\n\t\tthis.pages.forEach((page, url) => {\n\t\t\tif (predicate(url, page)) {\n\t\t\t\tthis.delete(url);\n\t\t\t}\n\t\t});\n\t}\n\n\t/** Resolve URLs by making them local and letting swup resolve them. */\n\tprotected resolve(urlToResolve: string): string {\n\t\tconst { url } = Location.fromUrl(urlToResolve);\n\t\treturn this.swup.resolveUrl(url);\n\t}\n}\n", "/** Find an element by selector. */\nexport const query = (selector: string, context: Document | Element = document) => {\n\treturn context.querySelector<HTMLElement>(selector);\n};\n\n/** Find a set of elements by selector. */\nexport const queryAll = (\n\tselector: string,\n\tcontext: Document | Element = document\n): HTMLElement[] => {\n\treturn Array.from(context.querySelectorAll(selector));\n};\n\n/** Return a Promise that resolves after the next event loop. */\nexport const nextTick = (): Promise<void> => {\n\treturn new Promise((resolve) => {\n\t\trequestAnimationFrame(() => {\n\t\t\trequestAnimationFrame(() => {\n\t\t\t\tresolve();\n\t\t\t});\n\t\t});\n\t});\n};\n\n/** Check if an object is a Promise or a Thenable */\nexport function isPromise<T>(obj: unknown): obj is PromiseLike<T> {\n\treturn (\n\t\t!!obj &&\n\t\t(typeof obj === 'object' || typeof obj === 'function') &&\n\t\ttypeof (obj as Record<string, unknown>).then === 'function'\n\t);\n}\n\n/** Call a function as a Promise. Resolves with the returned Promsise or immediately. */\n// eslint-disable-next-line @typescript-eslint/ban-types, @typescript-eslint/no-explicit-any\nexport function runAsPromise(func: Function, args: unknown[] = []): Promise<unknown> {\n\treturn new Promise((resolve, reject) => {\n\t\tconst result: unknown = func(...args);\n\t\tif (isPromise(result)) {\n\t\t\tresult.then(resolve, reject);\n\t\t} else {\n\t\t\tresolve(result);\n\t\t}\n\t});\n}\n\n/**\n * Force a layout reflow, e.g. after adding classnames\n * @returns The offset height, just here so it doesn't get optimized away by the JS engine\n * @see https://stackoverflow.com/a/21665117/3759615\n */\nexport function forceReflow(element?: HTMLElement) {\n\telement = element || document.body;\n\treturn element?.offsetHeight;\n}\n\n/** Escape a string with special chars to not break CSS selectors. */\nexport const escapeCssIdentifier = (ident: string) => {\n\t// @ts-ignore this is for support check, so it's correct that TS complains\n\tif (window.CSS && window.CSS.escape) {\n\t\treturn CSS.escape(ident);\n\t}\n\treturn ident;\n};\n\n/** Fix for Chrome below v61 formatting CSS floats with comma in some locales. */\nexport const toMs = (s: string) => {\n\treturn Number(s.slice(0, -1).replace(',', '.')) * 1000;\n};\n", "import Swup from '../Swup.js';\nimport { queryAll } from '../utils.js';\n\nexport class Classes {\n\tprotected swup: Swup;\n\tprotected swupClasses = ['to-', 'is-changing', 'is-rendering', 'is-popstate', 'is-animating'];\n\n\tconstructor(swup: Swup) {\n\t\tthis.swup = swup;\n\t}\n\n\tprotected get selectors(): string[] {\n\t\tconst { scope } = this.swup.visit.animation;\n\t\tif (scope === 'containers') return this.swup.visit.containers;\n\t\tif (scope === 'html') return ['html'];\n\t\tif (Array.isArray(scope)) return scope;\n\t\treturn [];\n\t}\n\n\tprotected get selector(): string {\n\t\treturn this.selectors.join(',');\n\t}\n\n\tprotected get targets(): HTMLElement[] {\n\t\tif (!this.selector.trim()) return [];\n\t\treturn queryAll(this.selector);\n\t}\n\n\tadd(...classes: string[]): void {\n\t\tthis.targets.forEach((target) => target.classList.add(...classes));\n\t}\n\n\tremove(...classes: string[]): void {\n\t\tthis.targets.forEach((target) => target.classList.remove(...classes));\n\t}\n\n\tclear(): void {\n\t\tthis.targets.forEach((target) => {\n\t\t\tconst remove = target.className.split(' ').filter((c) => this.isSwupClass(c));\n\t\t\ttarget.classList.remove(...remove);\n\t\t});\n\t}\n\n\tprotected isSwupClass(className: string): boolean {\n\t\treturn this.swupClasses.some((c) => className.startsWith(c));\n\t}\n}\n", "import Swup, { Options } from '../Swup.js';\nimport { HistoryAction, HistoryDirection } from './navigate.js';\n\n/** An object holding details about the current visit. */\nexport interface Visit {\n\t/** The previous page, about to leave */\n\tfrom: VisitFrom;\n\t/** The next page, about to enter */\n\tto: VisitTo;\n\t/** The content containers, about to be replaced */\n\tcontainers: Options['containers'];\n\t/** Information about animated page transitions */\n\tanimation: VisitAnimation;\n\t/** What triggered this visit */\n\ttrigger: VisitTrigger;\n\t/** Cache behavior for this visit */\n\tcache: VisitCache;\n\t/** Browser history behavior on this visit */\n\thistory: VisitHistory;\n\t/** Scroll behavior on this visit */\n\tscroll: VisitScroll;\n}\n\nexport interface VisitFrom {\n\t/** The URL of the previous page */\n\turl: string;\n}\n\nexport interface VisitTo {\n\t/** The URL of the next page */\n\turl: string;\n\t/** The hash of the next page */\n\thash?: string;\n\t/** The HTML content of the next page */\n\thtml?: string;\n}\n\nexport interface VisitAnimation {\n\t/** Whether this visit is animated. Default: `true` */\n\tanimate: boolean;\n\t/** Whether to wait for the next page to load before starting the animation. Default: `false` */\n\twait: boolean;\n\t/** Name of a custom animation to run. */\n\tname?: string;\n\t/** Elements on which to add animation classes. Default: `html` element */\n\tscope: 'html' | 'containers' | string[];\n\t/** Selector for detecting animation timing. Default: `[class*=\"transition-\"]` */\n\tselector: Options['animationSelector'];\n}\n\nexport interface VisitScroll {\n\t/** Whether to reset the scroll position after the visit. Default: `true` */\n\treset: boolean;\n\t/** Anchor element to scroll to on the next page. */\n\ttarget?: string | false;\n}\n\nexport interface VisitTrigger {\n\t/** DOM element that triggered this visit. */\n\tel?: Element;\n\t/** DOM event that triggered this visit. */\n\tevent?: Event;\n}\n\nexport interface VisitCache {\n\t/** Whether this visit will try to load the requested page from cache. */\n\tread: boolean;\n\t/** Whether this visit will save the loaded page in cache. */\n\twrite: boolean;\n}\n\nexport interface VisitHistory {\n\t/** History action to perform: `push` for creating a new history entry, `replace` for replacing the current entry. Default: `push` */\n\taction: HistoryAction;\n\t/** Whether this visit was triggered by a browser history navigation. */\n\tpopstate: boolean;\n\t/** The direction of travel in case of a browser history navigation: backward or forward. */\n\tdirection: HistoryDirection | undefined;\n}\n\nexport interface VisitInitOptions {\n\tto: string;\n\tfrom?: string;\n\thash?: string;\n\tel?: Element;\n\tevent?: Event;\n}\n\n/** Create a new visit object. */\nexport function createVisit(\n\tthis: Swup,\n\t{ to, from = this.currentPageUrl, hash, el, event }: VisitInitOptions\n): Visit {\n\treturn {\n\t\tfrom: { url: from },\n\t\tto: { url: to, hash },\n\t\tcontainers: this.options.containers,\n\t\tanimation: {\n\t\t\tanimate: true,\n\t\t\twait: false,\n\t\t\tname: undefined,\n\t\t\tscope: this.options.animationScope,\n\t\t\tselector: this.options.animationSelector\n\t\t},\n\t\ttrigger: {\n\t\t\tel,\n\t\t\tevent\n\t\t},\n\t\tcache: {\n\t\t\tread: this.options.cache,\n\t\t\twrite: this.options.cache\n\t\t},\n\t\thistory: {\n\t\t\taction: 'push',\n\t\t\tpopstate: false,\n\t\t\tdirection: undefined\n\t\t},\n\t\tscroll: {\n\t\t\treset: true,\n\t\t\ttarget: undefined\n\t\t}\n\t};\n}\n", "import { DelegateEvent } from 'delegate-it';\n\nimport Swup from '../Swup.js';\nimport { isPromise, runAsPromise } from '../utils.js';\nimport { Visit } from './Visit.js';\nimport { FetchOptions, PageData } from './fetchPage.js';\n\nexport interface HookDefinitions {\n\t'animation:out:start': undefined;\n\t'animation:out:await': { skip: boolean };\n\t'animation:out:end': undefined;\n\t'animation:in:start': undefined;\n\t'animation:in:await': { skip: boolean };\n\t'animation:in:end': undefined;\n\t'animation:skip': undefined;\n\t'cache:clear': undefined;\n\t'cache:set': { page: PageData };\n\t'content:replace': { page: PageData };\n\t'content:scroll': undefined;\n\t'enable': undefined;\n\t'disable': undefined;\n\t'fetch:request': { url: string; options: FetchOptions };\n\t'fetch:error': { url: string; status: number; response: Response };\n\t'history:popstate': { event: PopStateEvent };\n\t'link:click': { el: HTMLAnchorElement; event: DelegateEvent<MouseEvent> };\n\t'link:self': undefined;\n\t'link:anchor': { hash: string };\n\t'link:newtab': { href: string };\n\t'page:load': { page?: PageData; cache?: boolean; options: FetchOptions };\n\t'page:view': { url: string; title: string };\n\t'scroll:top': { options: ScrollIntoViewOptions };\n\t'scroll:anchor': { hash: string; options: ScrollIntoViewOptions };\n\t'visit:start': undefined;\n\t'visit:end': undefined;\n}\n\nexport interface HookReturnValues {\n\t'content:scroll': Promise<boolean>;\n\t'fetch:request': Promise<Response>;\n\t'page:load': Promise<PageData>;\n\t'scroll:top': boolean;\n\t'scroll:anchor': boolean;\n}\n\nexport type HookArguments<T extends HookName> = HookDefinitions[T];\n\nexport type HookName = keyof HookDefinitions;\n\n/** A generic hook handler. */\nexport type Handler<T extends HookName> = (\n\t/** Context about the current visit. */\n\tvisit: Visit,\n\t/** Local arguments passed into the handler. */\n\targs: HookArguments<T>\n) => Promise<unknown> | unknown;\n\n/** A default hook handler with an expected return type. */\nexport type DefaultHandler<T extends HookName> = (\n\t/** Context about the current visit. */\n\tvisit: Visit,\n\t/** Local arguments passed into the handler. */\n\targs: HookArguments<T>,\n\t/** Default handler to be executed. Available if replacing an internal hook handler. */\n\tdefaultHandler?: DefaultHandler<T>\n) => T extends keyof HookReturnValues ? HookReturnValues[T] : Promise<unknown> | unknown;\n\nexport type Handlers = {\n\t[K in HookName]: Handler<K>[];\n};\n\n/** Unregister a previously registered hook handler. */\nexport type HookUnregister = () => void;\n\n/** Define when and how a hook handler is executed. */\nexport type HookOptions = {\n\t/** Execute the hook once, then remove the handler */\n\tonce?: boolean;\n\t/** Execute the hook before the internal default handler */\n\tbefore?: boolean;\n\t/** Set a priority for when to execute this hook. Lower numbers execute first. Default: `0` */\n\tpriority?: number;\n\t/** Replace the internal default handler with this hook handler */\n\treplace?: boolean;\n};\n\nexport type HookRegistration<\n\tT extends HookName,\n\tH extends Handler<T> | DefaultHandler<T> = Handler<T>\n> = {\n\tid: number;\n\thook: T;\n\thandler: H;\n\tdefaultHandler?: DefaultHandler<T>;\n} & HookOptions;\n\ntype HookLedger<T extends HookName> = Map<Handler<T>, HookRegistration<T>>;\n\ninterface HookRegistry extends Map<HookName, HookLedger<HookName>> {\n\tget<K extends HookName>(key: K): HookLedger<K> | undefined;\n\tset<K extends HookName>(key: K, value: HookLedger<K>): this;\n}\n\n/**\n * Hook registry.\n *\n * Create, trigger and handle hooks.\n *\n */\nexport class Hooks {\n\t/** Swup instance this registry belongs to */\n\tprotected swup: Swup;\n\n\t/** Map of all registered hook handlers. */\n\tprotected registry: HookRegistry = new Map();\n\n\t// Can we deduplicate this somehow? Or make it error when not in sync with HookDefinitions?\n\t// https://stackoverflow.com/questions/53387838/how-to-ensure-an-arrays-values-the-keys-of-a-typescript-interface/53395649\n\tprotected readonly hooks: HookName[] = [\n\t\t'animation:out:start',\n\t\t'animation:out:await',\n\t\t'animation:out:end',\n\t\t'animation:in:start',\n\t\t'animation:in:await',\n\t\t'animation:in:end',\n\t\t'animation:skip',\n\t\t'cache:clear',\n\t\t'cache:set',\n\t\t'content:replace',\n\t\t'content:scroll',\n\t\t'enable',\n\t\t'disable',\n\t\t'fetch:request',\n\t\t'fetch:error',\n\t\t'history:popstate',\n\t\t'link:click',\n\t\t'link:self',\n\t\t'link:anchor',\n\t\t'link:newtab',\n\t\t'page:load',\n\t\t'page:view',\n\t\t'scroll:top',\n\t\t'scroll:anchor',\n\t\t'visit:start',\n\t\t'visit:end'\n\t];\n\n\tconstructor(swup: Swup) {\n\t\tthis.swup = swup;\n\t\tthis.init();\n\t}\n\n\t/**\n\t * Create ledgers for all core hooks.\n\t */\n\tprotected init() {\n\t\tthis.hooks.forEach((hook) => this.create(hook));\n\t}\n\n\t/**\n\t * Create a new hook type.\n\t */\n\tcreate(hook: string) {\n\t\tif (!this.registry.has(hook as HookName)) {\n\t\t\tthis.registry.set(hook as HookName, new Map());\n\t\t}\n\t}\n\n\t/**\n\t * Check if a hook type exists.\n\t */\n\texists(hook: HookName): boolean {\n\t\treturn this.registry.has(hook);\n\t}\n\n\t/**\n\t * Get the ledger with all registrations for a hook.\n\t */\n\tprotected get<T extends HookName>(hook: T): HookLedger<T> | undefined {\n\t\tconst ledger = this.registry.get(hook);\n\t\tif (ledger) {\n\t\t\treturn ledger;\n\t\t}\n\t\tconsole.error(`Unknown hook '${hook}'`);\n\t}\n\n\t/**\n\t * Remove all handlers of all hooks.\n\t */\n\tclear() {\n\t\tthis.registry.forEach((ledger) => ledger.clear());\n\t}\n\n\t/**\n\t * Register a new hook handler.\n\t * @param hook Name of the hook to listen for\n\t * @param handler The handler function to execute\n\t * @param options Object to specify how and when the handler is executed\n\t *                Available options:\n\t *                - `once`: Only execute the handler once\n\t *                - `before`: Execute the handler before the default handler\n\t *                - `priority`: Specify the order in which the handlers are executed\n\t *                - `replace`: Replace the default handler with this handler\n\t * @returns A function to unregister the handler\n\t */\n\n\t// Overload: replacing default handler\n\ton<T extends HookName, O extends HookOptions>(hook: T, handler: DefaultHandler<T>, options: O & { replace: true }): HookUnregister; // prettier-ignore\n\t// Overload: passed in handler options\n\ton<T extends HookName, O extends HookOptions>(hook: T, handler: Handler<T>, options: O): HookUnregister; // prettier-ignore\n\t// Overload: no handler options\n\ton<T extends HookName>(hook: T, handler: Handler<T>): HookUnregister; // prettier-ignore\n\t// Implementation\n\ton<T extends HookName, O extends HookOptions>(\n\t\thook: T,\n\t\thandler: O['replace'] extends true ? DefaultHandler<T> : Handler<T>,\n\t\toptions: Partial<O> = {}\n\t): HookUnregister {\n\t\tconst ledger = this.get(hook);\n\t\tif (!ledger) {\n\t\t\tconsole.warn(`Hook '${hook}' not found.`);\n\t\t\treturn () => {};\n\t\t}\n\n\t\tconst id = ledger.size + 1;\n\t\tconst registration: HookRegistration<T> = { ...options, id, hook, handler };\n\t\tledger.set(handler, registration);\n\n\t\treturn () => this.off(hook, handler);\n\t}\n\n\t/**\n\t * Register a new hook handler to run before the default handler.\n\t * Shortcut for `hooks.on(hook, handler, { before: true })`.\n\t * @param hook Name of the hook to listen for\n\t * @param handler The handler function to execute\n\t * @param options Any other event options (see `hooks.on()` for details)\n\t * @returns A function to unregister the handler\n\t * @see on\n\t */\n\t// Overload: passed in handler options\n\tbefore<T extends HookName>(hook: T, handler: Handler<T>, options: HookOptions): HookUnregister;\n\t// Overload: no handler options\n\tbefore<T extends HookName>(hook: T, handler: Handler<T>): HookUnregister;\n\t// Implementation\n\tbefore<T extends HookName>(\n\t\thook: T,\n\t\thandler: Handler<T>,\n\t\toptions: HookOptions = {}\n\t): HookUnregister {\n\t\treturn this.on(hook, handler, { ...options, before: true });\n\t}\n\n\t/**\n\t * Register a new hook handler to replace the default handler.\n\t * Shortcut for `hooks.on(hook, handler, { replace: true })`.\n\t * @param hook Name of the hook to listen for\n\t * @param handler The handler function to execute instead of the default handler\n\t * @param options Any other event options (see `hooks.on()` for details)\n\t * @returns A function to unregister the handler\n\t * @see on\n\t */\n\t// Overload: passed in handler options\n\treplace<T extends HookName>(hook: T, handler: DefaultHandler<T>, options: HookOptions): HookUnregister; // prettier-ignore\n\t// Overload: no handler options\n\treplace<T extends HookName>(hook: T, handler: DefaultHandler<T>): HookUnregister; // prettier-ignore\n\t// Implementation\n\treplace<T extends HookName>(\n\t\thook: T,\n\t\thandler: DefaultHandler<T>,\n\t\toptions: HookOptions = {}\n\t): HookUnregister {\n\t\treturn this.on(hook, handler, { ...options, replace: true });\n\t}\n\n\t/**\n\t * Register a new hook handler to run once.\n\t * Shortcut for `hooks.on(hook, handler, { once: true })`.\n\t * @param hook Name of the hook to listen for\n\t * @param handler The handler function to execute\n\t * @param options Any other event options (see `hooks.on()` for details)\n\t * @see on\n\t */\n\t// Overload: passed in handler options\n\tonce<T extends HookName>(hook: T, handler: Handler<T>, options: HookOptions): HookUnregister;\n\t// Overload: no handler options\n\tonce<T extends HookName>(hook: T, handler: Handler<T>): HookUnregister;\n\t// Implementation\n\tonce<T extends HookName>(\n\t\thook: T,\n\t\thandler: Handler<T>,\n\t\toptions: HookOptions = {}\n\t): HookUnregister {\n\t\treturn this.on(hook, handler, { ...options, once: true });\n\t}\n\n\t/**\n\t * Unregister a hook handler.\n\t * @param hook Name of the hook the handler is registered for\n\t * @param handler The handler function that was registered.\n\t *                If omitted, all handlers for the hook will be removed.\n\t */\n\t// Overload: unregister a specific handler\n\toff<T extends HookName>(hook: T, handler: Handler<T> | DefaultHandler<T>): void;\n\t// Overload: unregister all handlers\n\toff<T extends HookName>(hook: T): void;\n\t// Implementation\n\toff<T extends HookName>(hook: T, handler?: Handler<T> | DefaultHandler<T>): void {\n\t\tconst ledger = this.get(hook);\n\t\tif (ledger && handler) {\n\t\t\tconst deleted = ledger.delete(handler);\n\t\t\tif (!deleted) {\n\t\t\t\tconsole.warn(`Handler for hook '${hook}' not found.`);\n\t\t\t}\n\t\t} else if (ledger) {\n\t\t\tledger.clear();\n\t\t}\n\t}\n\n\t/**\n\t * Trigger a hook asynchronously, executing its default handler and all registered handlers.\n\t * Will execute all handlers in order and `await` any `Promise`s they return.\n\t * @param hook Name of the hook to trigger\n\t * @param args Arguments to pass to the handler\n\t * @param defaultHandler A default implementation of this hook to execute\n\t * @returns The resolved return value of the executed default handler\n\t */\n\tasync call<T extends HookName>(\n\t\thook: T,\n\t\targs: HookArguments<T>,\n\t\tdefaultHandler?: DefaultHandler<T>\n\t): Promise<Awaited<ReturnType<DefaultHandler<T>>>> {\n\t\tconst { before, handler, after } = this.getHandlers(hook, defaultHandler);\n\t\tawait this.run(before, args);\n\t\tconst [result] = await this.run(handler, args);\n\t\tawait this.run(after, args);\n\t\tthis.dispatchDomEvent(hook, args);\n\t\treturn result;\n\t}\n\n\t/**\n\t * Trigger a hook synchronously, executing its default handler and all registered handlers.\n\t * Will execute all handlers in order, but will **not** `await` any `Promise`s they return.\n\t * @param hook Name of the hook to trigger\n\t * @param args Arguments to pass to the handler\n\t * @param defaultHandler A default implementation of this hook to execute\n\t * @returns The (possibly unresolved) return value of the executed default handler\n\t */\n\tcallSync<T extends HookName>(\n\t\thook: T,\n\t\targs: HookArguments<T>,\n\t\tdefaultHandler?: DefaultHandler<T>\n\t): ReturnType<DefaultHandler<T>> {\n\t\tconst { before, handler, after } = this.getHandlers(hook, defaultHandler);\n\t\tthis.runSync(before, args);\n\t\tconst [result] = this.runSync(handler, args);\n\t\tthis.runSync(after, args);\n\t\tthis.dispatchDomEvent(hook, args);\n\t\treturn result;\n\t}\n\n\t/**\n\t * Execute the handlers for a hook, in order, as `Promise`s that will be `await`ed.\n\t * @param registrations The registrations (handler + options) to execute\n\t * @param args Arguments to pass to the handler\n\t */\n\n\t// Overload: running DefaultHandler: expect DefaultHandler return type\n\tprotected async run<T extends HookName>(registrations: HookRegistration<T, DefaultHandler<T>>[], args: HookArguments<T>): Promise<Awaited<ReturnType<DefaultHandler<T>>>[]>; // prettier-ignore\n\t// Overload:  running user handler: expect no specific type\n\tprotected async run<T extends HookName>(registrations: HookRegistration<T>[], args: HookArguments<T>): Promise<unknown[]>; // prettier-ignore\n\t// Implementation\n\tprotected async run<T extends HookName, R extends HookRegistration<T>[]>(\n\t\tregistrations: R,\n\t\targs: HookArguments<T>\n\t): Promise<Awaited<ReturnType<DefaultHandler<T>>> | unknown[]> {\n\t\tconst results = [];\n\t\tfor (const { hook, handler, defaultHandler, once } of registrations) {\n\t\t\tconst result = await runAsPromise(handler, [this.swup.visit, args, defaultHandler]);\n\t\t\tresults.push(result);\n\t\t\tif (once) {\n\t\t\t\tthis.off(hook, handler);\n\t\t\t}\n\t\t}\n\t\treturn results;\n\t}\n\n\t/**\n\t * Execute the handlers for a hook, in order, without `await`ing any returned `Promise`s.\n\t * @param registrations The registrations (handler + options) to execute\n\t * @param args Arguments to pass to the handler\n\t */\n\n\t// Overload: running DefaultHandler: expect DefaultHandler return type\n\tprotected runSync<T extends HookName>(registrations: HookRegistration<T, DefaultHandler<T>>[], args: HookArguments<T> ): ReturnType<DefaultHandler<T>>[]; // prettier-ignore\n\t// Overload: running user handler: expect no specific type\n\tprotected runSync<T extends HookName>(registrations: HookRegistration<T>[], args: HookArguments<T>): unknown[]; // prettier-ignore\n\t// Implementation\n\tprotected runSync<T extends HookName, R extends HookRegistration<T>[]>(\n\t\tregistrations: R,\n\t\targs: HookArguments<T>\n\t): (ReturnType<DefaultHandler<T>> | unknown)[] {\n\t\tconst results = [];\n\t\tfor (const { hook, handler, defaultHandler, once } of registrations) {\n\t\t\tconst result = (handler as DefaultHandler<T>)(this.swup.visit, args, defaultHandler);\n\t\t\tresults.push(result);\n\t\t\tif (isPromise(result)) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t`Promise returned from handler for synchronous hook '${hook}'.` +\n\t\t\t\t\t\t`Swup will not wait for it to resolve.`\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (once) {\n\t\t\t\tthis.off(hook, handler);\n\t\t\t}\n\t\t}\n\t\treturn results;\n\t}\n\n\t/**\n\t * Get all registered handlers for a hook, sorted by priority and registration order.\n\t * @param hook Name of the hook\n\t * @param defaultHandler The optional default handler of this hook\n\t * @returns An object with the handlers sorted into `before` and `after` arrays,\n\t *          as well as a flag indicating if the original handler was replaced\n\t */\n\tprotected getHandlers<T extends HookName>(hook: T, defaultHandler?: DefaultHandler<T>) {\n\t\tconst ledger = this.get(hook);\n\t\tif (!ledger) {\n\t\t\treturn { found: false, before: [], handler: [], after: [], replaced: false };\n\t\t}\n\n\t\tconst registrations = Array.from(ledger.values());\n\n\t\t// Let TypeScript know that replaced handlers are default handlers by filtering to true\n\t\tconst def = (T: HookRegistration<T>): T is HookRegistration<T, DefaultHandler<T>> => true;\n\t\tconst sort = this.sortRegistrations;\n\n\t\t// Filter into before, after, and replace handlers\n\t\tconst before = registrations.filter(({ before, replace }) => before && !replace).sort(sort);\n\t\tconst replace = registrations.filter(({ replace }) => replace).filter(def).sort(sort); // prettier-ignore\n\t\tconst after = registrations.filter(({ before, replace }) => !before && !replace).sort(sort);\n\t\tconst replaced = replace.length > 0;\n\n\t\t// Define main handler registration\n\t\t// Created as HookRegistration[] array to allow passing it into hooks.run() directly\n\t\tlet handler: HookRegistration<T, DefaultHandler<T>>[] = [];\n\t\tif (defaultHandler) {\n\t\t\thandler = [{ id: 0, hook, handler: defaultHandler }];\n\t\t\tif (replaced) {\n\t\t\t\tconst index = replace.length - 1;\n\t\t\t\tconst replacingHandler = replace[index].handler;\n\t\t\t\tconst createDefaultHandler = (index: number): DefaultHandler<T> | undefined => {\n\t\t\t\t\tconst next = replace[index - 1];\n\t\t\t\t\tif (next) {\n\t\t\t\t\t\treturn (visit, args) =>\n\t\t\t\t\t\t\tnext.handler(visit, args, createDefaultHandler(index - 1));\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn defaultHandler;\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tconst nestedDefaultHandler = createDefaultHandler(index);\n\t\t\t\thandler = [\n\t\t\t\t\t{ id: 0, hook, handler: replacingHandler, defaultHandler: nestedDefaultHandler }\n\t\t\t\t];\n\t\t\t}\n\t\t}\n\n\t\treturn { found: true, before, handler, after, replaced };\n\t}\n\n\t/**\n\t * Sort two hook registrations by priority and registration order.\n\t * @param a The registration object to compare\n\t * @param b The other registration object to compare with\n\t * @returns The sort direction\n\t */\n\tprotected sortRegistrations<T extends HookName>(\n\t\ta: HookRegistration<T>,\n\t\tb: HookRegistration<T>\n\t): number {\n\t\tconst priority = (a.priority ?? 0) - (b.priority ?? 0);\n\t\tconst id = a.id - b.id;\n\t\treturn priority || id || 0;\n\t}\n\n\t/**\n\t * Dispatch a custom event on the `document` for a hook. Prefixed with `swup:`\n\t * @param hook Name of the hook.\n\t */\n\tprotected dispatchDomEvent<T extends HookName>(hook: T, args?: HookArguments<T>): void {\n\t\tconst detail = { hook, args, visit: this.swup.visit };\n\t\tdocument.dispatchEvent(new CustomEvent(`swup:${hook}`, { detail }));\n\t}\n}\n", "import { escapeCssIdentifier as escape, query } from '../utils.js';\n\n/**\n * Find the anchor element for a given hash.\n *\n * @param hash Hash with or without leading '#'\n * @returns The element, if found, or null.\n *\n * @see https://html.spec.whatwg.org/#find-a-potential-indicated-element\n */\nexport const getAnchorElement = (hash?: string): Element | null => {\n\tif (hash && hash.charAt(0) === '#') {\n\t\thash = hash.substring(1);\n\t}\n\n\tif (!hash) {\n\t\treturn null;\n\t}\n\n\tconst decoded = decodeURIComponent(hash);\n\tlet element =\n\t\tdocument.getElementById(hash) ||\n\t\tdocument.getElementById(decoded) ||\n\t\tquery(`a[name='${escape(hash)}']`) ||\n\t\tquery(`a[name='${escape(decoded)}']`);\n\n\tif (!element && hash === 'top') {\n\t\telement = document.body;\n\t}\n\n\treturn element;\n};\n", "import { queryAll, toMs } from '../utils.js';\nimport Swup, { Options } from '../Swup.js';\n\nconst TRANSITION = 'transition';\nconst ANIMATION = 'animation';\n\ntype AnimationTypes = typeof TRANSITION | typeof ANIMATION;\ntype AnimationProperties = 'Delay' | 'Duration';\ntype AnimationStyleKeys = `${AnimationTypes}${AnimationProperties}` | 'transitionProperty';\ntype AnimationStyleDeclarations = Pick<CSSStyleDeclaration, AnimationStyleKeys>;\n\nexport type AnimationDirection = 'in' | 'out';\n\n/**\n * Return a Promise that resolves when all CSS animations and transitions\n * are done on the page. Filters by selector or takes elements directly.\n */\nexport async function awaitAnimations(\n\tthis: Swup,\n\t{\n\t\telements,\n\t\tselector\n\t}: {\n\t\tselector: Options['animationSelector'];\n\t\telements?: NodeListOf<HTMLElement> | HTMLElement[];\n\t}\n): Promise<void> {\n\t// Allow usage of swup without animations: { animationSelector: false }\n\tif (selector === false && !elements) {\n\t\treturn;\n\t}\n\n\t// Allow passing in elements\n\tlet animatedElements: HTMLElement[] = [];\n\tif (elements) {\n\t\tanimatedElements = Array.from(elements);\n\t} else if (selector) {\n\t\tanimatedElements = queryAll(selector, document.body);\n\t\t// Warn if no elements match the selector, but keep things going\n\t\tif (!animatedElements.length) {\n\t\t\tconsole.warn(`[swup] No elements found matching animationSelector \\`${selector}\\``);\n\t\t\treturn;\n\t\t}\n\t}\n\n\tconst awaitedAnimations = animatedElements.map((el) => awaitAnimationsOnElement(el));\n\tconst hasAnimations = awaitedAnimations.filter(Boolean).length > 0;\n\tif (!hasAnimations) {\n\t\tif (selector) {\n\t\t\tconsole.warn(\n\t\t\t\t`[swup] No CSS animation duration defined on elements matching \\`${selector}\\``\n\t\t\t);\n\t\t}\n\t\treturn;\n\t}\n\n\tawait Promise.all(awaitedAnimations);\n}\n\nfunction awaitAnimationsOnElement(element: Element): Promise<void> | false {\n\tconst { type, timeout, propCount } = getTransitionInfo(element);\n\n\t// Resolve immediately if no transition defined\n\tif (!type || !timeout) {\n\t\treturn false;\n\t}\n\n\treturn new Promise((resolve) => {\n\t\tconst endEvent = `${type}end`;\n\t\tconst startTime = performance.now();\n\t\tlet propsTransitioned = 0;\n\n\t\tconst end = () => {\n\t\t\telement.removeEventListener(endEvent, onEnd);\n\t\t\tresolve();\n\t\t};\n\n\t\tconst onEnd: EventListener = (event) => {\n\t\t\t// Skip transitions on child elements\n\t\t\tif (event.target !== element) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!isTransitionOrAnimationEvent(event)) {\n\t\t\t\tthrow new Error('Not a transition or animation event.');\n\t\t\t}\n\n\t\t\t// Skip transitions that happened before we started listening\n\t\t\tconst elapsedTime = (performance.now() - startTime) / 1000;\n\t\t\tif (elapsedTime < event.elapsedTime) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// End if all properties have transitioned\n\t\t\tif (++propsTransitioned >= propCount) {\n\t\t\t\tend();\n\t\t\t}\n\t\t};\n\n\t\tsetTimeout(() => {\n\t\t\tif (propsTransitioned < propCount) {\n\t\t\t\tend();\n\t\t\t}\n\t\t}, timeout + 1);\n\n\t\telement.addEventListener(endEvent, onEnd);\n\t});\n}\n\nexport function getTransitionInfo(element: Element, expectedType?: AnimationTypes) {\n\tconst styles = window.getComputedStyle(element) as AnimationStyleDeclarations;\n\n\tconst transitionDelays = getStyleProperties(styles, `${TRANSITION}Delay`);\n\tconst transitionDurations = getStyleProperties(styles, `${TRANSITION}Duration`);\n\tconst transitionTimeout = calculateTimeout(transitionDelays, transitionDurations);\n\tconst animationDelays = getStyleProperties(styles, `${ANIMATION}Delay`);\n\tconst animationDurations = getStyleProperties(styles, `${ANIMATION}Duration`);\n\tconst animationTimeout = calculateTimeout(animationDelays, animationDurations);\n\n\tlet type: AnimationTypes | null = null;\n\tlet timeout = 0;\n\tlet propCount = 0;\n\n\tif (expectedType === TRANSITION) {\n\t\tif (transitionTimeout > 0) {\n\t\t\ttype = TRANSITION;\n\t\t\ttimeout = transitionTimeout;\n\t\t\tpropCount = transitionDurations.length;\n\t\t}\n\t} else if (expectedType === ANIMATION) {\n\t\tif (animationTimeout > 0) {\n\t\t\ttype = ANIMATION;\n\t\t\ttimeout = animationTimeout;\n\t\t\tpropCount = animationDurations.length;\n\t\t}\n\t} else {\n\t\ttimeout = Math.max(transitionTimeout, animationTimeout);\n\t\ttype = timeout > 0 ? (transitionTimeout > animationTimeout ? TRANSITION : ANIMATION) : null;\n\t\tpropCount = type\n\t\t\t? type === TRANSITION\n\t\t\t\t? transitionDurations.length\n\t\t\t\t: animationDurations.length\n\t\t\t: 0;\n\t}\n\n\treturn {\n\t\ttype,\n\t\ttimeout,\n\t\tpropCount\n\t};\n}\n\nfunction isTransitionOrAnimationEvent(event: Event): event is TransitionEvent | AnimationEvent {\n\treturn [`${TRANSITION}end`, `${ANIMATION}end`].includes(event.type);\n}\n\nfunction getStyleProperties(styles: AnimationStyleDeclarations, key: AnimationStyleKeys): string[] {\n\treturn (styles[key] || '').split(', ');\n}\n\nfunction calculateTimeout(delays: string[], durations: string[]): number {\n\twhile (delays.length < durations.length) {\n\t\tdelays = delays.concat(delays);\n\t}\n\n\treturn Math.max(...durations.map((duration, i) => toMs(duration) + toMs(delays[i])));\n}\n", "import Swup from '../Swup.js';\nimport { createHistoryRecord, updateHistoryRecord, getCurrentUrl, Location } from '../helpers.js';\nimport { FetchOptions, PageData } from './fetchPage.js';\nimport { VisitInitOptions } from './Visit.js';\n\nexport type HistoryAction = 'push' | 'replace';\nexport type HistoryDirection = 'forwards' | 'backwards';\nexport type NavigationToSelfAction = 'scroll' | 'navigate';\nexport type CacheControl = Partial<{ read: boolean; write: boolean }>;\n\n/** Define how to navigate to a page. */\ntype NavigationOptions = {\n\t/** Whether this visit is animated. Default: `true` */\n\tanimate?: boolean;\n\t/** Name of a custom animation to run. */\n\tanimation?: string;\n\t/** History action to perform: `push` for creating a new history entry, `replace` for replacing the current entry. Default: `push` */\n\thistory?: HistoryAction;\n\t/** Whether this visit should read from or write to the cache. */\n\tcache?: CacheControl;\n};\n\n/**\n * Navigate to a new URL.\n * @param url The URL to navigate to.\n * @param options Options for how to perform this visit.\n * @returns Promise<void>\n */\nexport function navigate(\n\tthis: Swup,\n\turl: string,\n\toptions: NavigationOptions & FetchOptions = {},\n\tinit: Omit<VisitInitOptions, 'to'> = {}\n) {\n\tif (typeof url !== 'string') {\n\t\tthrow new Error(`swup.navigate() requires a URL parameter`);\n\t}\n\n\t// Check if the visit should be ignored\n\tif (this.shouldIgnoreVisit(url, { el: init.el, event: init.event })) {\n\t\twindow.location.href = url;\n\t\treturn;\n\t}\n\n\tconst { url: to, hash } = Location.fromUrl(url);\n\tthis.visit = this.createVisit({ ...init, to, hash });\n\tthis.performNavigation(options);\n}\n\n/**\n * Start a visit to a new URL.\n *\n * Internal method that assumes the visit context has already been created.\n *\n * As a user, you should call `swup.navigate(url)` instead.\n *\n * @param url The URL to navigate to.\n * @param options Options for how to perform this visit.\n * @returns Promise<void>\n */\nexport async function performNavigation(\n\tthis: Swup,\n\toptions: NavigationOptions & FetchOptions = {}\n) {\n\tconst { el } = this.visit.trigger;\n\toptions.referrer = options.referrer || this.currentPageUrl;\n\n\tif (options.animate === false) {\n\t\tthis.visit.animation.animate = false;\n\t}\n\n\t// Clean up old animation classes\n\tif (!this.visit.animation.animate) {\n\t\tthis.classes.clear();\n\t}\n\n\t// Get history action from option or attribute on trigger element\n\tconst history = options.history || el?.getAttribute('data-swup-history') || undefined;\n\tif (history && ['push', 'replace'].includes(history)) {\n\t\tthis.visit.history.action = history as HistoryAction;\n\t}\n\n\t// Get custom animation name from option or attribute on trigger element\n\tconst animation = options.animation || el?.getAttribute('data-swup-animation') || undefined;\n\tif (animation) {\n\t\tthis.visit.animation.name = animation;\n\t}\n\n\t// Sanitize cache option\n\tif (typeof options.cache === 'object') {\n\t\tthis.visit.cache.read = options.cache.read ?? this.visit.cache.read;\n\t\tthis.visit.cache.write = options.cache.write ?? this.visit.cache.write;\n\t} else if (options.cache !== undefined) {\n\t\tthis.visit.cache = { read: !!options.cache, write: !!options.cache };\n\t}\n\t// Delete this so that window.fetch doesn't mis-interpret it\n\tdelete options.cache;\n\n\ttry {\n\t\tawait this.hooks.call('visit:start', undefined);\n\n\t\t// Begin loading page\n\t\tconst pagePromise = this.hooks.call('page:load', { options }, async (visit, args) => {\n\t\t\t// Read from cache\n\t\t\tlet cachedPage: PageData | undefined;\n\t\t\tif (this.visit.cache.read) {\n\t\t\t\tcachedPage = this.cache.get(visit.to.url);\n\t\t\t}\n\n\t\t\targs.page = cachedPage || (await this.fetchPage(visit.to.url, args.options));\n\t\t\targs.cache = !!cachedPage;\n\n\t\t\treturn args.page;\n\t\t});\n\n\t\t// Create/update history record if this is not a popstate call or leads to the same URL\n\t\tif (!this.visit.history.popstate) {\n\t\t\t// Add the hash directly from the trigger element\n\t\t\tconst newUrl = this.visit.to.url + this.visit.to.hash;\n\t\t\tif (\n\t\t\t\tthis.visit.history.action === 'replace' ||\n\t\t\t\tthis.visit.to.url === this.currentPageUrl\n\t\t\t) {\n\t\t\t\tupdateHistoryRecord(newUrl);\n\t\t\t} else {\n\t\t\t\tconst index = this.currentHistoryIndex + 1;\n\t\t\t\tcreateHistoryRecord(newUrl, { index });\n\t\t\t}\n\t\t}\n\n\t\tthis.currentPageUrl = getCurrentUrl();\n\n\t\t// Wait for page before starting to animate out?\n\t\tif (this.visit.animation.wait) {\n\t\t\tconst { html } = await pagePromise;\n\t\t\tthis.visit.to.html = html;\n\t\t}\n\n\t\t// Wait for page to load and leave animation to finish\n\t\tconst animationPromise = this.animatePageOut();\n\t\tconst [page] = await Promise.all([pagePromise, animationPromise]);\n\n\t\t// Render page: replace content and scroll to top/fragment\n\t\tawait this.renderPage(this.visit.to.url, page);\n\n\t\t// Wait for enter animation\n\t\tawait this.animatePageIn();\n\n\t\t// Finalize visit\n\t\tawait this.hooks.call('visit:end', undefined, () => this.classes.clear());\n\n\t\t// Reset visit info after finish?\n\t\t// if (this.visit.to && this.isSameResolvedUrl(this.visit.to.url, requestedUrl)) {\n\t\t// \tthis.createVisit({ to: undefined });\n\t\t// }\n\t} catch (error: unknown) {\n\t\t// Return early if error is undefined (probably aborted preload request)\n\t\tif (!error) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Log to console as we swallow almost all hook errors\n\t\tconsole.error(error);\n\n\t\t// Rewrite `skipPopStateHandling` to redirect manually when `history.go` is processed\n\t\tthis.options.skipPopStateHandling = () => {\n\t\t\twindow.location.href = this.visit.to.url + this.visit.to.hash;\n\t\t\treturn true;\n\t\t};\n\n\t\t// Go back to the actual page we're still at\n\t\twindow.history.go(-1);\n\t}\n}\n", "import Swup from '../Swup.js';\nimport { Location } from '../helpers.js';\n\n/** A page object as used by swup and its cache. */\nexport interface PageData {\n\t/** The URL of the page */\n\turl: string;\n\t/** The complete HTML response received from the server */\n\thtml: string;\n}\n\n/** Define how a page is fetched. */\nexport interface FetchOptions extends Omit<RequestInit, 'cache'> {\n\t/** The request method. */\n\tmethod?: 'GET' | 'POST';\n\t/** The body of the request: raw string, form data object or URL params. */\n\tbody?: string | FormData | URLSearchParams;\n}\n\nexport class FetchError extends Error {\n\turl: string;\n\tstatus: number;\n\tconstructor(message: string, details: { url: string; status: number }) {\n\t\tsuper(message);\n\t\tthis.name = 'FetchError';\n\t\tthis.url = details.url;\n\t\tthis.status = details.status;\n\t}\n}\n\n/**\n * Fetch a page from the server, return it and cache it.\n */\nexport async function fetchPage(\n\tthis: Swup,\n\turl: URL | string,\n\toptions: FetchOptions = {}\n): Promise<PageData> {\n\turl = Location.fromUrl(url).url;\n\n\tconst headers = { ...this.options.requestHeaders, ...options.headers };\n\toptions = { ...options, headers };\n\n\t// Allow hooking before this and returning a custom response-like object (e.g. custom fetch implementation)\n\tconst response: Response = await this.hooks.call(\n\t\t'fetch:request',\n\t\t{ url, options },\n\t\t(visit, { url, options }) => fetch(url, options)\n\t);\n\n\tconst { status, url: responseUrl } = response;\n\tconst html = await response.text();\n\n\tif (status === 500) {\n\t\tthis.hooks.call('fetch:error', { status, response, url: responseUrl });\n\t\tthrow new FetchError(`Server error: ${responseUrl}`, { status, url: responseUrl });\n\t}\n\n\tif (!html) {\n\t\tthrow new FetchError(`Empty response: ${responseUrl}`, { status, url: responseUrl });\n\t}\n\n\t// Resolve real url after potential redirect\n\tconst { url: finalUrl } = Location.fromUrl(responseUrl);\n\tconst page = { url: finalUrl, html };\n\n\t// Write to cache for safe methods and non-redirects\n\tif (\n\t\tthis.visit.cache.write &&\n\t\t(!options.method || options.method === 'GET') &&\n\t\turl === finalUrl\n\t) {\n\t\tthis.cache.set(page.url, page);\n\t}\n\n\treturn page;\n}\n", "import Swup from '../Swup.js';\nimport { classify } from '../helpers.js';\n\n/**\n * Perform the out/leave animation of the current page.\n * @returns Promise<void>\n */\nexport const animatePageOut = async function (this: Swup) {\n\tif (!this.visit.animation.animate) {\n\t\tawait this.hooks.call('animation:skip', undefined);\n\t\treturn;\n\t}\n\n\tawait this.hooks.call('animation:out:start', undefined, (visit) => {\n\t\tthis.classes.add('is-changing', 'is-leaving', 'is-animating');\n\t\tif (visit.history.popstate) {\n\t\t\tthis.classes.add('is-popstate');\n\t\t}\n\t\tif (visit.animation.name) {\n\t\t\tthis.classes.add(`to-${classify(visit.animation.name)}`);\n\t\t}\n\t});\n\n\tawait this.hooks.call('animation:out:await', { skip: false }, async (visit, { skip }) => {\n\t\tif (skip) return;\n\t\tawait this.awaitAnimations({ selector: visit.animation.selector });\n\t});\n\n\tawait this.hooks.call('animation:out:end', undefined);\n};\n", "import Swup, { Options } from '../Swup.js';\nimport { query, queryAll } from '../utils.js';\nimport { PageData } from './fetchPage.js';\n\n/**\n * Perform the replacement of content after loading a page.\n *\n * It takes an object with the page data as returned from `fetchPage` and a list\n * of container selectors to replace.\n *\n * @returns Whether all containers were replaced.\n */\nexport const replaceContent = function (\n\tthis: Swup,\n\t{ html }: PageData,\n\t{ containers }: { containers: Options['containers'] } = this.options\n): boolean {\n\tconst incomingDocument = new DOMParser().parseFromString(html, 'text/html');\n\n\t// Update browser title\n\tconst title = incomingDocument.querySelector('title')?.innerText || '';\n\tdocument.title = title;\n\n\t// Save persisted elements\n\tconst persistedElements = queryAll('[data-swup-persist]:not([data-swup-persist=\"\"])');\n\n\t// Update content containers\n\tconst replaced = containers\n\t\t.map((selector) => {\n\t\t\tconst currentEl = document.querySelector(selector);\n\t\t\tconst incomingEl = incomingDocument.querySelector(selector);\n\t\t\tif (currentEl && incomingEl) {\n\t\t\t\tcurrentEl.replaceWith(incomingEl);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\tif (!currentEl) {\n\t\t\t\tconsole.warn(`[swup] Container missing in current document: ${selector}`);\n\t\t\t}\n\t\t\tif (!incomingEl) {\n\t\t\t\tconsole.warn(`[swup] Container missing in incoming document: ${selector}`);\n\t\t\t}\n\t\t\treturn false;\n\t\t})\n\t\t.filter(Boolean);\n\n\t// Restore persisted elements\n\tpersistedElements.forEach((existing) => {\n\t\tconst key = existing.getAttribute('data-swup-persist');\n\t\tconst replacement = query(`[data-swup-persist=\"${key}\"]`);\n\t\tif (replacement && replacement !== existing) {\n\t\t\treplacement.replaceWith(existing);\n\t\t}\n\t});\n\n\treturn replaced.length === containers.length;\n};\n", "import Swup from '../Swup.js';\n\n/**\n * Update the scroll position after page render.\n * @returns Promise<boolean>\n */\nexport const scrollToContent = function (this: Swup): boolean {\n\tconst options: ScrollIntoViewOptions = { behavior: 'auto' };\n\tconst { target, reset } = this.visit.scroll;\n\tconst scrollTarget = target ?? this.visit.to.hash;\n\n\tlet scrolled = false;\n\n\tif (scrollTarget) {\n\t\tscrolled = this.hooks.callSync(\n\t\t\t'scroll:anchor',\n\t\t\t{ hash: scrollTarget, options },\n\t\t\t(visit, { hash, options }) => {\n\t\t\t\tconst anchor = this.getAnchorElement(hash);\n\t\t\t\tif (anchor) {\n\t\t\t\t\tanchor.scrollIntoView(options);\n\t\t\t\t}\n\t\t\t\treturn !!anchor;\n\t\t\t}\n\t\t);\n\t}\n\n\tif (reset && !scrolled) {\n\t\tscrolled = this.hooks.callSync('scroll:top', { options }, (visit, { options }) => {\n\t\t\twindow.scrollTo({ top: 0, left: 0, ...options });\n\t\t\treturn true;\n\t\t});\n\t}\n\n\treturn scrolled;\n};\n", "import Swup from '../Swup.js';\nimport { nextTick } from '../utils.js';\n\n/**\n * Perform the in/enter animation of the next page.\n * @returns Promise<void>\n */\nexport const animatePageIn = async function (this: Swup) {\n\tif (!this.visit.animation.animate) {\n\t\treturn;\n\t}\n\n\tconst animation = this.hooks.call(\n\t\t'animation:in:await',\n\t\t{ skip: false },\n\t\tasync (visit, { skip }) => {\n\t\t\tif (skip) return;\n\t\t\tawait this.awaitAnimations({ selector: visit.animation.selector });\n\t\t}\n\t);\n\n\tawait nextTick();\n\n\tawait this.hooks.call('animation:in:start', undefined, () => {\n\t\tthis.classes.remove('is-animating');\n\t});\n\n\tawait animation;\n\n\tawait this.hooks.call('animation:in:end', undefined);\n};\n", "import { updateHistoryRecord, getCurrentUrl, classify } from '../helpers.js';\nimport Swup from '../Swup.js';\nimport { PageData } from './fetchPage.js';\n\n/**\n * Render the next page: replace the content and update scroll position.\n * @returns Promise<void>\n */\nexport const renderPage = async function (this: Swup, requestedUrl: string, page: PageData) {\n\tconst { url, html } = page;\n\n\tthis.classes.remove('is-leaving');\n\n\t// do nothing if another page was requested in the meantime\n\tif (!this.isSameResolvedUrl(getCurrentUrl(), requestedUrl)) {\n\t\treturn;\n\t}\n\n\t// update state if the url was redirected\n\tif (!this.isSameResolvedUrl(getCurrentUrl(), url)) {\n\t\tupdateHistoryRecord(url);\n\t\tthis.currentPageUrl = getCurrentUrl();\n\t\tthis.visit.to.url = this.currentPageUrl;\n\t}\n\n\t// only add for animated page loads\n\tif (this.visit.animation.animate) {\n\t\tthis.classes.add('is-rendering');\n\t}\n\n\t// save html into visit context for easier retrieval\n\tthis.visit.to.html = html;\n\n\t// replace content: allow handlers and plugins to overwrite paga data and containers\n\tawait this.hooks.call('content:replace', { page }, (visit, { page }) => {\n\t\tconst success = this.replaceContent(page, { containers: visit.containers });\n\t\tif (!success) {\n\t\t\tthrow new Error('[swup] Container mismatch, aborting');\n\t\t}\n\t\tif (visit.animation.animate) {\n\t\t\t// Make sure to add these classes to new containers as well\n\t\t\tthis.classes.add('is-animating', 'is-changing', 'is-rendering');\n\t\t\tif (visit.animation.name) {\n\t\t\t\tthis.classes.add(`to-${classify(visit.animation.name)}`);\n\t\t\t}\n\t\t}\n\t});\n\n\t// scroll into view: either anchor or top of page\n\t// @ts-ignore: not returning a promise is intentional to allow users to pause in handler\n\tawait this.hooks.call('content:scroll', undefined, () => {\n\t\treturn this.scrollToContent();\n\t});\n\n\tawait this.hooks.call('page:view', { url: this.currentPageUrl, title: document.title });\n};\n", "import Swup from '../Swup.js';\n\nexport type Plugin = {\n\t/** Identify as a swup plugin */\n\tisSwupPlugin: true;\n\t/** Name of this plugin */\n\tname: string;\n\t/** Version of this plugin. Currently not in use, defined here for backward compatiblity. */\n\tversion?: string;\n\t/** The swup instance that mounted this plugin */\n\tswup?: Swup;\n\t/** Version requirements of this plugin. Example: `{ swup: '>=4' }` */\n\trequires?: Record<string, string | string[]>;\n\t/** Run on mount */\n\tmount: () => void;\n\t/** Run on unmount */\n\tunmount: () => void;\n\t_beforeMount?: () => void;\n\t_afterUnmount?: () => void;\n\t_checkRequirements?: () => boolean;\n};\n\nconst isSwupPlugin = (maybeInvalidPlugin: unknown): maybeInvalidPlugin is Plugin => {\n\t// @ts-ignore: this might be anything, object or no\n\treturn Boolean(maybeInvalidPlugin?.isSwupPlugin);\n};\n\n/** Install a plugin. */\nexport const use = function (this: Swup, plugin: unknown) {\n\tif (!isSwupPlugin(plugin)) {\n\t\tconsole.error('Not a swup plugin instance', plugin);\n\t\treturn;\n\t}\n\n\tplugin.swup = this;\n\tif (plugin._checkRequirements) {\n\t\tif (!plugin._checkRequirements()) {\n\t\t\treturn;\n\t\t}\n\t}\n\tif (plugin._beforeMount) {\n\t\tplugin._beforeMount();\n\t}\n\tplugin.mount();\n\n\tthis.plugins.push(plugin);\n\n\treturn this.plugins;\n};\n\n/** Uninstall a plugin. */\nexport function unuse(this: Swup, pluginOrName: Plugin | string) {\n\tconst plugin = this.findPlugin(pluginOrName);\n\tif (!plugin) {\n\t\tconsole.error('No such plugin', plugin);\n\t\treturn;\n\t}\n\n\tplugin.unmount();\n\tif (plugin._afterUnmount) {\n\t\tplugin._afterUnmount();\n\t}\n\n\tthis.plugins = this.plugins.filter((p) => p !== plugin);\n\n\treturn this.plugins;\n}\n\n/** Find a plugin by name or reference. */\nexport function findPlugin(this: Swup, pluginOrName: Plugin | string) {\n\treturn this.plugins.find(\n\t\t(plugin) =>\n\t\t\tplugin === pluginOrName ||\n\t\t\tplugin.name === pluginOrName ||\n\t\t\tplugin.name === `Swup${String(pluginOrName)}`\n\t);\n}\n", "import Swup from '../Swup.js';\n\n/**\n * Utility function to validate and run the global option 'resolveUrl'\n * @param {string} url\n * @returns {string} the resolved url\n */\nexport function resolveUrl(this: Swup, url: string): string {\n\tif (typeof this.options.resolveUrl !== 'function') {\n\t\tconsole.warn(`[swup] options.resolveUrl expects a callback function.`);\n\t\treturn url;\n\t}\n\tconst result = this.options.resolveUrl(url);\n\tif (!result || typeof result !== 'string') {\n\t\tconsole.warn(`[swup] options.resolveUrl needs to return a url`);\n\t\treturn url;\n\t}\n\tif (result.startsWith('//') || result.startsWith('http')) {\n\t\tconsole.warn(`[swup] options.resolveUrl needs to return a relative url`);\n\t\treturn url;\n\t}\n\treturn result;\n}\n\n/**\n * Compares the resolved version of two paths and returns true if they are the same\n * @param {string} url1\n * @param {string} url2\n * @returns {boolean}\n */\nexport function isSameResolvedUrl(this: Swup, url1: string, url2: string): boolean {\n\treturn this.resolveUrl(url1) === this.resolveUrl(url2);\n}\n", "import { DelegateEvent } from 'delegate-it';\n\nimport version from './config/version.js';\n\nimport { delegateEvent, getCurrentUrl, Location, updateHistoryRecord } from './helpers.js';\nimport { DelegateEventUnsubscribe } from './helpers/delegateEvent.js';\n\nimport { Cache } from './modules/Cache.js';\nimport { Classes } from './modules/Classes.js';\nimport { Visit, createVisit } from './modules/Visit.js';\nimport { Hooks } from './modules/Hooks.js';\nimport { getAnchorElement } from './modules/getAnchorElement.js';\nimport { awaitAnimations } from './modules/awaitAnimations.js';\nimport { navigate, performNavigation, NavigationToSelfAction } from './modules/navigate.js';\nimport { fetchPage } from './modules/fetchPage.js';\nimport { animatePageOut } from './modules/animatePageOut.js';\nimport { replaceContent } from './modules/replaceContent.js';\nimport { scrollToContent } from './modules/scrollToContent.js';\nimport { animatePageIn } from './modules/animatePageIn.js';\nimport { renderPage } from './modules/renderPage.js';\nimport { use, unuse, findPlugin, Plugin } from './modules/plugins.js';\nimport { isSameResolvedUrl, resolveUrl } from './modules/resolveUrl.js';\nimport { nextTick } from './utils.js';\nimport { HistoryState } from './helpers/createHistoryRecord.js';\n\n/** Options for customizing swup's behavior. */\nexport type Options = {\n\t/** Whether history visits are animated. Default: `false` */\n\tanimateHistoryBrowsing: boolean;\n\t/** Selector for detecting animation timing. Default: `[class*=\"transition-\"]` */\n\tanimationSelector: string | false;\n\t/** Elements on which to add animation classes. Default: `html` element */\n\tanimationScope: 'html' | 'containers';\n\t/** Enable in-memory page cache. Default: `true` */\n\tcache: boolean;\n\t/** Content containers to be replaced on page visits. Default: `['#swup']` */\n\tcontainers: string[];\n\t/** Callback for ignoring visits. Receives the element and event that triggered the visit. */\n\tignoreVisit: (url: string, { el, event }: { el?: Element; event?: Event }) => boolean;\n\t/** Selector for links that trigger visits. Default: `'a[href]'` */\n\tlinkSelector: string;\n\t/** How swup handles links to the same page. Default: `scroll` */\n\tlinkToSelf: NavigationToSelfAction;\n\t/** Plugins to register on startup. */\n\tplugins: Plugin[];\n\t/** Custom headers sent along with fetch requests. */\n\trequestHeaders: Record<string, string>;\n\t/** Rewrite URLs before loading them. */\n\tresolveUrl: (url: string) => string;\n\t/** Callback for telling swup to ignore certain popstate events.  */\n\tskipPopStateHandling: (event: PopStateEvent) => boolean;\n};\n\nconst defaults: Options = {\n\tanimateHistoryBrowsing: false,\n\tanimationSelector: '[class*=\"transition-\"]',\n\tanimationScope: 'html',\n\tcache: true,\n\tcontainers: ['#swup'],\n\tignoreVisit: (url, { el } = {}) => !!el?.closest('[data-no-swup]'),\n\tlinkSelector: 'a[href]',\n\tlinkToSelf: 'scroll',\n\tplugins: [],\n\tresolveUrl: (url) => url,\n\trequestHeaders: {\n\t\t'X-Requested-With': 'swup',\n\t\t'Accept': 'text/html, application/xhtml+xml'\n\t},\n\tskipPopStateHandling: (event) => (event.state as HistoryState)?.source !== 'swup'\n};\n\n/** Swup page transition library. */\nexport default class Swup {\n\t/** Library version */\n\treadonly version: string = version;\n\t/** Options passed into the instance */\n\toptions: Options;\n\t/** Default options before merging user options */\n\treadonly defaults: Options = defaults;\n\t/** Registered plugin instances */\n\tplugins: Plugin[] = [];\n\t/** Data about the current visit */\n\tvisit: Visit;\n\t/** Cache instance */\n\treadonly cache: Cache;\n\t/** Hook registry */\n\treadonly hooks: Hooks;\n\t/** Animation class manager */\n\treadonly classes: Classes;\n\t/** URL of the currently visible page */\n\tcurrentPageUrl: string = getCurrentUrl();\n\t/** Index of the current history entry */\n\tprotected currentHistoryIndex = 1;\n\t/** Delegated event subscription handle */\n\tprotected clickDelegate?: DelegateEventUnsubscribe;\n\n\t/** Install a plugin */\n\tuse = use;\n\t/** Uninstall a plugin */\n\tunuse = unuse;\n\t/** Find a plugin by name or instance */\n\tfindPlugin = findPlugin;\n\n\t/** Log a message. Has no effect unless debug plugin is installed */\n\tlog: (message: string, context?: unknown) => void = () => {};\n\n\t/** Navigate to a new URL */\n\tnavigate = navigate;\n\t/** Actually perform a navigation */\n\tprotected performNavigation = performNavigation;\n\t/** Create a new context for this visit */\n\tprotected createVisit = createVisit;\n\t/** Register a delegated event listener */\n\tdelegateEvent = delegateEvent;\n\t/** Fetch a page from the server */\n\tfetchPage = fetchPage;\n\t/** Resolve when animations on the page finish */\n\tawaitAnimations = awaitAnimations;\n\tprotected renderPage = renderPage;\n\t/** Replace the content after page load */\n\treplaceContent = replaceContent;\n\tprotected animatePageIn = animatePageIn;\n\tprotected animatePageOut = animatePageOut;\n\tprotected scrollToContent = scrollToContent;\n\t/** Find the anchor element for a given hash */\n\tgetAnchorElement = getAnchorElement;\n\n\t/** Get the current page URL */\n\tgetCurrentUrl = getCurrentUrl;\n\t/** Resolve a URL to its final location */\n\tresolveUrl = resolveUrl;\n\t/** Check if two URLs resolve to the same location */\n\tprotected isSameResolvedUrl = isSameResolvedUrl;\n\n\tconstructor(options: Partial<Options> = {}) {\n\t\t// Merge defaults and options\n\t\tthis.options = { ...this.defaults, ...options };\n\n\t\tthis.handleLinkClick = this.handleLinkClick.bind(this);\n\t\tthis.handlePopState = this.handlePopState.bind(this);\n\n\t\tthis.cache = new Cache(this);\n\t\tthis.classes = new Classes(this);\n\t\tthis.hooks = new Hooks(this);\n\t\tthis.visit = this.createVisit({ to: '' });\n\n\t\tif (!this.checkRequirements()) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.enable();\n\t}\n\n\tprotected checkRequirements() {\n\t\tif (typeof Promise === 'undefined') {\n\t\t\tconsole.warn('Promise is not supported');\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\n\t/** Enable this instance, adding listeners and classnames. */\n\tasync enable() {\n\t\t// Add event listener\n\t\tconst { linkSelector } = this.options;\n\t\tthis.clickDelegate = this.delegateEvent(linkSelector, 'click', this.handleLinkClick);\n\n\t\twindow.addEventListener('popstate', this.handlePopState);\n\n\t\t// Set scroll restoration to manual if animating history visits\n\t\tif (this.options.animateHistoryBrowsing) {\n\t\t\twindow.history.scrollRestoration = 'manual';\n\t\t}\n\n\t\t// Initial save to cache\n\t\tif (this.options.cache) {\n\t\t\t// Disabled to avoid caching modified dom state: logic moved to preload plugin\n\t\t\t// https://github.com/swup/swup/issues/475\n\t\t}\n\n\t\t// Mount plugins\n\t\tthis.options.plugins.forEach((plugin) => this.use(plugin));\n\n\t\t// Modify initial history record\n\t\tupdateHistoryRecord(null, { index: 1 });\n\n\t\t// Give consumers a chance to hook into enable\n\t\tawait nextTick();\n\n\t\t// Trigger enable hook\n\t\tawait this.hooks.call('enable', undefined, () => {\n\t\t\t// Add swup-enabled class to html tag\n\t\t\tdocument.documentElement.classList.add('swup-enabled');\n\t\t});\n\t}\n\n\t/** Disable this instance, removing listeners and classnames. */\n\tasync destroy() {\n\t\t// remove delegated listener\n\t\tthis.clickDelegate!.destroy();\n\n\t\t// remove popstate listener\n\t\twindow.removeEventListener('popstate', this.handlePopState);\n\n\t\t// empty cache\n\t\tthis.cache.clear();\n\n\t\t// unmount plugins\n\t\tthis.options.plugins.forEach((plugin) => this.unuse(plugin));\n\n\t\t// trigger disable hook\n\t\tawait this.hooks.call('disable', undefined, () => {\n\t\t\t// remove swup-enabled class from html tag\n\t\t\tdocument.documentElement.classList.remove('swup-enabled');\n\t\t});\n\n\t\t// remove handlers\n\t\tthis.hooks.clear();\n\t}\n\n\t/** Determine if a visit should be ignored by swup, based on URL or trigger element. */\n\tshouldIgnoreVisit(href: string, { el, event }: { el?: Element; event?: Event } = {}) {\n\t\tconst { origin, url, hash } = Location.fromUrl(href);\n\n\t\t// Ignore if the new origin doesn't match the current one\n\t\tif (origin !== window.location.origin) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Ignore if the link/form would open a new window (or none at all)\n\t\tif (el && this.triggerWillOpenNewWindow(el)) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Ignore if the visit should be ignored as per user options\n\t\tif (this.options.ignoreVisit(url + hash, { el, event })) {\n\t\t\treturn true;\n\t\t}\n\n\t\t// Finally, allow the visit\n\t\treturn false;\n\t}\n\n\tprotected handleLinkClick(event: DelegateEvent<MouseEvent>) {\n\t\tconst el = event.delegateTarget as HTMLAnchorElement;\n\t\tconst { href, url, hash } = Location.fromElement(el);\n\n\t\t// Exit early if the link should be ignored\n\t\tif (this.shouldIgnoreVisit(href, { el, event })) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.visit = this.createVisit({ to: url, hash, el, event });\n\n\t\t// Exit early if control key pressed\n\t\tif (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) {\n\t\t\tthis.hooks.call('link:newtab', { href });\n\t\t\treturn;\n\t\t}\n\n\t\t// Exit early if other than left mouse button\n\t\tif (event.button !== 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.hooks.callSync('link:click', { el, event }, () => {\n\t\t\tconst from = this.visit.from.url ?? '';\n\n\t\t\tevent.preventDefault();\n\n\t\t\t// Handle links to the same page\n\t\t\tif (!url || url === from) {\n\t\t\t\tif (hash) {\n\t\t\t\t\t// With hash: scroll to anchor\n\t\t\t\t\tthis.hooks.callSync('link:anchor', { hash }, () => {\n\t\t\t\t\t\tupdateHistoryRecord(url + hash);\n\t\t\t\t\t\tthis.scrollToContent();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// Without hash: scroll to top or load/reload page\n\t\t\t\t\tthis.hooks.callSync('link:self', undefined, () => {\n\t\t\t\t\t\tswitch (this.options.linkToSelf) {\n\t\t\t\t\t\t\tcase 'navigate':\n\t\t\t\t\t\t\t\treturn this.performNavigation();\n\t\t\t\t\t\t\tcase 'scroll':\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\treturn this.scrollToContent();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Exit early if the resolved path hasn't changed\n\t\t\tif (this.isSameResolvedUrl(url, from)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Finally, proceed with loading the page\n\t\t\tthis.performNavigation();\n\t\t});\n\t}\n\n\tprotected handlePopState(event: PopStateEvent) {\n\t\tconst href: string = (event.state as HistoryState)?.url ?? location.href;\n\n\t\t// Exit early if this event should be ignored\n\t\tif (this.options.skipPopStateHandling(event)) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Exit early if the resolved path hasn't changed\n\t\tif (this.isSameResolvedUrl(getCurrentUrl(), this.currentPageUrl)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { url, hash } = Location.fromUrl(href);\n\n\t\tthis.visit = this.createVisit({ to: url, hash, event });\n\n\t\t// Mark as history visit\n\t\tthis.visit.history.popstate = true;\n\n\t\t// Determine direction of history visit\n\t\tconst index = Number((event.state as HistoryState)?.index);\n\t\tif (index) {\n\t\t\tconst direction = index - this.currentHistoryIndex > 0 ? 'forwards' : 'backwards';\n\t\t\tthis.visit.history.direction = direction;\n\t\t}\n\n\t\t// Disable animation & scrolling for history visits\n\t\tthis.visit.animation.animate = false;\n\t\tthis.visit.scroll.reset = false;\n\t\tthis.visit.scroll.target = false;\n\n\t\t// Animated history visit: re-enable animation & scroll reset\n\t\tif (this.options.animateHistoryBrowsing) {\n\t\t\tthis.visit.animation.animate = true;\n\t\t\tthis.visit.scroll.reset = true;\n\t\t}\n\n\t\t// Does this even do anything?\n\t\t// if (!hash) {\n\t\t// \tevent.preventDefault();\n\t\t// }\n\n\t\tthis.hooks.callSync('history:popstate', { event }, () => {\n\t\t\tthis.performNavigation();\n\t\t});\n\t}\n\n\t/** Determine whether an element will open a new tab when clicking/activating. */\n\tprotected triggerWillOpenNewWindow(triggerEl: Element) {\n\t\tif (triggerEl.matches('[download], [target=\"_blank\"]')) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n}\n"], "names": ["classify", "text", "fallback", "String", "toLowerCase", "replace", "getCurrentUrl", "_temp", "hash", "location", "pathname", "search", "createHistoryRecord", "url", "customData", "data", "random", "Math", "source", "history", "pushState", "updateHistoryRecord", "state", "replaceState", "ledger", "WeakMap", "editLedger", "wanted", "baseElement", "callback", "setup", "has", "elementMap", "get", "set", "setups", "Set", "existed", "add", "delete", "delegateEvent", "selector", "type", "options", "controller", "AbortController", "signal", "base", "document", "aborted", "once", "nativeListenerOptions", "Document", "documentElement", "capture", "Boolean", "listenerFn", "event", "<PERSON><PERSON><PERSON><PERSON>", "target", "Text", "parentElement", "Element", "currentTarget", "closest", "contains", "safeClosest", "Object", "assign", "call", "removeEventListener", "JSON", "stringify", "addEventListener", "delegate", "destroy", "abort", "Location", "URL", "constructor", "baseURI", "super", "toString", "this", "fromElement", "el", "href", "getAttribute", "fromUrl", "<PERSON><PERSON>", "swup", "pages", "Map", "size", "all", "copy", "for<PERSON>ach", "page", "key", "resolve", "result", "hooks", "callSync", "update", "payload", "clear", "undefined", "prune", "predicate", "urlToResolve", "resolveUrl", "query", "context", "querySelector", "queryAll", "Array", "from", "querySelectorAll", "nextTick", "Promise", "requestAnimationFrame", "isPromise", "obj", "then", "escapeCssIdentifier", "ident", "window", "CSS", "escape", "toMs", "s", "Number", "slice", "Classes", "swupClasses", "selectors", "scope", "visit", "animation", "containers", "isArray", "join", "targets", "trim", "classList", "arguments", "remove", "className", "split", "filter", "c", "isSwupClass", "some", "startsWith", "createVisit", "_ref", "to", "currentPageUrl", "animate", "wait", "name", "animationScope", "animationSelector", "trigger", "cache", "read", "write", "action", "popstate", "direction", "scroll", "reset", "_iteratorSymbol", "Symbol", "iterator", "pact", "value", "_Pact", "o", "_settle", "bind", "v", "observer", "onFulfilled", "onRejected", "e", "_this", "_isSettledPact", "thenable", "<PERSON>s", "registry", "init", "hook", "create", "exists", "console", "error", "on", "handler", "warn", "id", "registration", "off", "before", "args", "defaultHandler", "after", "getHandlers", "run", "dispatchDomEvent", "reject", "runSync", "registrations", "_this2", "results", "body", "check", "step", "_cycle", "next", "done", "return", "_fixup", "TypeError", "values", "i", "length", "array", "_forTo", "_forOf", "_ref2", "func", "runAsPromise", "push", "found", "replaced", "sort", "sortRegistrations", "_ref3", "_ref4", "T", "_ref5", "index", "createDefaultHandler", "a", "b", "priority", "dispatchEvent", "CustomEvent", "detail", "getAnchorElement", "char<PERSON>t", "substring", "decoded", "decodeURIComponent", "element", "getElementById", "awaitAnimations", "elements", "animatedElements", "awaitedAnimations", "map", "timeout", "propCount", "expectedType", "styles", "getComputedStyle", "transitionDelays", "getStyleProperties", "TRANSITION", "transitionDurations", "transitionTimeout", "calculateTimeout", "animationDelays", "ANIMATION", "animationDurations", "animationTimeout", "max", "getTransitionInfo", "endEvent", "startTime", "performance", "now", "propsTransitioned", "end", "onEnd", "includes", "isTransitionOrAnimationEvent", "Error", "elapsedTime", "setTimeout", "awaitAnimationsOnElement", "delays", "durations", "concat", "duration", "performNavigation", "referrer", "classes", "_temp2", "animationPromise", "animatePageOut", "pagePromise", "renderPage", "animatePageIn", "_temp3", "_this$fetchPage", "cachedPage", "fetchPage", "newUrl", "currentHistoryIndex", "html", "_catch", "skipPopStateHandling", "go", "navigate", "shouldIgnoreVisit", "headers", "requestHeaders", "fetch", "response", "status", "responseUrl", "FetchError", "finalUrl", "method", "message", "details", "_exit", "_result", "skip", "replace<PERSON><PERSON><PERSON>", "incomingDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "title", "innerText", "persistedElements", "currentEl", "incomingEl", "replaceWith", "existing", "replacement", "scroll<PERSON>o<PERSON>ontent", "behavior", "scrollTarget", "scrolled", "anchor", "scrollIntoView", "scrollTo", "top", "left", "requestedUrl", "isSameResolvedUrl", "use", "plugin", "maybeInvalidPlugin", "isSwupPlugin", "_checkRequirements", "_beforeMount", "mount", "plugins", "unuse", "pluginOrName", "findPlugin", "unmount", "_afterUnmount", "p", "find", "url1", "url2", "defaults", "animateHistoryBrowsing", "ignoreVisit", "linkSelector", "linkToSelf", "Accept", "version", "clickDelegate", "log", "handleLinkClick", "handlePopState", "checkRequirements", "enable", "scrollRestoration", "origin", "triggerWillOpenNewWindow", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "button", "preventDefault", "triggerEl", "matches"], "mappings": "uNACO,MAAMA,EAAWA,CAACC,EAAcC,IACvBC,OAAOF,GACpBG,cAGAC,QAAQ,YAAa,KACrBA,QAAQ,WAAY,IACpBA,QAAQ,OAAQ,KAChBA,QAAQ,WAAY,KACLH,GAAY,GCTjBI,EAAgB,SAAAC,GAAC,IAAAC,KAAEA,QAA6B,IAAzBD,EAAyB,CAAE,EAAAA,EAC9D,OAAOE,SAASC,SAAWD,SAASE,QAAUH,EAAOC,SAASD,KAAO,GACtE,ECQaI,EAAsB,SAClCC,EACAC,QAAA,IAAAA,IAAAA,EAAsC,CAAA,GAGtC,MAAMC,EAAqB,CAC1BF,IAFDA,EAAMA,GAAOP,EAAc,CAAEE,MAAM,IAGlCQ,OAAQC,KAAKD,SACbE,OAAQ,UACLJ,GAEJK,QAAQC,UAAUL,EAAM,GAAIF,EAC7B,ECnBaQ,EAAsB,SAClCR,EACAC,QADqB,IAArBD,IAAAA,EAAqB,eACrBC,IAAAA,EAAsC,IAEtCD,EAAMA,GAAOP,EAAc,CAAEE,MAAM,IACnC,MACMO,EAAqB,IADZI,QAAQG,OAA0B,CAAA,EAGhDT,MACAG,OAAQC,KAAKD,SACbE,OAAQ,UACLJ,GAEJK,QAAQI,aAAaR,EAAM,GAAIF,EAChC,ECjBMW,EAAS,IAAIC,QACnB,SAASC,EAAWC,EAAQC,EAAaC,EAAUC,GAC/C,IAAKH,IAAWH,EAAOO,IAAIH,GACvB,OAAO,EAEX,MAAMI,EAAaR,EAAOS,IAAIL,IACvB,IAAIH,QACXD,EAAOU,IAAIN,EAAaI,GACxB,MAAMG,EAASH,EAAWC,IAAIJ,IAAa,IAAIO,IAC/CJ,EAAWE,IAAIL,EAAUM,GACzB,MAAME,EAAUF,EAAOJ,IAAID,GAO3B,OANIH,EACAQ,EAAOG,IAAIR,GAGXK,EAAOI,OAAOT,GAEXO,GAAWV,CACtB,CCXa,MAAAa,EAAgBA,CAK5BC,EACAC,EACAb,EACAc,KAEA,MAAMC,EAAa,IAAIC,gBAGvB,ODaD,SAAkBJ,EAAUC,EAAMb,EAAUc,EAAU,CAAA,GAClD,MAAMG,OAAEA,EAAMC,KAAEA,EAAOC,UAAaL,EACpC,GAAIG,GAAQG,QACR,OAGJ,MAAMC,KAAEA,KAASC,GAA0BR,EAErCf,EAAcmB,aAAgBK,SAAWL,EAAKM,gBAAkBN,EAEhEO,EAAUC,QAA2B,iBAAZZ,EAAuBA,EAAQW,QAAUX,GAClEa,EAAcC,IAChB,MAAMC,EA1Bd,SAAqBD,EAAOhB,GACxB,IAAIkB,EAASF,EAAME,OAInB,GAHIA,aAAkBC,OAClBD,EAASA,EAAOE,eAEhBF,aAAkBG,SAAWL,EAAMM,yBAAyBD,QAAS,CAErE,MAAME,EAAUL,EAAOK,QAAQvB,GAC/B,GAAIuB,GAAWP,EAAMM,cAAcE,SAASD,GACxC,OAAOA,CAEd,CACL,CAc+BE,CAAYT,EAAOhB,GAC1C,GAAIiB,EAAgB,CAChB,MAAMlB,EAAgB2B,OAAOC,OAAOX,EAAO,CAAEC,mBAC7C7B,EAASwC,KAAKzC,EAAaY,GACvBU,IACAtB,EAAY0C,oBAAoB5B,EAAMc,EAAYL,GAClDzB,GAAW,EAAOE,EAAaC,EAAUC,GAEhD,GAECA,EAAQyC,KAAKC,UAAU,CAAE/B,WAAUC,OAAMY,YACpB5B,GAAW,EAAME,EAAaC,EAAUC,IAE/DF,EAAY6C,iBAAiB/B,EAAMc,EAAYL,GAEnDL,GAAQ2B,iBAAiB,QAAS,KAC9B/C,GAAW,EAAOE,EAAaC,EAAUC,EAAM,EAEvD,CC5CC4C,CAAqCjC,EAAUC,EAAMb,EADrDc,EAAU,IAAKA,EAASG,OAAQF,EAAWE,SAEpC,CAAE6B,QAASA,IAAM/B,EAAWgC,QAAO,EChBrC,MAAOC,UAAiBC,IAC7BC,WAAAA,CAAYlE,EAAmBkC,QAAe,IAAfA,IAAAA,EAAeC,SAASgC,SACtDC,MAAMpE,EAAIqE,WAAYnC,EACvB,CAKA,OAAIlC,GACH,OAAOsE,KAAKzE,SAAWyE,KAAKxE,MAC7B,CAOA,kBAAOyE,CAAYC,GAClB,MAAMC,EAAOD,EAAGE,aAAa,SAAWF,EAAGE,aAAa,eAAiB,GACzE,WAAWV,EAASS,EACrB,CAOA,cAAOE,CAAQ3E,GACd,OAAO,IAAIgE,EAAShE,EACrB,QCzBY4E,EAOZV,WAAAA,CAAYW,QALFA,UAAI,EAAAP,KAGJQ,MAAgC,IAAIC,IAG7CT,KAAKO,KAAOA,CACb,CAGA,QAAIG,GACH,YAAYF,MAAME,IACnB,CAGA,OAAIC,GACH,MAAMC,EAAO,IAAIH,IAIjB,OAHAT,KAAKQ,MAAMK,QAAQ,CAACC,EAAMC,KACzBH,EAAK7D,IAAIgE,EAAK,IAAKD,GAAM,GAEnBF,CACR,CAGAhE,GAAAA,CAAIlB,GACH,OAAOsE,KAAKQ,MAAM5D,IAAIoD,KAAKgB,QAAQtF,GACpC,CAGAoB,GAAAA,CAAIpB,GACH,MAAMuF,EAASjB,KAAKQ,MAAM1D,IAAIkD,KAAKgB,QAAQtF,IAC3C,OAAKuF,EACE,IAAKA,GADQA,CAErB,CAGAlE,GAAAA,CAAIrB,EAAaoF,GAChBpF,EAAMsE,KAAKgB,QAAQtF,GACnBoF,EAAO,IAAKA,EAAMpF,OAClBsE,KAAKQ,MAAMzD,IAAIrB,EAAKoF,GACpBd,KAAKO,KAAKW,MAAMC,SAAS,YAAa,CAAEL,QACzC,CAGAM,MAAAA,CAAO1F,EAAa2F,GACnB3F,EAAMsE,KAAKgB,QAAQtF,GACnB,MAAMoF,EAAO,IAAKd,KAAKlD,IAAIpB,MAAS2F,EAAS3F,OAC7CsE,KAAKQ,MAAMzD,IAAIrB,EAAKoF,EACrB,CAGA1D,OAAO1B,GACNsE,KAAKQ,MAAMpD,OAAO4C,KAAKgB,QAAQtF,GAChC,CAGA4F,KAAAA,GACCtB,KAAKQ,MAAMc,QACXtB,KAAKO,KAAKW,MAAMC,SAAS,mBAAeI,EACzC,CAGAC,KAAAA,CAAMC,GACLzB,KAAKQ,MAAMK,QAAQ,CAACC,EAAMpF,KACrB+F,EAAU/F,EAAKoF,IAClBd,KAAK5C,OAAO1B,EACZ,EAEH,CAGUsF,OAAAA,CAAQU,GACjB,MAAMhG,IAAEA,GAAQgE,EAASW,QAAQqB,GACjC,OAAO1B,KAAKO,KAAKoB,WAAWjG,EAC7B,ECpFY,MAAAkG,EAAQ,SAACtE,EAAkBuE,GACvC,gBADuCA,IAAAA,EAA8BhE,UAC9DgE,EAAQC,cAA2BxE,EAC3C,EAGayE,EAAW,SACvBzE,EACAuE,GAEA,YAFA,IAAAA,IAAAA,EAA8BhE,UAEvBmE,MAAMC,KAAKJ,EAAQK,iBAAiB5E,GAC5C,EAGa6E,EAAWA,IAChB,IAAIC,QAASpB,IACnBqB,sBAAsB,KACrBA,sBAAsB,KACrBrB,GAAO,EACP,EACD,GAKa,SAAAsB,EAAaC,GAC5B,QACGA,IACc,iBAARA,GAAmC,mBAARA,IACc,mBAAzCA,EAAgCC,IAE1C,CA0BO,MAAMC,EAAuBC,GAE/BC,OAAOC,KAAOD,OAAOC,IAAIC,OACrBD,IAAIC,OAAOH,GAEZA,EAIKI,EAAQC,GAC8B,IAA3CC,OAAOD,EAAEE,MAAM,GAAI,GAAG/H,QAAQ,IAAK,YChE9BgI,EAIZtD,WAAAA,CAAYW,GAAUP,KAHZO,UAAI,EAAAP,KACJmD,YAAc,CAAC,MAAO,cAAe,eAAgB,cAAe,gBAG7EnD,KAAKO,KAAOA,CACb,CAEA,aAAc6C,GACb,MAAMC,MAAEA,GAAUrD,KAAKO,KAAK+C,MAAMC,UAClC,MAAc,eAAVF,EAA+BrD,KAAKO,KAAK+C,MAAME,WACrC,SAAVH,EAAyB,CAAC,QAC1BrB,MAAMyB,QAAQJ,GAAeA,EAC1B,EACR,CAEA,YAAc/F,GACb,OAAO0C,KAAKoD,UAAUM,KAAK,IAC5B,CAEA,WAAcC,GACb,OAAK3D,KAAK1C,SAASsG,OACZ7B,EAAS/B,KAAK1C,UADa,EAEnC,CAEAH,GAAAA,GACC6C,KAAK2D,QAAQ9C,QAASrC,GAAWA,EAAOqF,UAAU1G,OAAI8F,GAAAA,MAAA/D,KAAA4E,YACvD,CAEAC,MAAAA,GACC/D,KAAK2D,QAAQ9C,QAASrC,GAAWA,EAAOqF,UAAUE,UAAOd,GAAAA,MAAA/D,KAAA4E,YAC1D,CAEAxC,KAAAA,GACCtB,KAAK2D,QAAQ9C,QAASrC,IACrB,MAAMuF,EAASvF,EAAOwF,UAAUC,MAAM,KAAKC,OAAQC,GAAMnE,KAAKoE,YAAYD,IAC1E3F,EAAOqF,UAAUE,UAAUA,EAC5B,EACD,CAEUK,WAAAA,CAAYJ,GACrB,OAAWhE,KAACmD,YAAYkB,KAAMF,GAAMH,EAAUM,WAAWH,GAC1D,EC4Ce,SAAAI,EAAWC,GAE2C,IAArEC,GAAEA,EAAExC,KAAEA,EAAOjC,KAAK0E,eAAcrJ,KAAEA,EAAI6E,GAAEA,EAAE5B,MAAEA,GAAyBkG,EAErE,MAAO,CACNvC,KAAM,CAAEvG,IAAKuG,GACbwC,GAAI,CAAE/I,IAAK+I,EAAIpJ,QACfmI,WAAYxD,KAAKxC,QAAQgG,WACzBD,UAAW,CACVoB,SAAS,EACTC,MAAM,EACNC,UAAMtD,EACN8B,MAAOrD,KAAKxC,QAAQsH,eACpBxH,SAAU0C,KAAKxC,QAAQuH,mBAExBC,QAAS,CACR9E,KACA5B,SAED2G,MAAO,CACNC,KAAMlF,KAAKxC,QAAQyH,MACnBE,MAAOnF,KAAKxC,QAAQyH,OAErBjJ,QAAS,CACRoJ,OAAQ,OACRC,UAAU,EACVC,eAAW/D,GAEZgE,OAAQ,CACPC,OAAO,EACPhH,YAAQ+C,GAGX,CCmRkB,MACAkE,EACM,oBAAAC,OAAAA,OAAAC,WAAAD,OAAAC,SAAAD,OAAA,oBAAA,wBAjQTE,EAAAzJ,EAAA0J,SACF9C,EAAA,IACV8C,aAAAC,EAAA,CAEF,IAAAD,EAAA9C,EAQU,YADP8C,EAAAE,EAAAC,EAAAC,KAAA,KAAAL,EAAAzJ,IANG,EAALA,MACK0J,EAAM9C,GAGZ8C,EAAAA,EAAAK,EAOA,GAAAL,GAAAA,EAAArD,KAEG,0DAEErG,QAEH,MAAAgK,EAAAP,EAAAG,EACDI,GAEDA,EAAAP,EAEG,CACH,CApED,MAAAE,eAAA,sFAKG,GAAA3J,EAAA,CACH,QAAkB,EAAAA,EAAAiK,EAAAC,KAC4B3J,EAAA,CACnC,IAEiCsJ,EAAA/E,EAAA,EAAAvE,EAAAsD,KAAAkG,GACjC,CAAA,MAAyBI,GAEwDN,EAAA/E,EAAA,EAAAqF,EAC3F,CACmB,OAAoBrF,SAEjBjB,mBAGD,SAAAuG,aAEJV,EAAAU,EAAAL,EACH,EAAbK,EAAaxD,IACF9B,EAAA,EAAAmF,EAAAA,EAAAP,GAAAA,GACMQ,IACDpF,EAAA,EAAAoF,EAAAR,MAEP5E,EAAA,EAAA4E,SAEIS,KACKrF,EAAA,EAAAqF,KAGLrF,KAlCf,GAsEE,SAAAuF,EAAAC,GAED,OAAAA,aAAAX,GAAA,EAAAW,EAAA1D,EAlEY,MAAA2D,EAsCZ9G,WAAAA,CAAYW,GApCFA,KAAAA,UAGAoG,EAAAA,KAAAA,SAAyB,IAAIlG,IAIpBS,KAAAA,MAAoB,CACtC,sBACA,sBACA,oBACA,qBACA,qBACA,mBACA,iBACA,cACA,YACA,kBACA,iBACA,SACA,UACA,gBACA,cACA,mBACA,aACA,YACA,cACA,cACA,YACA,YACA,aACA,gBACA,cACA,aAIAlB,KAAKO,KAAOA,EACZP,KAAK4G,MACN,CAKUA,IAAAA,GACT5G,KAAKkB,MAAML,QAASgG,GAAS7G,KAAK8G,OAAOD,GAC1C,CAKAC,MAAAA,CAAOD,GACD7G,KAAK2G,SAAS/J,IAAIiK,IACtB7G,KAAK2G,SAAS5J,IAAI8J,EAAkB,IAAIpG,IAE1C,CAKAsG,MAAAA,CAAOF,GACN,OAAO7G,KAAK2G,SAAS/J,IAAIiK,EAC1B,CAKU/J,GAAAA,CAAwB+J,GACjC,MAAMxK,EAAS2D,KAAK2G,SAAS7J,IAAI+J,GACjC,GAAIxK,EACH,OAAOA,EAER2K,QAAQC,uBAAuBJ,KAChC,CAKAvF,KAAAA,GACCtB,KAAK2G,SAAS9F,QAASxE,GAAWA,EAAOiF,QAC1C,CAsBA4F,EAAAA,CACCL,EACAM,EACA3J,YAAAA,IAAAA,EAAsB,CAAA,GAEtB,MAAMnB,EAAS2D,KAAKlD,IAAI+J,GACxB,IAAKxK,EAEJ,OADA2K,QAAQI,cAAcP,iBACf,OAGR,MAAMQ,EAAKhL,EAAOqE,KAAO,EACnB4G,EAAoC,IAAK9J,EAAS6J,KAAIR,OAAMM,WAGlE,OAFA9K,EAAOU,IAAIoK,EAASG,GAEb,IAAMtH,KAAKuH,IAAIV,EAAMM,EAC7B,CAgBAK,MAAAA,CACCX,EACAM,EACA3J,GAEA,gBAFAA,IAAAA,EAAuB,CAAE,GAEdwC,KAACkH,GAAGL,EAAMM,EAAS,IAAK3J,EAASgK,QAAQ,GACrD,CAgBAtM,OAAAA,CACC2L,EACAM,EACA3J,GAEA,gBAFAA,IAAAA,EAAuB,CAAE,GAEdwC,KAACkH,GAAGL,EAAMM,EAAS,IAAK3J,EAAStC,SAAS,GACtD,CAeA6C,IAAAA,CACC8I,EACAM,EACA3J,GAEA,gBAFAA,IAAAA,EAAuB,CAAA,GAEZwC,KAACkH,GAAGL,EAAMM,EAAS,IAAK3J,EAASO,MAAM,GACnD,CAaAwJ,GAAAA,CAAwBV,EAASM,GAChC,MAAM9K,EAAS2D,KAAKlD,IAAI+J,GACpBxK,GAAU8K,EACG9K,EAAOe,OAAO+J,IAE7BH,QAAQI,0BAA0BP,iBAEzBxK,GACVA,EAAOiF,OAET,CAUMpC,IAAAA,CACL2H,EACAY,EACAC,GAAkC,IAAAnB,MAAAA,EAECvG,MAA7BwH,OAAEA,EAAML,QAAEA,EAAOQ,MAAEA,GAAUpB,EAAKqB,YAAYf,EAAMa,GAAgB,OAAAtF,QAAApB,QACpEuF,EAAKsB,IAAIL,EAAQC,IAAKjF,KAAA,WAAA,OAAAJ,QAAApB,QACLuF,EAAKsB,IAAIV,EAASM,IAAKjF,KAAAgC,SAAAA,OAAvCvD,GAAOuD,EAAA,OAAApC,QAAApB,QACRuF,EAAKsB,IAAIF,EAAOF,IAAKjF,KAC3B+D,WACA,OADAA,EAAKuB,iBAAiBjB,EAAMY,GACrBxG,CAAO,EACf,EAAA,EAAA,CAAC,MAAAqF,GAAAlE,OAAAA,QAAA2F,OAAAzB,EAUDnF,CAAAA,CAAAA,QAAAA,CACC0F,EACAY,EACAC,GAEA,MAAMF,OAAEA,EAAML,QAAEA,EAAOQ,MAAEA,GAAU3H,KAAK4H,YAAYf,EAAMa,GAC1D1H,KAAKgI,QAAQR,EAAQC,GACrB,MAAOxG,GAAUjB,KAAKgI,QAAQb,EAASM,GAGvC,OAFAzH,KAAKgI,QAAQL,EAAOF,GACpBzH,KAAK8H,iBAAiBjB,EAAMY,GACrBxG,CACR,CAagB4G,GAAAA,CACfI,EACAR,GAAsB,IAAAS,MAAAA,EAIuBlI,KAFvCmI,EAAU,GAAG/M,EA6BlB,SAAQoD,EAAW4J,EAAEC,GACrB,GAAuB,mBAAvB7J,EAAaiH,GAAU,CACtB,IACC6C,EAAA1C,EAAAmC,EADDpC,EAAAnH,EAAOiH,KA6BT,GA3BI,SAAA8C,EAAAtH,GAEF,IACD,OAAAqH,EAAQ3C,EAAE6C,QAAAC,MAET,IADAxH,EAAAmH,EAAAE,EAAQzC,SACR5E,EAAAuB,KAAA,CACD,IAAAgE,EAAAvF,eAIFA,EAAAuB,KAAA+F,EAAAR,IAAAA,EAAA/B,EAAAC,KAAA,KAAAL,EAAA,IAAAE,EAAA,KAHC7E,IAAeiF,IASbF,EAAAJ,EAAA,EAAA3E,KAEIA,QAELqF,GACAN,EAAAJ,IAAAA,EAAA,IAAAE,GAAA,EAAAQ,OAMDX,EAAU+C,OAAO,OAEiC,SAAA7C,OAE5CyC,EAAAG,QACAC,eAG6BpC,WAE/BT,CACJ,EACC,GAAAD,GAAAA,EAAUpD,KACV,OAAAoD,EAAIpD,OAAU,SAAA8D,GACb,MAAAqC,EAAArC,QAIC,SAGC,CACA,KAAA,WAAA9H,GACA,MAAA,IAAAoK,UAAA,0BAID,IADD,IAAAC,EAAA,GACCC,EAAA,EAAAA,EAAAtK,EAAOuK,OAAeD,aACrBA,IAEH,OAxJA,SAAAE,EAAAZ,EAAAC,GAAM,IAAAzC,IAAAkD,GAAA,oBACA7H,GACN,WACD6H,EAAAE,EAAAD,YAED9H,EAAAmH,EAAAU,+EAQA,GAOC9C,EAAAJ,EAAO,EAAM3E,GAEb2E,EAAA3E,EAED,MAACqF,GAEDN,EAAAJ,IAAAA,EAAA,IAAAE,GAAA,EAAAQ,SA8HE2C,CAAAJ,EAAA,SAAAC,GAAA,OAAAV,EAAAS,EAAAC,GAAA,EAED,CA5FmBI,CACmCjB,EAAakB,SAAAA,GAAE,IAA1DtC,KAAEA,EAAIM,QAAEA,EAAOO,eAAEA,EAAc3J,KAAEA,GAAMoL,EAAA,OAAA/G,QAAApB,QHrVpC,SAAaoI,EAAgB3B,GAC5C,gBAD4CA,IAAAA,EAAkB,IACvD,IAAIrF,QAAQ,CAACpB,EAAS+G,KAC5B,MAAM9G,EAAkBmI,KAAQ3B,GAC5BnF,EAAUrB,GACbA,EAAOuB,KAAKxB,EAAS+G,GAErB/G,EAAQC,EACR,EAEH,CG6UwBoI,CAAalC,EAAS,CAACe,EAAK3H,KAAK+C,MAAOmE,EAAMC,KAAgBlF,KAAA,SAA7EvB,GACNkH,EAAQmB,KAAKrI,GACTlD,GACHmK,EAAKX,IAAIV,EAAMM,EAAS,EAEzB,GAAA/E,OAAAA,QAAApB,QAAA5F,GAAAA,EAAAoH,KAAApH,EAAAoH,KACD,WAAA,OAAO2F,CAAQ,GAARA,EACR,CAAC,MAAA7B,GAAAlE,OAAAA,QAAA2F,OAAAzB,EAaS0B,CAAAA,CAAAA,OAAAA,CACTC,EACAR,GAEA,MAAMU,EAAU,GAChB,IAAK,MAAMtB,KAAEA,EAAIM,QAAEA,EAAOO,eAAEA,EAAc3J,KAAEA,KAAUkK,EAAe,CACpE,MAAMhH,EAAUkG,EAA8BnH,KAAKO,KAAK+C,MAAOmE,EAAMC,GACrES,EAAQmB,KAAKrI,GACTqB,EAAUrB,IACb+F,QAAQI,KACP,uDAAuDP,4CAIrD9I,GACHiC,KAAKuH,IAAIV,EAAMM,EAEhB,CACD,OAAOgB,CACR,CASUP,WAAAA,CAAgCf,EAASa,GAClD,MAAMrL,EAAS2D,KAAKlD,IAAI+J,GACxB,IAAKxK,EACJ,MAAO,CAAEkN,OAAO,EAAO/B,OAAQ,GAAIL,QAAS,GAAIQ,MAAO,GAAI6B,UAAU,GAGtE,MAAMvB,EAAgBjG,MAAMC,KAAK5F,EAAOwM,UAIlCY,EAAOzJ,KAAK0J,kBAGZlC,EAASS,EAAc/D,OAAOyF,IAAA,IAACnC,OAAEA,EAAMtM,QAAEA,GAASyO,EAAA,OAAKnC,IAAWtM,IAASuO,KAAKA,GAChFvO,EAAU+M,EAAc/D,OAAO0F,IAAC,IAAA1O,QAAEA,GAAS0O,EAAK,OAAA1O,IAASgJ,OALlD2F,IAAwE,GAKVJ,KAAKA,GAC1E9B,EAAQM,EAAc/D,OAAO4F,IAAC,IAAAtC,OAAEA,EAAMtM,QAAEA,GAAS4O,EAAK,OAACtC,IAAWtM,IAASuO,KAAKA,GAChFD,EAAWtO,EAAQ6N,OAAS,EAIlC,IAAI5B,EAAoD,GACxD,GAAIO,IACHP,EAAU,CAAC,CAAEE,GAAI,EAAGR,OAAMM,QAASO,IAC/B8B,GAAU,CACb,MAAMO,EAAQ7O,EAAQ6N,OAAS,EAEzBiB,EAAwBD,IAC7B,MAAMvB,EAAOtN,EAAQ6O,EAAQ,GAC7B,OAAIvB,EACI,CAAClF,EAAOmE,IACde,EAAKrB,QAAQ7D,EAAOmE,EAAMuC,EAAqBD,EAAQ,IAEjDrC,CACP,EAGFP,EAAU,CACT,CAAEE,GAAI,EAAGR,OAAMM,QAZSjM,EAAQ6O,GAAO5C,QAYGO,eAFdsC,EAAqBD,IAIlD,CAGF,MAAO,CAAER,OAAO,EAAM/B,SAAQL,UAASQ,QAAO6B,WAC/C,CAQUE,iBAAAA,CACTO,EACAC,GAIA,OAFkBD,EAAEE,UAAY,IAAMD,EAAEC,UAAY,IACzCF,EAAE5C,GAAK6C,EAAE7C,IACK,CAC1B,CAMUS,gBAAAA,CAAqCjB,EAASY,GAEvD5J,SAASuM,cAAc,IAAIC,YAAY,QAAQxD,IAAQ,CAAEyD,OAD1C,CAAEzD,OAAMY,OAAMnE,MAAOtD,KAAKO,KAAK+C,SAE/C,ECleY,MAAAiH,EAAoBlP,IAKhC,GAJIA,GAA2B,MAAnBA,EAAKmP,OAAO,KACvBnP,EAAOA,EAAKoP,UAAU,KAGlBpP,EACJ,OAAO,KAGR,MAAMqP,EAAUC,mBAAmBtP,GACnC,IAAIuP,EACH/M,SAASgN,eAAexP,IACxBwC,SAASgN,eAAeH,IACxB9I,EAAiB,WAAAiB,EAAOxH,SACxBuG,aAAiBiB,EAAO6H,QAMzB,OAJKE,GAAoB,QAATvP,IACfuP,EAAU/M,SAASuK,MAGbwC,GCbcE,EAAeA,SAAAtG,GAEpC,IAAAuG,SACCA,EAAQzN,SACRA,GAIAkH,EAAA,IAGD,IAAiB,IAAblH,IAAuByN,EAC1B,OAAA3I,QAAApB,UAID,IAAIgK,EAAkC,GACtC,GAAID,EACHC,EAAmBhJ,MAAMC,KAAK8I,QACxB,GAAIzN,IACV0N,EAAmBjJ,EAASzE,EAAUO,SAASuK,OAE1C4C,EAAiBjC,QAErB,OADA/B,QAAQI,8DAA8D9J,OACtE8E,QAAApB,UAIF,MAAMiK,EAAoBD,EAAiBE,IAAKhL,GAcjD,SAAkC0K,GACjC,MAAMrN,KAAEA,EAAI4N,QAAEA,EAAOC,UAAEA,YAiDUR,EAAkBS,GACnD,MAAMC,EAAS3I,OAAO4I,iBAAiBX,GAEjCY,EAAmBC,EAAmBH,EAAW,GAAAI,UACjDC,EAAsBF,EAAmBH,EAAW,GAAAI,aACpDE,EAAoBC,EAAiBL,EAAkBG,GACvDG,EAAkBL,EAAmBH,EAAW,GAAAS,UAChDC,EAAqBP,EAAmBH,EAAW,GAAAS,aACnDE,EAAmBJ,EAAiBC,EAAiBE,GAE3D,IAAIzO,EAA8B,KAC9B4N,EAAU,EACVC,EAAY,EAwBhB,OAtBIC,IAAiBK,EAChBE,EAAoB,IACvBrO,EAAOmO,EACPP,EAAUS,EACVR,EAAYO,EAAoB5C,QAEvBsC,IAAiBU,EACvBE,EAAmB,IACtB1O,EAAOwO,EACPZ,EAAUc,EACVb,EAAYY,EAAmBjD,SAGhCoC,EAAUrP,KAAKoQ,IAAIN,EAAmBK,GACtC1O,EAAO4N,EAAU,EAAKS,EAAoBK,EAAmBP,EAAaK,EAAa,KACvFX,EAAY7N,EACTA,IAASmO,EACRC,EAAoB5C,OACpBiD,EAAmBjD,OACpB,GAGG,CACNxL,OACA4N,UACAC,YAEF,CA1FsCe,CAAkBvB,GAGvD,SAAKrN,IAAS4N,IAIP,IAAI/I,QAASpB,IACnB,MAAMoL,EAAc,GAAA7O,OACd8O,EAAYC,YAAYC,MAC9B,IAAIC,EAAoB,EAExB,MAAMC,EAAMA,KACX7B,EAAQzL,oBAAoBiN,EAAUM,GACtC1L,GAAO,EAGF0L,EAAwBpO,IAE7B,GAAIA,EAAME,SAAWoM,EAArB,CAIA,IAqEH,SAAsCtM,GACrC,MAAO,CAAI,GAAAoN,UAAoBK,QAAgBY,SAASrO,EAAMf,KAC/D,CAvEQqP,CAA6BtO,GACjC,UAAUuO,MAAM,yCAIIP,YAAYC,MAAQF,GAAa,IACpC/N,EAAMwO,eAKlBN,GAAqBpB,GAC1BqB,GAdA,CAeA,EAGFM,WAAW,KACNP,EAAoBpB,GACvBqB,GACA,EACCtB,EAAU,GAEbP,EAAQtL,iBAAiB8M,EAAUM,EAAK,EAE1C,CA9DwDM,CAAyB9M,IAEhF,OADsB+K,EAAkB/G,OAAO9F,SAAS2K,OAAS,EAQhE3G,QAAApB,QAEKoB,QAAQzB,IAAIsK,IAAkBzI,KACrC,WAAA,IATMlF,GACH0J,QAAQI,wEAC4D9J,OAGrE8E,QAAApB,UAIF,CAAC,MAAAsF,GAAA,OAAAlE,QAAA2F,OAAAzB,EAAA,CAAA,EAtDKoF,EAAa,aACbK,EAAY,YAwJlB,SAASN,EAAmBH,EAAoCvK,GAC/D,OAAQuK,EAAOvK,IAAQ,IAAIkD,MAAM,KAClC,CAEA,SAAS4H,EAAiBoB,EAAkBC,GAC3C,KAAOD,EAAOlE,OAASmE,EAAUnE,QAChCkE,EAASA,EAAOE,OAAOF,GAGxB,OAAOnR,KAAKoQ,OAAOgB,EAAUhC,IAAI,CAACkC,EAAUtE,IAAMhG,EAAKsK,GAAYtK,EAAKmK,EAAOnE,KAChF,CC1GsB,MAAAuE,EAAiB,SAEtC7P,YAAAA,IAAAA,EAA4C,IAAE,IAAA+I,MAAAA,EAE/BvG,MAATE,GAAEA,GAAOqG,EAAKjD,MAAM0B,QAC1BxH,EAAQ8P,SAAW9P,EAAQ8P,UAAY/G,EAAK7B,gBAEpB,IAApBlH,EAAQmH,UACX4B,EAAKjD,MAAMC,UAAUoB,SAAU,GAI3B4B,EAAKjD,MAAMC,UAAUoB,SACzB4B,EAAKgH,QAAQjM,QAId,MAAMtF,EAAUwB,EAAQxB,SAAWkE,GAAIE,aAAa,2BAAwBmB,EACxEvF,GAAW,CAAC,OAAQ,WAAW2Q,SAAS3Q,KAC3CuK,EAAKjD,MAAMtH,QAAQoJ,OAASpJ,GAI7B,MAAMuH,EAAY/F,EAAQ+F,WAAarD,GAAIE,aAAa,6BAA0BmB,EAa7D,OAZjBgC,IACHgD,EAAKjD,MAAMC,UAAUsB,KAAOtB,GAIA,iBAAlB/F,EAAQyH,OAClBsB,EAAKjD,MAAM2B,MAAMC,KAAO1H,EAAQyH,MAAMC,MAAQqB,EAAKjD,MAAM2B,MAAMC,KAC/DqB,EAAKjD,MAAM2B,MAAME,MAAQ3H,EAAQyH,MAAME,OAASoB,EAAKjD,MAAM2B,MAAME,YACrC5D,IAAlB/D,EAAQyH,QAClBsB,EAAKjD,MAAM2B,MAAQ,CAAEC,OAAQ1H,EAAQyH,MAAOE,QAAS3H,EAAQyH,eAGvDzH,EAAQyH,MAAM7C,QAAApB,gCAEjBoB,QAAApB,QACGuF,EAAKrF,MAAMhC,KAAK,mBAAeqC,IAAUiB,KAAAgL,WAAAA,SAAAA,IAwC/C,MAAMC,EAAmBlH,EAAKmH,iBAAiB,OAAAtL,QAAApB,QAC1BoB,QAAQzB,IAAI,CAACgN,EAAaF,KAAkBjL,KAAA,SAAAgC,GAAA,IAA1D1D,GAAK0D,EAAApC,OAAAA,QAAApB,QAGNuF,EAAKqH,WAAWrH,EAAKjD,MAAMmB,GAAG/I,IAAKoF,IAAK0B,KAAAJ,WAAAA,OAAAA,QAAApB,QAGxCuF,EAAKsH,iBAAerL,KAAAJ,WAAAA,OAAAA,QAAApB,QAGpBuF,EAAKrF,MAAMhC,KAAK,iBAAaqC,EAAW,IAAMgF,EAAKgH,QAAQjM,UAAQkB,KA/CzE,WAAA,EAAA,EAAA,EAAA,EAAA,CAAA,MAAMmL,EAAcpH,EAAKrF,MAAMhC,KAAK,YAAa,CAAE1B,oBAAkB8F,EAAOmE,GAAQ,IAAA,SAAAqG,EAAAC,GAUnF,OAHAtG,EAAK3G,KAAIiN,EACTtG,EAAKxC,QAAU+I,EAERvG,EAAK3G,IAAK,CARjB,IAAIkN,EAKkB,OAJlBzH,EAAKjD,MAAM2B,MAAMC,OACpB8I,EAAazH,EAAKtB,MAAMnI,IAAIwG,EAAMmB,GAAG/I,MAGhB0G,QAAApB,QAAVgN,EAAUF,EAAVE,GAAU5L,QAAApB,QAAWuF,EAAK0H,UAAU3K,EAAMmB,GAAG/I,IAAK+L,EAAKjK,UAAQgF,KAAAsL,GAI5E,CAAC,MAAAxH,GAAA,OAAAlE,QAAA2F,OAAAzB,EAAC,CAAA,GAGF,IAAKC,EAAKjD,MAAMtH,QAAQqJ,SAAU,CAEjC,MAAM6I,EAAS3H,EAAKjD,MAAMmB,GAAG/I,IAAM6K,EAAKjD,MAAMmB,GAAGpJ,KAElB,YAA9BkL,EAAKjD,MAAMtH,QAAQoJ,QACnBmB,EAAKjD,MAAMmB,GAAG/I,MAAQ6K,EAAK7B,eAE3BxI,EAAoBgS,GAGpBzS,EAAoByS,EAAQ,CAAEnE,MADhBxD,EAAK4H,oBAAsB,GAG1C,CAED5H,EAAK7B,eAAiBvJ,IAAgB,MAAAC,EAAA,WAAA,GAGlCmL,EAAKjD,MAAMC,UAAUqB,KAAIxC,OAAAA,QAAApB,QACL2M,GAAWnL,KAAA,SAAA2G,GAAA,IAA5BiF,KAAEA,GAAMjF,EACd5C,EAAKjD,MAAMmB,GAAG2J,KAAOA,CAAK,EAAA,CALW,GAKX,OAAAhT,GAAAA,EAAAoH,KAAApH,EAAAoH,KAAAgL,GAAAA,GAAA,4DAvCPa,CAAA,EA2DpB,SAAQpH,GAEHA,IAKLD,QAAQC,MAAMA,GAGdV,EAAK/I,QAAQ8Q,qBAAuB,KACnC3L,OAAOrH,SAAS6E,KAAOoG,EAAKjD,MAAMmB,GAAG/I,IAAM6K,EAAKjD,MAAMmB,GAAGpJ,MAClD,GAIRsH,OAAO3G,QAAQuS,IAAI,GACnB,GACF,CAAC,MAAAjI,GAAA,OAAAlE,QAAA2F,OAAAzB,EAjJD,CAAA,EAAgB,SAAAkI,EAEf9S,EACA8B,EACAoJ,GAEA,QAH4C,IAA5CpJ,IAAAA,EAA4C,CAAE,QAC9C,IAAAoJ,IAAAA,EAAqC,CAAA,GAElB,iBAARlL,EACV,MAAU,IAAAmR,MAAM,4CAIjB,GAAI7M,KAAKyO,kBAAkB/S,EAAK,CAAEwE,GAAI0G,EAAK1G,GAAI5B,MAAOsI,EAAKtI,QAE1D,YADAqE,OAAOrH,SAAS6E,KAAOzE,GAIxB,MAAQA,IAAK+I,EAAEpJ,KAAEA,GAASqE,EAASW,QAAQ3E,GAC3CsE,KAAKsD,MAAQtD,KAAKuE,YAAY,IAAKqC,EAAMnC,KAAIpJ,SAC7C2E,KAAKqN,kBAAkB7P,EACxB,CCdA,MAAsByQ,EAAS,SAE9BvS,EACA8B,YAAAA,IAAAA,EAAwB,IAAE,UAAA+I,EAILvG,KAFrBtE,EAAMgE,EAASW,QAAQ3E,GAAKA,IAE5B,MAAMgT,EAAU,IAAKnI,EAAK/I,QAAQmR,kBAAmBnR,EAAQkR,SAC3B,OAAlClR,EAAU,IAAKA,EAASkR,WAAUtM,QAAApB,QAGDuF,EAAKrF,MAAMhC,KAC3C,gBACA,CAAExD,MAAK8B,WACP,CAAC8F,EAAKkB,KAAA,IAAE9I,IAAEA,EAAG8B,QAAEA,GAASgH,EAAA,OAAKoK,MAAMlT,EAAK8B,EAAO,IAC/CgF,KAJKqM,SAAAA,GAMN,MAAMC,OAAEA,EAAQpT,IAAKqT,GAAgBF,EAAS,OAAAzM,QAAApB,QAC3B6N,EAAS/T,QAAM0H,KAA5B4L,SAAAA,GAEN,GAAe,MAAXU,EAEH,MADAvI,EAAKrF,MAAMhC,KAAK,cAAe,CAAE4P,SAAQD,WAAUnT,IAAKqT,IAC9C,IAAAC,EAAW,iBAAiBD,IAAe,CAAED,SAAQpT,IAAKqT,IAGrE,IAAKX,EACJ,MAAM,IAAIY,EAAW,mBAAmBD,IAAe,CAAED,SAAQpT,IAAKqT,IAIvE,MAAQrT,IAAKuT,GAAavP,EAASW,QAAQ0O,GACrCjO,EAAO,CAAEpF,IAAKuT,EAAUb,QAW9B,OAPC7H,EAAKjD,MAAM2B,MAAME,OACf3H,EAAQ0R,QAA6B,QAAnB1R,EAAQ0R,QAC5BxT,IAAQuT,GAER1I,EAAKtB,MAAMlI,IAAI+D,EAAKpF,IAAKoF,GAGnBA,CAAK,EAAA,EACb,CAAC,MAAAwF,GAAAlE,OAAAA,QAAA2F,OAAAzB,EAAA,CAAA,EAzDY,MAAA0I,UAAmBnC,MAG/BjN,WAAAA,CAAYuP,EAAiBC,GAC5BtP,MAAMqP,GAASnP,KAHhBtE,SACAoT,EAAAA,KAAAA,cAGC9O,KAAK6E,KAAO,aACZ7E,KAAKtE,IAAM0T,EAAQ1T,IACnBsE,KAAK8O,OAASM,EAAQN,MACvB,ECpBY,MAAApB,EAAc,WAAA,IAAQ2B,IAAAA,EAAA9I,MAAAA,EAC7BvG,KAAIwN,SAAAA,EAAA8B,GAAA,OAAAD,EAAAC,EAAAlN,QAAApB,QAKHuF,EAAKrF,MAAMhC,KAAK,2BAAuBqC,EAAY+B,IACxDiD,EAAKgH,QAAQpQ,IAAI,cAAe,aAAc,gBAC1CmG,EAAMtH,QAAQqJ,UACjBkB,EAAKgH,QAAQpQ,IAAI,eAEdmG,EAAMC,UAAUsB,MACnB0B,EAAKgH,QAAQpQ,UAAUtC,EAASyI,EAAMC,UAAUsB,QAChD,IACArC,KAAA,WAAA,OAAAJ,QAAApB,QAEIuF,EAAKrF,MAAMhC,KAAK,sBAAuB,CAAEqQ,MAAM,GAAO,SAASjM,EAAKkB,GAAA,IAAE+K,KAAEA,GAAM/K,EAAI,IACvF,OAAI+K,EAAMnN,QAAApB,UAAOoB,QAAApB,QACXuF,EAAKuE,gBAAgB,CAAExN,SAAUgG,EAAMC,UAAUjG,YAAWkF,KAAA,WAAA,EACnE,CAAC,MAAA8D,GAAA,OAAAlE,QAAA2F,OAAAzB,EAAA,CAAA,IAAC9D,KAAAJ,WAAAA,OAAAA,QAAApB,QAEIuF,EAAKrF,MAAMhC,KAAK,yBAAqBqC,IAAUiB,KAAA,aAAA,EAAA,EAAA,CAAA,MAAApH,EAAA,WAAA,IApBhDmL,EAAKjD,MAAMC,UAAUoB,QAAOvC,OAAAA,QAAApB,QAC1BuF,EAAKrF,MAAMhC,KAAK,sBAAkBqC,IAAUiB,KAAA,WAAA6M,EAAA,CAAA,EAAA,CAmBE,GAnBF,OAAAjN,QAAApB,QAAA5F,GAAAA,EAAAoH,KAAApH,EAAAoH,KAAAgL,GAAAA,EAAApS,GAoBpD,CAAC,MAAAkL,GAAA,OAAAlE,QAAA2F,OAAAzB,EAAA,CAAA,ECjBYkJ,EAAiB,SAAAhL,EAAApJ,GAE7B,IAAAgT,KAAEA,GAAgB5J,GAClBhB,WAAEA,QAAsD,IAAApI,EAAA4E,KAAKxC,QAAOpC,EAEpE,MAAMqU,GAAmB,IAAIC,WAAYC,gBAAgBvB,EAAM,aAGzDwB,EAAQH,EAAiB3N,cAAc,UAAU+N,WAAa,GACpEhS,SAAS+R,MAAQA,EAGjB,MAAME,EAAoB/N,EAAS,mDAG7ByH,EAAWhG,EACf0H,IAAK5N,IACL,MAAMyS,EAAYlS,SAASiE,cAAcxE,GACnC0S,EAAaP,EAAiB3N,cAAcxE,GAClD,OAAIyS,GAAaC,GAChBD,EAAUE,YAAYD,IAEtB,IACID,GACJ/I,QAAQI,sDAAsD9J,KAE1D0S,GACJhJ,QAAQI,uDAAuD9J,MAEzD,KAEP4G,OAAO9F,SAWT,OARA0R,EAAkBjP,QAASqP,IAC1B,MAAMnP,EAAMmP,EAAS9P,aAAa,qBAC5B+P,EAAcvO,yBAA6Bb,OAC7CoP,GAAeA,IAAgBD,GAClCC,EAAYF,YAAYC,EACxB,GAGK1G,EAAST,SAAWvF,EAAWuF,MACvC,ECjDaqH,EAAkB,WAC9B,MAAM5S,EAAiC,CAAE6S,SAAU,SAC7C7R,OAAEA,EAAMgH,MAAEA,GAAUxF,KAAKsD,MAAMiC,OAC/B+K,EAAe9R,GAAUwB,KAAKsD,MAAMmB,GAAGpJ,KAE7C,IAAIkV,GAAW,EAuBf,OArBID,IACHC,EAAWvQ,KAAKkB,MAAMC,SACrB,gBACA,CAAE9F,KAAMiV,EAAc9S,WACtB,CAAC8F,EAAKkB,SAAEnJ,KAAEA,EAAImC,QAAEA,GAASgH,EACxB,MAAMgM,EAASxQ,KAAKuK,iBAAiBlP,GAIrC,OAHImV,GACHA,EAAOC,eAAejT,KAEdgT,KAKRhL,IAAU+K,IACbA,EAAWvQ,KAAKkB,MAAMC,SAAS,aAAc,CAAE3D,WAAW,CAAC8F,EAAK6F,KAAiB,IAAf3L,QAAEA,GAAS2L,EAE5E,OADAxG,OAAO+N,SAAS,CAAEC,IAAK,EAAGC,KAAM,KAAMpT,UAKjC+S,CACR,EC5Ba1C,EAAa,WAAA,UAAQtH,EAC5BvG,KAAL,IAAKuG,EAAKjD,MAAMC,UAAUoB,QACzB,OAAAvC,QAAApB,UAGD,MAAMuC,EAAYgD,EAAKrF,MAAMhC,KAC5B,qBACA,CAAEqQ,MAAM,GAAO,SACRjM,EAAKkB,GAAE,IAAA+K,KAAEA,GAAM/K,EAAI,IACzB,OAAI+K,EAAMnN,QAAApB,UAAOoB,QAAApB,QACXuF,EAAKuE,gBAAgB,CAAExN,SAAUgG,EAAMC,UAAUjG,YAAWkF,KACnE,WAAA,EAAA,CAAC,MAAA8D,GAAA,OAAAlE,QAAA2F,OAAAzB,MACA,OAAAlE,QAAApB,QAEImB,KAAUK,KAAAJ,WAAAA,OAAAA,QAAApB,QAEVuF,EAAKrF,MAAMhC,KAAK,0BAAsBqC,EAAW,KACtDgF,EAAKgH,QAAQxJ,OAAO,eAAc,IACjCvB,KAAAJ,WAAAA,OAAAA,QAAApB,QAEIuC,GAASf,KAAAJ,WAAAA,OAAAA,QAAApB,QAETuF,EAAKrF,MAAMhC,KAAK,wBAAoBqC,IAAUiB,KACrD,WAAA,EAAA,EAAA,EAAA,EAAA,CAAC,MAAA8D,GAAA,OAAAlE,QAAA2F,OAAAzB,EAAA,CAAA,ECtBYsH,EAAUA,SAA+BiD,EAAsB/P,GAAc,IAAA,MAAAyF,EAGzFvG,MAFMtE,IAAEA,EAAG0S,KAAEA,GAAStN,EAKtB,OAHAyF,EAAKgH,QAAQxJ,OAAO,cAGfwC,EAAKuK,kBAAkB3V,IAAiB0V,IAKxCtK,EAAKuK,kBAAkB3V,IAAiBO,KAC5CQ,EAAoBR,GACpB6K,EAAK7B,eAAiBvJ,IACtBoL,EAAKjD,MAAMmB,GAAG/I,IAAM6K,EAAK7B,gBAItB6B,EAAKjD,MAAMC,UAAUoB,SACxB4B,EAAKgH,QAAQpQ,IAAI,gBAIlBoJ,EAAKjD,MAAMmB,GAAG2J,KAAOA,EAAKhM,QAAApB,QAGpBuF,EAAKrF,MAAMhC,KAAK,kBAAmB,CAAE4B,QAAQ,CAACwC,EAAKkB,SAAE1D,KAAEA,GAAM0D,EAElE,IADgB+B,EAAKiJ,eAAe1O,EAAM,CAAE0C,WAAYF,EAAME,aAE7D,MAAU,IAAAqJ,MAAM,uCAEbvJ,EAAMC,UAAUoB,UAEnB4B,EAAKgH,QAAQpQ,IAAI,eAAgB,cAAe,gBAC5CmG,EAAMC,UAAUsB,MACnB0B,EAAKgH,QAAQpQ,UAAUtC,EAASyI,EAAMC,UAAUsB,SAEjD,IACArC,KAAA,WAAA,OAAAJ,QAAApB,QAIIuF,EAAKrF,MAAMhC,KAAK,sBAAkBqC,EAAW,IAC3CgF,EAAK6J,oBACX5N,KAAA,WAAA,OAAAJ,QAAApB,QAEIuF,EAAKrF,MAAMhC,KAAK,YAAa,CAAExD,IAAK6K,EAAK7B,eAAgBkL,MAAO/R,SAAS+R,SAAQpN,KAAA,WAAA,EAAA,EAAA,IAvCtFJ,QAAApB,SAwCF,CAAC,MAAAsF,GAAAlE,OAAAA,QAAA2F,OAAAzB,EAAA,CAAA,EC3BYyK,EAAM,SAAsBC,GANnBC,MAOrB,GAPqBA,EAOHD,EALX5S,QAAQ6S,GAAoBC,eAWnC,GADAF,EAAOzQ,KAAOP,MACVgR,EAAOG,oBACLH,EAAOG,qBAWb,OAPIH,EAAOI,cACVJ,EAAOI,eAERJ,EAAOK,QAEPrR,KAAKsR,QAAQhI,KAAK0H,GAEXhR,KAAKsR,aAjBXtK,QAAQC,MAAM,6BAA8B+J,EAkB9C,EAGgB,SAAAO,EAAkBC,GACjC,MAAMR,EAAShR,KAAKyR,WAAWD,GAC/B,GAAKR,EAYL,OAPAA,EAAOU,UACHV,EAAOW,eACVX,EAAOW,gBAGR3R,KAAKsR,QAAUtR,KAAKsR,QAAQpN,OAAQ0N,GAAMA,IAAMZ,GAEzChR,KAAKsR,QAXXtK,QAAQC,MAAM,iBAAkB+J,EAYlC,CAGM,SAAUS,EAAuBD,GACtC,OAAWxR,KAACsR,QAAQO,KAClBb,GACAA,IAAWQ,GACXR,EAAOnM,OAAS2M,GAChBR,EAAOnM,OAAgB,OAAA7J,OAAOwW,KAEjC,CCrEM,SAAU7P,EAAuBjG,GACtC,GAAuC,mBAAxBsE,KAACxC,QAAQmE,WAEvB,OADAqF,QAAQI,KAAK,0DACN1L,EAER,MAAMuF,EAASjB,KAAKxC,QAAQmE,WAAWjG,GACvC,OAAKuF,GAA4B,iBAAXA,EAIlBA,EAAOqD,WAAW,OAASrD,EAAOqD,WAAW,SAChD0C,QAAQI,KAAK,4DACN1L,GAEDuF,GAPN+F,QAAQI,KAAK,mDACN1L,EAOT,CAQgB,SAAAoV,EAA8BgB,EAAcC,GAC3D,OAAW/R,KAAC2B,WAAWmQ,KAAU9R,KAAK2B,WAAWoQ,EAClD,CCqBA,MAAMC,EAAoB,CACzBC,wBAAwB,EACxBlN,kBAAmB,yBACnBD,eAAgB,OAChBG,OAAO,EACPzB,WAAY,CAAC,SACb0O,YAAa,SAACxW,EAAGN,GAAA,IAAE8E,GAAEA,QAAO,IAAH9E,EAAG,CAAA,EAAEA,EAAK,QAAE8E,GAAIrB,QAAQ,iBAAiB,EAClEsT,aAAc,UACdC,WAAY,SACZd,QAAS,GACT3P,WAAajG,GAAQA,EACrBiT,eAAgB,CACf,mBAAoB,OACpB0D,OAAU,oCAEX/D,qBAAuBhQ,GAAoD,SAAzCA,EAAMnC,OAAwBJ,eAI5C,MA8DpB6D,WAAAA,CAAYpC,QAAAA,IAAAA,IAAAA,EAA4B,CAAA,GA5D/B8U,KAAAA,qBAET9U,aAAO,EAAAwC,KAEEgS,SAAoBA,EAE7BV,KAAAA,QAAoB,QAEpBhO,WAAK,EAAAtD,KAEIiF,WAEA/D,EAAAA,KAAAA,kBAEAqM,aAAO,EAAAvN,KAEhB0E,eAAyBvJ,IAEfgT,KAAAA,oBAAsB,EAACnO,KAEvBuS,mBAAa,EAAAvS,KAGvB+Q,IAAMA,EAENQ,KAAAA,MAAQA,EAAKvR,KAEbyR,WAAaA,EAGbe,KAAAA,IAAoD,OAAQxS,KAG5DwO,SAAWA,EAEDnB,KAAAA,kBAAoBA,OAEpB9I,YAAcA,EAAWvE,KAEnC3C,cAAgBA,EAEhB4Q,KAAAA,UAAYA,EAASjO,KAErB8K,gBAAkBA,EACR8C,KAAAA,WAAaA,OAEvB4B,eAAiBA,EAAcxP,KACrB6N,cAAgBA,EAChBH,KAAAA,eAAiBA,EAAc1N,KAC/BoQ,gBAAkBA,EAE5B7F,KAAAA,iBAAmBA,EAAgBvK,KAGnC7E,cAAgBA,EAEhBwG,KAAAA,WAAaA,EAAU3B,KAEb8Q,kBAAoBA,EAI7B9Q,KAAKxC,QAAU,IAAKwC,KAAKgS,YAAaxU,GAEtCwC,KAAKyS,gBAAkBzS,KAAKyS,gBAAgBxM,KAAKjG,MACjDA,KAAK0S,eAAiB1S,KAAK0S,eAAezM,KAAKjG,MAE/CA,KAAKiF,MAAQ,IAAI3E,EAAMN,MACvBA,KAAKuN,QAAU,IAAIrK,EAAQlD,MAC3BA,KAAKkB,MAAQ,IAAIwF,EAAM1G,MACvBA,KAAKsD,MAAQtD,KAAKuE,YAAY,CAAEE,GAAI,KAE/BzE,KAAK2S,qBAIV3S,KAAK4S,QACN,CAEUD,iBAAAA,GACT,MAAuB,oBAAZvQ,UACV4E,QAAQI,KAAK,6BAEb,EAEF,CAGMwL,MAAAA,GAAM,IAAA,MAAArM,EAEcvG,MAAnBmS,aAAEA,GAAiB5L,EAAK/I,QAoBU,OAnBxC+I,EAAKgM,cAAgBhM,EAAKlJ,cAAc8U,EAAc,QAAS5L,EAAKkM,iBAEpE9P,OAAOrD,iBAAiB,WAAYiH,EAAKmM,gBAGrCnM,EAAK/I,QAAQyU,yBAChBtP,OAAO3G,QAAQ6W,kBAAoB,UAUpCtM,EAAK/I,QAAQ8T,QAAQzQ,QAASmQ,GAAWzK,EAAKwK,IAAIC,IAGlD9U,EAAoB,KAAM,CAAE6N,MAAO,IAAK3H,QAAApB,QAGlCmB,KAAUK,KAAAJ,WAAAA,OAAAA,QAAApB,QAGVuF,EAAKrF,MAAMhC,KAAK,cAAUqC,EAAW,KAE1C1D,SAASK,gBAAgB2F,UAAU1G,IAAI,eACxC,IAAEqF,KAAA,WAAA,EAAA,EACH,CAAC,MAAA8D,GAAAlE,OAAAA,QAAA2F,OAAAzB,EAAA,CAAA,CAGK9G,OAAAA,GAAO,IAAA0I,MAAAA,EAEZlI,KAS6D,OAT7DkI,EAAKqK,cAAe/S,UAGpBmD,OAAOxD,oBAAoB,WAAY+I,EAAKwK,gBAG5CxK,EAAKjD,MAAM3D,QAGX4G,EAAK1K,QAAQ8T,QAAQzQ,QAASmQ,GAAW9I,EAAKqJ,MAAMP,IAAS5O,QAAApB,QAGvDkH,EAAKhH,MAAMhC,KAAK,eAAWqC,EAAW,KAE3C1D,SAASK,gBAAgB2F,UAAUE,OAAO,eAC3C,IAAEvB,KAGF0F,WAAAA,EAAKhH,MAAMI,OAAQ,EACpB,CAAC,MAAAgF,UAAAlE,QAAA2F,OAAAzB,EAGDmI,CAAAA,CAAAA,iBAAAA,CAAkBtO,EAAYqN,OAAEtN,GAAEA,EAAE5B,MAAEA,cAA2C,CAAE,EAAAkP,EAClF,MAAMsF,OAAEA,EAAMpX,IAAEA,EAAGL,KAAEA,GAASqE,EAASW,QAAQF,GAG/C,OAAI2S,IAAWnQ,OAAOrH,SAASwX,WAK3B5S,IAAMF,KAAK+S,yBAAyB7S,OAKpCF,KAAKxC,QAAQ0U,YAAYxW,EAAML,EAAM,CAAE6E,KAAI5B,SAMhD,CAEUmU,eAAAA,CAAgBnU,GACzB,MAAM4B,EAAK5B,EAAMC,gBACX4B,KAAEA,EAAIzE,IAAEA,EAAGL,KAAEA,GAASqE,EAASO,YAAYC,GAG7CF,KAAKyO,kBAAkBtO,EAAM,CAAED,KAAI5B,YAIvC0B,KAAKsD,MAAQtD,KAAKuE,YAAY,CAAEE,GAAI/I,EAAKL,OAAM6E,KAAI5B,UAG/CA,EAAM0U,SAAW1U,EAAM2U,SAAW3U,EAAM4U,UAAY5U,EAAM6U,OAC7DnT,KAAKkB,MAAMhC,KAAK,cAAe,CAAEiB,SAKb,IAAjB7B,EAAM8U,QAIVpT,KAAKkB,MAAMC,SAAS,aAAc,CAAEjB,KAAI5B,SAAS,KAChD,MAAM2D,EAAOjC,KAAKsD,MAAMrB,KAAKvG,KAAO,GAEpC4C,EAAM+U,iBAGD3X,GAAOA,IAAQuG,EAuBhBjC,KAAK8Q,kBAAkBpV,EAAKuG,IAKhCjC,KAAKqN,oBA3BAhS,EAEH2E,KAAKkB,MAAMC,SAAS,cAAe,CAAE9F,QAAQ,KAC5Ca,EAAoBR,EAAML,GAC1B2E,KAAKoQ,iBACN,GAGApQ,KAAKkB,MAAMC,SAAS,iBAAaI,EAAW,IAErC,aADEvB,KAAKxC,QAAQ4U,WAEZpS,KAAKqN,yBAGA+C,kBAclB,GACD,CAEUsC,cAAAA,CAAepU,GACxB,MAAM6B,EAAgB7B,EAAMnC,OAAwBT,KAAOJ,SAAS6E,KAGpE,GAAIH,KAAKxC,QAAQ8Q,qBAAqBhQ,GACrC,OAID,GAAI0B,KAAK8Q,kBAAkB3V,IAAiB6E,KAAK0E,gBAChD,OAGD,MAAMhJ,IAAEA,EAAGL,KAAEA,GAASqE,EAASW,QAAQF,GAEvCH,KAAKsD,MAAQtD,KAAKuE,YAAY,CAAEE,GAAI/I,EAAKL,OAAMiD,UAG/C0B,KAAKsD,MAAMtH,QAAQqJ,UAAW,EAG9B,MAAM0E,EAAQ/G,OAAQ1E,EAAMnC,OAAwB4N,OAChDA,IAEH/J,KAAKsD,MAAMtH,QAAQsJ,UADDyE,EAAQ/J,KAAKmO,oBAAsB,EAAI,WAAa,aAKvEnO,KAAKsD,MAAMC,UAAUoB,SAAU,EAC/B3E,KAAKsD,MAAMiC,OAAOC,OAAQ,EAC1BxF,KAAKsD,MAAMiC,OAAO/G,QAAS,EAGvBwB,KAAKxC,QAAQyU,yBAChBjS,KAAKsD,MAAMC,UAAUoB,SAAU,EAC/B3E,KAAKsD,MAAMiC,OAAOC,OAAQ,GAQ3BxF,KAAKkB,MAAMC,SAAS,mBAAoB,CAAE7C,SAAS,KAClD0B,KAAKqN,mBACN,EACD,CAGU0F,wBAAAA,CAAyBO,GAClC,QAAIA,EAAUC,QAAQ,gCAIvB"}