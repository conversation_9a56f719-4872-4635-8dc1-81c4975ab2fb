export function initMasonry() {
  console.log("🚀 initMasonry 函数开始执行");

  var loadingPlaceholder = document.querySelector(".loading-placeholder");
  var masonryContainer = document.querySelector("#masonry-container");

  if (!loadingPlaceholder || !masonryContainer) {
    return;
  }

  loadingPlaceholder.style.display = "block";
  masonryContainer.style.display = "none";

  // 获取所有图片元素
  var images = document.querySelectorAll("#masonry-container .masonry-item img");

  // 处理懒加载图片
  function processLazyImages() {

    for (var i = 0; i < images.length; i++) {
      var img = images[i];
      var dataSrc = img.getAttribute('data-src');
      var currentSrc = img.src;

      console.log(`📷 图片 ${i+1}:`, {
        hasDataSrc: !!dataSrc,
        hasSrc: !!currentSrc,
        isLazyload: img.classList.contains('lazyload'),
        complete: img.complete
      });

      // 如果是懒加载图片且还没有设置真实src
      if (img.classList.contains('lazyload') && dataSrc && !currentSrc) {
        console.log(`📷 图片 ${i+1} 是懒加载，设置真实src`);
        //img.src = dataSrc;
        //img.removeAttribute('data-src');
        img.classList.remove('lazyload');
      }
    }
  }

  // 处理图片
  processLazyImages();

  // 布局初始化函数
  function initializeMasonryLayout() {

    loadingPlaceholder.style.opacity = 0;

    setTimeout(() => {
      loadingPlaceholder.style.display = "none";
      masonryContainer.style.display = "block";

      var baseWidth = window.innerWidth >= 768 ? 255 : 150;

      var masonry = new MiniMasonry({
        baseWidth: baseWidth,
        container: masonryContainer,
        gutterX: 10,
        gutterY: 10,
        surroundingGutter: false,
      });

      masonry.layout();

      masonryContainer.style.opacity = 1;
    }, 100);
  }

  initializeMasonryLayout();
}


if (typeof data !== 'undefined' && data.masonry) {
  try {
    swup.hooks.on("page:view", ()=>{
      console.log("🔄 Swup page:view 事件触发");
      initMasonry();
      console.log(window.lazySizes);
      console.log(window)
      // if () {
      //   console.log("✅ lazySizes initialized");
      //   window.lazySizes.init();
      // }
    });
  } catch (e) {
    console.log("❌ Masonry swup init failed: " + e);
  }

  document.addEventListener("DOMContentLoaded", initMasonry());
}
//# sourceMappingURL=masonry.js.map