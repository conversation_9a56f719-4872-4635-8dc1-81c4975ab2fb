.markdown-body
  font-family $english-font-family, $chinese-font-family, sans-serif
  font-size $default-article-font-size

  blockquote
    p, ul, ol
      padding 5px 5px 5px 10px

    box-sizing border-box
    margin 20px 0
    color var(--default-text-color)
    background var(--third-background-color)
    padding 0 1rem 0 0.5rem
    border-radius 0 $redefine-border-radius-medium $redefine-border-radius-medium 0
    position relative
    border-left 5px solid var(--default-text-color)

  p
    line-height $default-line-height
    color var(--default-text-color)
    font-weight 400
    margin-bottom 1rem
    margin-top 0

  a
    position relative
    outline 0
    text-decoration underline
    overflow-wrap break-word
    cursor pointer
    // border-bottom 1px solid var(--third-text-color)
    box-sizing border-box
    padding-bottom 2px
    text-underline-offset 2px
    text-decoration-color var(--fourth-text-color)
    transition: text-decoration-color 0.2s ease

    &.link
      padding-right 0.1em

    .fas, .far, fab
      margin 0 2px 0 6px
      position relative
      color var(--third-text-color)
      font-size 0.88rem

    .link-icon
      transition transform 0.2s ease

    &:hover,
    &:focus
      text-decoration underline !important
      text-decoration-color var(--primary-color) !important

      .link-icon
        transform translate(0.1em, -0.1em)

      &::after
        background var(--primary-color)
        text-decoration underline



  strong
    color var(--default-text-color)

  hr
    margin 2rem 0
    border 0
    border-top 2px solid var(--shadow-color-1)

  em
    color var(--default-text-color)

  ul > li, ol > li
    margin-left 20px
    line-height 2rem

  ul
    margin-bottom 1.2rem

    li
      list-style disc

      ul
        li
          list-style circle

          ul
            li
              list-style square

  ol
    margin-bottom 1.2rem

    li
      list-style decimal

      ul
        li
          list-style disc

        ul
          li
            list-style circle

      ol
        li
          list-style upper-alpha

          ol
            li
              list-style upper-roman

  li
    color var(--default-text-color)

  h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6
    font-family $english-font-family, $chinese-font-family, sans-serif
    color var(--second-text-color)
    line-height $default-line-height

    +redefine-tablet()
      line-height: $default-line-height * 0.96

  h1, .h1
    font-size 3.2rem
    font-weight 650
    border-bottom solid 2px var(--fourth-text-color)
    margin-top $h1-heading-margin-top
    margin-bottom 1rem
    letter-spacing -0.025em
    line-height 1.2

    +redefine-tablet()
      font-size 1.7rem
      line-height 1.2

  h2, .h2
    font-size 2.5rem
    font-weight 650
    padding 3px 0
    border-bottom solid 2px var(--fourth-text-color)
    margin-top $h2-heading-margin-top
    margin-bottom 1rem
    letter-spacing -0.025em
    line-height 1.1

    +redefine-tablet()
      font-size 1.6rem
      line-height 1.1

  h3, .h3
    font-size 1.8rem
    font-weight 600
    margin-top $h3-heading-margin-top
    margin-bottom 0.75rem
    letter-spacing -0.025em
    line-height 1.3

    +redefine-tablet()
      font-size 1.5rem
      line-height 1.2

  h4, .h4
    font-size 1.5rem
    font-weight 600
    margin-top $h4-heading-margin-top
    margin-bottom 0.75rem
    line-height 1.4

    +redefine-tablet()
      font-size 1.4rem
      line-height 1.3

  h5, .h5
    font-size 1.28rem
    font-weight 600
    margin-top $h5-heading-margin-top
    margin-bottom 0.5rem
    line-height 1.3

    +redefine-tablet()
      font-size 1.18rem
      line-height 1.2

  h6, .h6
    font-size 1.2rem
    font-weight 600
    margin-top $h6-heading-margin-top
    margin-bottom 0.5rem
    line-height 1.2

    +redefine-tablet()
      font-size 1.1rem
      line-height 1.1


  img
    box-sizing border-box
    border-radius $usr-img-border-radius
    max-width 100%
    cursor zoom-in
    display block
    transition-t('padding, margin', '0, 0', '0.2, 0.2', 'linear, linear')

    if (hexo-config('articles.style.image_alignment') == 'center')
      margin 1rem auto 1rem
    else
      margin 1rem 0 1rem

  
  del.mask
    text-decoration none !important
    transition .2s cubic-bezier(.25, .46, .45, .94) color, .2s cubic-bezier(.25, .46, .45, .94) background

    * 
      transition .35s cubic-bezier(.25, .46, .45, .94) opacity

    &:not(:hover)
      color transparent !important
      background #000 !important

      * 
        opacity 0 !important

      &, *
        border none !important

  

  & > table,
  .tab-pane > table
    border-spacing 0
    border-collapse collapse
    width 100%
    overflow auto
    margin-bottom 1.2rem
    margin-top 1rem
    box-shadow var(--redefine-box-shadow-flat)
    border-radius $redefine-border-radius-medium

    thead
      th
        border-bottom 1px solid var(--shadow-color-1)
        background-color var(--third-background-color) !important

        &:first-child
          border-top-left-radius $redefine-border-radius-medium

        &:last-child
          border-top-right-radius $redefine-border-radius-medium

    tbody
      tr
        &:last-child
          td
            &:first-child
              border-bottom-left-radius $redefine-border-radius-medium

            &:last-child
              border-bottom-right-radius $redefine-border-radius-medium

    td, th
      padding 8px 16px
      // border 1px solid var(--border-color)
      border-left 1px solid var(--shadow-color-1)

      &:first-child
        border-left none

    tr
      background-color var(--background-color)
      // border 1px solid var(--fourth-text-color)

    tr:nth-child(2n)
      background-color var(--second-background-color)

  mjx-container
    display inline
    overflow-x auto
    white-space nowrap
    margin 0 0.2rem

    &:hover::-webkit-scrollbar-thumb
      border 3px solid rgba(183, 183, 183, 0.3)

    &::-webkit-scrollbar
      width 6px
      height 6px
      transition all 0.2s ease
      display block

    &::-webkit-scrollbar-track
      background transparent
      border none

    &::-webkit-scrollbar-thumb
      border-radius 20px
      border 3px solid rgba(183, 183, 183, 0)
      transition all 0.2s ease

    svg
      display inline-block /* Allow content to be centered properly */


  mjx-container[display="true"]
    display block
    justify-content center
    vertical-align baseline
    overflow-x auto
    text-align center
    white-space nowrap
    scrollbar-width: thin  // For Firefox, if needed
    padding-bottom 8px

    &:hover::-webkit-scrollbar-thumb
      border 3px solid rgba(183, 183, 183, 0.3)

    &::-webkit-scrollbar
      width 6px
      height 6px
      transition all 0.2s ease
      display block

    &::-webkit-scrollbar-track
      background transparent
      border none

    &::-webkit-scrollbar-thumb
      border-radius 20px
      border 3px solid rgba(183, 183, 183, 0)
      transition all 0.2s ease


    svg
      display inline-block
