name: Bug Report
description: Found a bug? Let us help you fix it!
title: "[BUG] "
labels: ["bug"]
assignees: ["EvanNotFound"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Fill out the details below to help us solve the issue quickly.

  - type: checkboxes
    id: pre-check
    attributes:
      label: Quick Checklist
      description: "Before submitting, please make sure you've done the following:"
      options:
        - label: "I've tried running `hexo clean` but the issue is still there"
          required: true
        - label: "[I'm using the latest version of the theme](https://redefine-docs.ohevan.com/getting-started#update)"
          required: true
        - label: "[I've updated my theme configuration after updating](https://redefine-docs.ohevan.com/getting-started#migrate-configuration)"
          required: true

  - type: textarea
    id: bug-description
    attributes:
      label: "What's the issue?"
      description: "Tell us what's happening. Please include your relevant config settings from `_config.yml` and `_config.redefine.yml` (remember to remove any sensitive info)."
    validations:
      required: true

  - type: textarea
    id: reproduce-steps
    attributes:
      label: "How can we reproduce this?"
      placeholder: "Walk us through the steps to see this bug in action"
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: "What should have happened?"
      placeholder: "Tell us what you expected to see"

  - type: textarea
    id: screenshots
    attributes:
      label: "Screenshots (if any)"
      description: "Screenshots can help illustrate the problem."

  - type: textarea
    id: logs
    attributes:
      label: "Related Logs"
      description: "Please copy and paste any relevant log output. This will be automatically formatted as code, so no backticks are needed."
      render: shell

  - type: dropdown
    id: pc-browser
    attributes:
      label: "What browser(s) are you using?"
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - Other

  - type: dropdown
    id: pc-os
    attributes:
      label: "What operating system are you on?"
      options:
        - Windows
        - macOS
        - Linux
        - iOS
        - Android
        - Other

  - type: input
    id: pc-version
    attributes:
      label: "Browser Version"
      placeholder: "e.g., Chrome 108.0.5359.124"

  - type: dropdown
    id: priority
    validations:
      required: true
    attributes:
      label: "Priority"
      description: "Please select the priority based on the severity of the issue."
      options:
        - Low
        - Medium
        - High

  - type: textarea
    id: additional-info
    attributes:
      label: "Anything else we should know?"
      placeholder: "Add any other helpful context about the problem here"
