export default function initLocalSearch(){let e=config.path;if(!e)return void console.warn("`hexo-generator-searchdb` plugin is not installed!");let t,n=!1,o=!0;0===e.length?e="search.xml":e.endsWith("json")&&(o=!1);const r=document.querySelector(".search-input"),l=document.getElementById("search-result"),getIndexByWord=(e,t,n)=>{let o=e.length;if(0===o)return[];let r=0,l=[],s=[];for(n||(t=t.toLowerCase(),e=e.toLowerCase());(l=t.indexOf(e,r))>-1;)s.push({position:l,word:e}),r=l+o;return s},mergeIntoSlice=(e,t,n,o)=>{let r=n[n.length-1],{position:l,word:s}=r,i=[],a=0;for(;l+s.length<=t&&0!==n.length;){s===o&&a++,i.push({position:l,length:s.length});const e=l+s.length;n.pop();for(let t=n.length-1;t>=0&&(r=n[t],l=r.position,s=r.word,!(e<=l));t--)n.pop()}return{hits:i,start:e,end:t,searchTextCount:a}},highlightKeyword=(e,t)=>{let n="",o=t.start;return t.hits.forEach((t=>{n+=e.substring(o,t.position);let r=t.position+t.length;n+=`<b class="search-keyword">${e.substring(t.position,r)}</b>`,o=r})),n+=e.substring(o,t.end),n},inputEventFunction=()=>{if(!n)return;let e=r.value.trim().toLowerCase(),o=e.split(/[-\s]+/);o.length>1&&o.push(e);let s=[];if(e.length>0&&t.forEach((({title:t,content:n,url:r})=>{let l=t.toLowerCase(),i=n.toLowerCase(),a=[],c=[],h=0;if(o.forEach((e=>{a=a.concat(getIndexByWord(e,l,!1)),c=c.concat(getIndexByWord(e,i,!1))})),a.length>0||c.length>0){let o=a.length+c.length;[a,c].forEach((e=>{e.sort(((e,t)=>t.position!==e.position?t.position-e.position:e.word.length-t.word.length))}));let l=[];if(0!==a.length){let n=mergeIntoSlice(0,t.length,a,e);h+=n.searchTextCountInSlice,l.push(n)}let i=[];for(;0!==c.length;){let t=c[c.length-1],{position:o,word:r}=t,l=o-20,s=o+80;l<0&&(l=0),s<o+r.length&&(s=o+r.length),s>n.length&&(s=n.length);let a=mergeIntoSlice(l,s,c,e);h+=a.searchTextCountInSlice,i.push(a)}i.sort(((e,t)=>e.searchTextCount!==t.searchTextCount?t.searchTextCount-e.searchTextCount:e.hits.length!==t.hits.length?t.hits.length-e.hits.length:e.start-t.start));let u=parseInt(theme.navbar.search.top_n_per_article?theme.navbar.search.top_n_per_article:1,10);u>=0&&(i=i.slice(0,u));let p="";0!==l.length?p+=`<li><a href="${r}" class="search-result-title">${highlightKeyword(t,l[0])}</a>`:p+=`<li><a href="${r}" class="search-result-title">${t}</a>`,i.forEach((e=>{p+=`<a href="${r}"><p class="search-result">${highlightKeyword(n,e)}...</p></a>`})),p+="</li>",s.push({item:p,id:s.length,hitCount:o,searchTextCount:h})}})),1===o.length&&""===o[0])l.innerHTML='<div id="no-result"><i class="fa-solid fa-magnifying-glass fa-5x"></i></div>';else if(0===s.length)l.innerHTML='<div id="no-result"><i class="fa-solid fa-box-open fa-5x"></i></div>';else{s.sort(((e,t)=>e.searchTextCount!==t.searchTextCount?t.searchTextCount-e.searchTextCount:e.hitCount!==t.hitCount?t.hitCount-e.hitCount:t.id-e.id));let e='<ul class="search-result-list">';s.forEach((t=>{e+=t.item})),e+="</ul>",l.innerHTML=e,window.pjax&&window.pjax.refresh(l)}},fetchData=()=>{fetch(config.root+e).then((e=>e.text())).then((e=>{n=!0,t=o?[...(new DOMParser).parseFromString(e,"text/xml").querySelectorAll("entry")].map((e=>({title:e.querySelector("title").textContent,content:e.querySelector("content").textContent,url:e.querySelector("url").textContent}))):JSON.parse(e),t=t.filter((e=>e.title)).map((e=>(e.title=e.title.trim(),e.content=e.content?e.content.trim().replace(/<[^>]+>/g,""):"",e.url=decodeURIComponent(e.url).replace(/\/{2,}/g,"/"),e)));const r=document.querySelector("#no-result");r&&(r.innerHTML='<i class="fa-solid fa-magnifying-glass fa-5x"></i>')}))};theme.navbar.search.preload&&fetchData(),r&&r.addEventListener("input",inputEventFunction),document.querySelectorAll(".search-popup-trigger").forEach((e=>{e.addEventListener("click",(()=>{document.body.style.overflow="hidden",document.querySelector(".search-pop-overlay").classList.add("active"),setTimeout((()=>r.focus()),500),n||fetchData()}))}));const onPopupClose=()=>{document.body.style.overflow="",document.querySelector(".search-pop-overlay").classList.remove("active")};document.querySelector(".search-pop-overlay").addEventListener("click",(e=>{e.target===document.querySelector(".search-pop-overlay")&&onPopupClose()})),document.querySelector(".search-input-field-pre").addEventListener("click",(()=>{r.value="",r.focus(),inputEventFunction()})),document.querySelector(".popup-btn-close").addEventListener("click",onPopupClose);try{swup.hooks.on("page:view",(e=>{onPopupClose()}))}catch(e){}window.addEventListener("keyup",(e=>{"Escape"===e.key&&onPopupClose()}))}
//# sourceMappingURL=localSearch.js.map