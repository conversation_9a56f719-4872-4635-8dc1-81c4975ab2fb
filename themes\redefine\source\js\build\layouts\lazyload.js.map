{"version": 3, "file": "lazyload.js", "names": ["initLazyLoad", "imgs", "document", "querySelectorAll", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "img", "target", "src", "getAttribute", "removeAttribute", "unobserve", "rootMargin", "threshold", "hasAttribute", "observe"], "sources": ["0"], "mappings": "eAAe,SAASA,eACtB,MAAMC,EAAOC,SAASC,iBAAiB,OAKjCC,EAAW,IAAIC,sBAAqB,CAACC,EAASF,KAClDE,EAAQC,SAASC,IACf,GAAIA,EAAMC,eAAgB,CACxB,MAAMC,EAAMF,EAAMG,OAClBD,EAAIE,IAAMF,EAAIG,aAAa,YAC3BH,EAAII,gBAAgB,YACpBV,EAASW,UAAUL,EACrB,IACA,GAZY,CACdM,WAAY,MACZC,UAAW,KAYbhB,EAAKM,SAASG,IACRA,EAAIQ,aAAa,aACnBd,EAASe,QAAQT,EACnB,GAEJ", "ignoreList": []}