$friend-link-item-height = 82px
$friend-link-item-height-tablet = 68px
$friend-link-item-interval = 16px
$friend-link-item-border-radius = 9px

@require '../../common/variables'

.page-template-container
  redefine-container(
    false,
    0,
    0,
    30px,
    30px
  )

  +redefine-mobile()
    padding 1.2rem 0.2rem
    box-shadow none

    &:hover
      box-shadow none

  .page-template-content
    color var(--default-text-color)

    h1, h2, h3, h4, h5, h6
      &:first-child
        margin 6px 0 36px 0
        border-bottom none
        line-height 1

  #shuoshuo-content
    img:hover
      cursor zoom-in


  #masonry-container
    position relative
    display none
    opacity 0
    transition opacity 0.1s ease-in

    .masonry-item
      position absolute
      box-sizing border-box

      &:hover
        img
          box-shadow var(--redefine-box-shadow)

      img
        width 100%
        border-radius $redefine-border-radius-medium
        box-shadow var(--redefine-box-shadow-flat)
        cursor zoom-in

  /* Loading */
  .loading-placeholder
    display block
    justify-content center
    align-items center
    opacity 1
    transition opacity 0.1s ease-out

  .loading
    -webkit-animation gradient-slide 1.1s ease infinite
    animation gradient-slide 1.1s ease infinite
    background 0 0 / 300% 300% linear-gradient(120deg, #9c9c9c20 40%, #83838325 50%, #9c9c9c20 60%)

  .flex-grid
    display grid
    grid-template-columns repeat(auto-fill, minmax(150px, 1fr))
    gap 10px

    @media screen and (min-width: 768px)
      grid-template-columns repeat(auto-fill, minmax(255px, 1fr))

  .card
    border 1px solid var(--border-color)
    border-radius $redefine-border-radius-medium
    box-sizing border-box
    min-height 80vh
    padding 30px

  @keyframes gradient-slide
    0%
      background-position 100% 50%

    to
      background-position 0% 50%

  /* Images */
  .image-container
    position relative

  .image-title
    position absolute
    top 5px
    left 5px
    color var(--default-text-color)
    background-color var(--background-color-transparent-40)
    padding 5px 10px
    font-size 14px
    opacity 0
    backdrop-filter blur(10px)
    -webkit-backdrop-filter blur(10px)
    border-radius $redefine-border-radius-small
    transition opacity 0.2s ease-out

  .image-description
    position absolute
    bottom 11px
    right 5px
    color var(--default-text-color)
    background-color var(--background-color-transparent-40)
    padding 5px 10px
    font-size 14px
    opacity 0
    max-width 80%
    backdrop-filter blur(10px)
    -webkit-backdrop-filter blur(10px)
    border-radius $redefine-border-radius-small
    transition opacity 0.2s ease-out

  figure.image-caption
    img
      margin-bottom 0

    figcaption
      margin-top 5px
      margin-bottom 10px
      text-align center
      font-size 0.9rem
      color var(--third-text-color)

  .image-container:hover .image-title, .image-container:hover .image-description
    opacity 1

  .image-container img
    width 100%
    height auto
    transition box-shadow 0.3s ease-in-out
    overflow hidden

  .image-container:hover img
    box-shadow var(--redefine-box-shadow-hover)
